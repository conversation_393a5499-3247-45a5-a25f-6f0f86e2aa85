# 🎮 Card Battle Game - WebSocket API Usage Guide

## 📋 Overview
Hướng dẫn sử dụng WebSocket API cho các chức năng matching trong game Card Battle.

## 🔧 Prerequisites
- Server: http://localhost:3000
- WebSocket URL: ws://localhost:3000/cable
- Redis: localhost:6379
- Test Users: user1, user2, user3, ... (tạo từ matching_test)

## 🏆 RANK MATCHING (ランクマッチ)

### 1. Kết nối WebSocket của User 1
```bash
# Sử dụng wscat để kết nối WebSocket
wscat -c "ws://localhost:3000/cable?client_type=user&token=user1"

# Hoặc sử dụng curl để test connection
curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  -H "Sec-WebSocket-Version: 13" \
  "http://localhost:3000/cable?client_type=user&token=user1"
```

### 2. <PERSON><PERSON>t nối WebSocket của User 2
```bash
wscat -c "ws://localhost:3000/cable?client_type=user&token=user2"
```

### 3. User 1 subscribe và bắt đầu tìm trận (rank matching)
```json
// Gửi message subscribe
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"RankMatchingChannel\"}"
}

// Server sẽ tự động đưa user vào queue khi subscribe
// User 1 sẽ nhận được message xác nhận
```

### 4. User 2 subscribe và bắt đầu tìm trận (rank matching)
```json
// Gửi message subscribe
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"RankMatchingChannel\"}"
}

// Server sẽ tự động match 2 users
```

### 5. Kết quả matching
```json
// User 1 nhận được:
{
  "identifier": "{\"channel\":\"RankMatchingChannel\"}",
  "message": {
    "command": "RankMatchingSuccess",
    "room_id": "user1",
    "index_in_room": 0,
    "timecount": 0
  }
}

// User 2 nhận được:
{
  "identifier": "{\"channel\":\"RankMatchingChannel\"}",
  "message": {
    "command": "RankMatchingSuccess",
    "room_id": "user1",
    "index_in_room": 1,
    "timecount": 0
  }
}
```

### 6. Curl commands để test Rank Matching
```bash
# Test với matching_test API
curl -X POST http://localhost:3000/matching_test/simulate_rank_matching \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')" \
  -d '{"user_ids": ["1", "2"]}'
```

## 🎯 FREE MATCHING (フリーマッチ)

### 1. Kết nối WebSocket của User 1
```bash
wscat -c "ws://localhost:3000/cable?client_type=user&token=user1"
```

### 2. Kết nối WebSocket của User 2
```bash
wscat -c "ws://localhost:3000/cable?client_type=user&token=user2"
```

### 3. User 1 subscribe Free Matching Channel
```json
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"FreeMatchingChannel\"}"
}
```

### 4. User 1 gửi message để bắt đầu tìm trận (free matching)
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"FreeMatchingChannel\"}",
  "data": "{\"action\":\"join_free_matching\"}"
}
```

### 5. User 2 subscribe và join Free Matching
```json
// Subscribe
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"FreeMatchingChannel\"}"
}

// Join matching
{
  "command": "message",
  "identifier": "{\"channel\":\"FreeMatchingChannel\"}",
  "data": "{\"action\":\"join_free_matching\"}"
}
```

### 6. Kết quả Free Matching
```json
// Cả 2 users sẽ nhận được:
{
  "identifier": "{\"channel\":\"FreeMatchingChannel\"}",
  "message": {
    "command": "FreeMatchingSuccess",
    "room_id": "user1",
    "index_in_room": 0, // hoặc 1 cho user2
    "timecount": 0
  }
}
```

### 7. Curl commands để test Free Matching
```bash
curl -X POST http://localhost:3000/matching_test/simulate_free_matching \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')" \
  -d '{"user_ids": ["3", "4"]}'
```

## 🏠 ROOM MATCHING (ルームマッチ)

### 1. Kết nối WebSocket của User 1
```bash
wscat -c "ws://localhost:3000/cable?client_type=user&token=user1"
```

### 2. Kết nối WebSocket của User 2
```bash
wscat -c "ws://localhost:3000/cable?client_type=user&token=user2"
```

### 3. User 1 subscribe Room Matching Channel
```json
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}"
}
```

### 4. User 1 gửi message để tạo phòng
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}",
  "data": "{\"action\":\"create_room\",\"text\":{\"player_id\":\"user1\",\"name\":\"Player 1\",\"level\":10,\"icon_id\":1,\"frame_id\":1,\"title_id\":1}}"
}
```

### 5. User 2 subscribe và join phòng
```json
// Subscribe
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user2\"}"
}

// Join room (cần room_id từ response của create_room)
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user2\"}",
  "data": "{\"action\":\"join_room\",\"text\":{\"room_id\":\"123456\",\"player_id\":\"user2\",\"name\":\"Player 2\",\"level\":15,\"icon_id\":2,\"frame_id\":2,\"title_id\":2}}"
}
```

### 6. User 1 gửi message để set ready
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}",
  "data": "{\"action\":\"ready\",\"text\":{\"ready\":true}}"
}
```

### 7. User 1 gửi message để set not ready
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}",
  "data": "{\"action\":\"ready\",\"text\":{\"ready\":false}}"
}
```

### 8. User 1 gửi message để set ready lại
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}",
  "data": "{\"action\":\"ready\",\"text\":{\"ready\":true}}"
}
```

### 9. User 2 gửi message để set ready
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user2\"}",
  "data": "{\"action\":\"ready\",\"text\":{\"ready\":true}}"
}
```

### 10. User 1 gửi message để bắt đầu trận đấu
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user1\"}",
  "data": "{\"action\":\"start_battle\",\"text\":{}}"
}
```

### 11. User 2 gửi message để bắt đầu trận đấu
```json
{
  "command": "message",
  "identifier": "{\"channel\":\"RoomMatchingChannel\",\"player_id\":\"user2\"}",
  "data": "{\"action\":\"start_battle\",\"text\":{}}"
}
```

### 12. Curl commands để test Room Matching
```bash
# Tạo room matching
curl -X POST http://localhost:3000/matching_test/simulate_room_matching \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')" \
  -d '{"user_ids": ["5", "6", "7", "8"]}'

# Toggle ready status
curl -X POST http://localhost:3000/matching_test/toggle_ready \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')" \
  -d '{"user_id": "5"}'

# Auto ready all rooms
curl -X POST http://localhost:3000/matching_test/auto_ready_rooms \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')"
```

## 🔌 UTILITY COMMANDS

### Disconnect Selected Users
```bash
curl -X POST http://localhost:3000/matching_test/disconnect_all_users \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')" \
  -d '{"user_ids": ["1", "2", "3"]}'
```

### Clear All Status
```bash
curl -X POST http://localhost:3000/matching_test/clear_all_status \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')"
```

### Test Broadcast
```bash
curl -X POST http://localhost:3000/matching_test/test_broadcast \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: $(curl -s http://localhost:3000/matching_test | grep csrf-token | sed 's/.*content="\([^"]*\)".*/\1/')"
```

## 📊 MONITORING ENDPOINTS

### View User Status (Admin)
```bash
# Main dashboard
curl http://localhost:3000/user_statuses

# Rank match status
curl http://localhost:3000/user_statuses/rank_match

# Free match status
curl http://localhost:3000/user_statuses/free_match

# Room match status
curl http://localhost:3000/user_statuses/room_match
```

### WebSocket Admin Monitoring
```bash
# Connect as admin to monitor all activities
wscat -c "ws://localhost:3000/cable?client_type=admin&token=admin-1"

# Subscribe to admin channels
{
  "command": "subscribe",
  "identifier": "{\"channel\":\"Admin::UserStatusChannel\"}"
}

{
  "command": "subscribe",
  "identifier": "{\"channel\":\"Admin::MatchStatusChannel\",\"match_type\":\"rank\"}"
}

{
  "command": "subscribe",
  "identifier": "{\"channel\":\"Admin::MatchStatusChannel\",\"match_type\":\"free\"}"
}

{
  "command": "subscribe",
  "identifier": "{\"channel\":\"Admin::MatchStatusChannel\",\"match_type\":\"room\"}"
}
```

## 🚨 ERROR CODES

### Rank Matching Errors
- ESBC-0001: Player already in queue
- ESBC-0002: Player not found
- ESBC-0003: Database error
- ESBC-9999: Internal server error

### Room Matching Errors
- CR0000: Failed to generate unique room ID
- CR0001: Failed to create room
- CR0002: Failed to create room (general)
- JR0001: Room not found in Redis
- JR0002: JSON parse error for room
- JR0003: Error creating player pack
- JR0004: Room no longer exists

## 📝 NOTES

1. **User IDs**: Sử dụng string format ("user1", "user2", etc.)
2. **Room IDs**: 6-digit random numbers được generate tự động
3. **WebSocket Connections**: Mỗi user cần connection riêng biệt
4. **Timeouts**: Connections có timeout, cần handle reconnection
5. **Redis Keys**: Tất cả data được store trong Redis với TTL
6. **Realtime Updates**: Admin dashboard sẽ update realtime khi có changes

## 🔧 DEVELOPMENT TOOLS

### Matching Test Interface
- URL: http://localhost:3000/matching_test?limit=30
- Features: Select users, test all matching types, monitor realtime

### User Status Dashboard
- URL: http://localhost:3000/user_statuses
- Features: Monitor all user activities, realtime stats, navigation

### WebSocket Debug
- Browser Console: Check ActionCable connections
- Rails Logs: Monitor channel activities và errors
- Redis CLI: Inspect stored data directly