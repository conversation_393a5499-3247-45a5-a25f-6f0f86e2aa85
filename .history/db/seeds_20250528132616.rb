# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

Lang.create(uid: 1, name: "日本語", locale: "ja", position: 1)
Lang.create(uid: 2, name: "英語", locale: "en", position: 2)
# ServerSetting.create(uid: 1, version_id: -1)
# 宝箱
chest_categories = ["wood","silver","gold","rainbow"]
chest_categories.each_with_index do |category,index|
    unless Chest.exists?(uid: index+1)
    Chest.create(uid: index+1,category:category,rewards:[
    {
        "ext": {
            "max": 40,
            "min": 10,
            "rate": 100,
            "use_range": true
        },
        "count": 100,
        "item_id": 0,
        "item_type": "dia"
    },
    {
        "ext": {
            "rate": 30
        },
        "count": 100,
        "item_id": 1,
        "item_type": "icon"
    },
    {
        "ext": {
            "rate": 10.5
        },
        "count": 100,
        "item_id": 3,
        "item_type": "title"
    }
        ])
    end
end

# ショップ関連
unless Supply.exists?(uid: 1)
    Supply.create(uid: 1, dia_cost: 100, category: "icon", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
        },
    ])
end

unless Supply.exists?(uid: 2)
    Supply.create(uid: 2, dia_cost: 100, category: "icon_frame", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon_frame",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon_frame",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
    ])
end

unless ShopBonus.exists?(uid: 1)
    ShopBonus.create(uid: 1, need_dia: 100, category: "charged_bonus", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "dia",
        "item_id": 0,
        "count": 100,
        "ext": {}
        }
    ])
end

unless ShopBonus.exists?(uid: 2)
    ShopBonus.create(uid: 2, need_dia: 100, category: "consumed_bonus", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
            "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
            "item_type": "dia",
            "item_id": 0,
            "count": 100,
            "ext": {}
        }
    ])
end

# ログインボーナス

3.times do |i|
    if i == 0
        is_event = false
    else
        is_event = true
    end
    unless LoginBonus.exists?(uid: i+1)
    LoginBonus.create(uid: i+1, title: {"ja"=>"ログインボーナス", "en"=>"Login Bonus"}, desc: {"ja"=>"ログインボーナス", "en"=>"Login Bonus"},
    start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", is_event: is_event, rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 3,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 4,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 5,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 6,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 7,
        "count": 1,
        "ext": {}
    },
])
    end
end

# バトルパス
unless BattlePass.exists?(uid: 1)
BattlePass.create(uid: 1, season: 1, start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", free_rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
], premium_rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
])
end

# ショップバンドル
2.times do |i|
    unless ShopBundle.exists?(uid: i+1)
    ShopBundle.create(uid: i+1, name: {"ja"=>"バンドル#{i+1}", "en"=>"Bundle #{i+1}"}, desc: {"ja"=>"バンドル#{i+1}", "en"=>"Bundle #{i+1}"},
    cost: 100, start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", max_count: 2, rewards: [
        {
            "item_type": "icon",
            "item_id": 1,
            "count": 1,
            "ext": {}
        },
        {
            "item_type": "icon",
            "item_id": 2,
            "count": 1,
            "ext": {}
        }
    ])
    end
end

# バナー
2.times do |i|
    unless Banner.exists?(uid: i+1)
    Banner.create(uid: i+1, banner_image_url: "https://example.com/banner.png", title: " テストバナー",
    start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", view_type: "info", view_uid: 3)
    end
end

# ギフト
2.times do |i|
    unless GiftMaster.exists?(uid: i+1)
        GiftMaster.create(uid: i+1, name: {"ja"=>"テストギフト", "en"=>"Test Gift"}, desc: {"ja"=>"テストギフト", "en"=>"Test Gift"},duration: 30,
        start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59",
        rewards: [
            {
                "item_type": "icon",
                "item_id": 1,
                "count": 1,
                "ext": {}
            },
            {
                "item_type": "icon",
                "item_id": 2,
                "count": 1,
                "ext": {}
            }
        ])
    end
end

# パック
unless Pack.exists?(uid: 1)
    Pack.create(
        id: 1,
        uid: 1,
        name: {"en"=>"", "ja"=>"テスト"},
        desc: {"en"=>"", "ja"=>"テストパック"},
        card_data:
        [[], [{"id"=>30006, "rarity"=>1, "need_dia"=>100, "need_pts"=>10, "probability"=>64.0}], [{"id"=>10006, "rarity"=>2, "need_dia"=>100, "need_pts"=>10, "probability"=>30.0}], [{"id"=>30007, "rarity"=>3, "need_dia"=>100, "need_pts"=>10, "probability"=>5.0}], [{"id"=>90003, "rarity"=>4, "need_dia"=>100, "need_pts"=>10, "probability"=>1.0}]],
        start_at: "2025-04-12 16:22:00.000000000 +0900",
        end_at: "2026-04-12 16:22:00.000000000 +0900",
        period_id: -1,
        cards_per_pack: 5,
        dia_cost: 10,
        skin_data: [{"id"=>1}, {"id"=>2}, {"id"=>3}, {"id"=>4}],
        per_skin_prob: 0.3e2
    )
end

# パックの確率
# unless PackRarityProbability.exists?(uid: 1)
#     PackRarityProbability.create(uid: 1, probability: 0.64e2,rarity: 1,pack_id: 1)
# end
# unless PackRarityProbability.exists?(uid: 2)
#     PackRarityProbability.create(uid: 2, probability: 0.3e2,rarity: 2,pack_id: 1)
# end
# unless PackRarityProbability.exists?(uid: 3)
#     PackRarityProbability.create(uid: 3, probability: 0.5e1,rarity: 3,pack_id: 1)
# end
# unless PackRarityProbability.exists?(uid: 4)
#     PackRarityProbability.create(uid: 4, probability: 0.1e1,rarity: 4,pack_id: 1)
# end

puts "Creating test users for WebSocket Load Test..."

# Tạo user test với rank đa dạng để test matching
test_users_data = [
  # Beginner rank users (500-999)
  { name: "Beginner Player 1", rate: 500 },
  { name: "Beginner Player 2", rate: 650 },
  { name: "Beginner Player 3", rate: 750 },
  { name: "Beginner Player 4", rate: 850 },
  { name: "Beginner Player 5", rate: 950 },

  # Intermediate rank users (1000-1499)
  { name: "Intermediate Player 1", rate: 1000 },
  { name: "Intermediate Player 2", rate: 1150 },
  { name: "Intermediate Player 3", rate: 1250 },
  { name: "Intermediate Player 4", rate: 1350 },
  { name: "Intermediate Player 5", rate: 1450 },

  # Advanced rank users (1500-1999)
  { name: "Advanced Player 1", rate: 1500 },
  { name: "Advanced Player 2", rate: 1650 },
  { name: "Advanced Player 3", rate: 1750 },
  { name: "Advanced Player 4", rate: 1850 },
  { name: "Advanced Player 5", rate: 1950 },

  # Expert rank users (2000+)
  { name: "Expert Player 1", rate: 2000 },
  { name: "Expert Player 2", rate: 2200 },
  { name: "Expert Player 3", rate: 2500 },
  { name: "Expert Player 4", rate: 2800 },
  { name: "Expert Player 5", rate: 3000 },

  # Same rank users for easy matching
  { name: "Same Rank A1", rate: 1200 },
  { name: "Same Rank A2", rate: 1200 },
  { name: "Same Rank A3", rate: 1200 },
  { name: "Same Rank B1", rate: 1800 },
  { name: "Same Rank B2", rate: 1800 },
  { name: "Same Rank B3", rate: 1800 },
]

test_users_data.each_with_index do |user_data, i|
  user_id = i + 1
  user_open_id = "test_user_#{user_id}"

  unless User.exists?(open_id: user_open_id)
    begin
      user = User.new(
        open_id: user_open_id,
        name: user_data[:name],
        password: "password123",
        password_confirmation: "password123",
        free_dia: 1000,
        ios_dia: 0,
        android_dia: 0,
        steam_dia: 0,
        exp: 0,
        rate: user_data[:rate],
        wins: rand(0..50),
        last_login_at: Time.current,
        tutorial_status: "COMPLETED"
      )

      user.save(validate: false)
      puts "Created test user: #{user_data[:name]} (Rate: #{user_data[:rate]})"
    rescue => e
      puts "\nError creating user #{user_id}: #{e.message}"
    end
  end
end

# Tạo thêm 974 user random để đủ 1000 user
(test_users_data.length + 1..1000).each do |i|
  user_open_id = "test_user_#{i}"

  unless User.exists?(open_id: user_open_id)
    begin
      user = User.new(
        open_id: user_open_id,
        name: "Random User #{i}",
        password: "password123",
        password_confirmation: "password123",
        free_dia: 1000,
        ios_dia: 0,
        android_dia: 0,
        steam_dia: 0,
        exp: 0,
        rate: rand(500..3000),
        wins: rand(0..100),
        last_login_at: Time.current,
        tutorial_status: "COMPLETED"
      )

      user.save(validate: false)
      print "." if i % 100 == 0
    rescue => e
      puts "\nError creating user #{i}: #{e.message}"
    end
  end
end
puts "\nTest users created!"

if defined?(WebsocketTest) && WebsocketTest.table_exists?
  puts "Creating sample WebSocket tests..."

  begin
    WebsocketTest.create!(
      test_type: "conn_test",
      channel_type: "battle",
      connection_count: 50,
      duration: 30,
      step: 10,
      interval: 1000,
      max_cpu: 80,
      status: "completed",
      started_at: 2.days.ago,
      completed_at: 2.days.ago + 30.seconds,
      results: {
        connections: [
          { timestamp: 2.days.ago.to_i, count: 0 },
          { timestamp: (2.days.ago + 5.seconds).to_i, count: 10 },
          { timestamp: (2.days.ago + 10.seconds).to_i, count: 20 },
          { timestamp: (2.days.ago + 15.seconds).to_i, count: 30 },
          { timestamp: (2.days.ago + 20.seconds).to_i, count: 40 },
          { timestamp: (2.days.ago + 25.seconds).to_i, count: 50 }
        ],
        messages: [],
        errors: [],
        cpu: [
          { timestamp: 2.days.ago.to_i, usage: 20.5 },
          { timestamp: (2.days.ago + 15.seconds).to_i, usage: 35.2 },
          { timestamp: (2.days.ago + 30.seconds).to_i, usage: 42.8 }
        ],
        memory: [
          { timestamp: 2.days.ago.to_i, usage: 512.3 },
          { timestamp: (2.days.ago + 15.seconds).to_i, usage: 645.7 },
          { timestamp: (2.days.ago + 30.seconds).to_i, usage: 712.4 }
        ],
        latency: []
      }
    )
    puts "Created connection test sample"
  rescue => e
    puts "Error creating connection test: #{e.message}"
  end

  begin
    WebsocketTest.create!(
      test_type: "msg_test",
      channel_type: "rank_matching",
      connection_count: 100,
      duration: 60,
      step: 20,
      interval: 2000,
      max_cpu: 80,
      status: "completed",
      started_at: 1.day.ago,
      completed_at: 1.day.ago + 1.minute,
      results: {
        connections: [
          { timestamp: 1.day.ago.to_i, count: 0 },
          { timestamp: (1.day.ago + 10.seconds).to_i, count: 20 },
          { timestamp: (1.day.ago + 20.seconds).to_i, count: 40 },
          { timestamp: (1.day.ago + 30.seconds).to_i, count: 60 },
          { timestamp: (1.day.ago + 40.seconds).to_i, count: 80 },
          { timestamp: (1.day.ago + 50.seconds).to_i, count: 100 }
        ],
        messages: [
          { timestamp: (1.day.ago + 10.seconds).to_i, count: 50 },
          { timestamp: (1.day.ago + 20.seconds).to_i, count: 150 },
          { timestamp: (1.day.ago + 30.seconds).to_i, count: 300 },
          { timestamp: (1.day.ago + 40.seconds).to_i, count: 500 },
          { timestamp: (1.day.ago + 50.seconds).to_i, count: 750 },
          { timestamp: (1.day.ago + 60.seconds).to_i, count: 1000 }
        ],
        errors: [
          { timestamp: (1.day.ago + 25.seconds).to_i, user_id: 42, message: "Connection reset" },
          { timestamp: (1.day.ago + 40.seconds).to_i, user_id: 67, message: "Timeout error" }
        ],
        cpu: [
          { timestamp: 1.day.ago.to_i, usage: 25.5 },
          { timestamp: (1.day.ago + 20.seconds).to_i, usage: 45.2 },
          { timestamp: (1.day.ago + 40.seconds).to_i, usage: 62.8 },
          { timestamp: (1.day.ago + 60.seconds).to_i, usage: 58.4 }
        ],
        memory: [
          { timestamp: 1.day.ago.to_i, usage: 620.3 },
          { timestamp: (1.day.ago + 20.seconds).to_i, usage: 745.7 },
          { timestamp: (1.day.ago + 40.seconds).to_i, usage: 812.4 },
          { timestamp: (1.day.ago + 60.seconds).to_i, usage: 834.1 }
        ],
        latency: [
          { timestamp: (1.day.ago + 15.seconds).to_i, user_id: 12, latency: 0.045 },
          { timestamp: (1.day.ago + 25.seconds).to_i, user_id: 24, latency: 0.062 },
          { timestamp: (1.day.ago + 35.seconds).to_i, user_id: 36, latency: 0.078 },
          { timestamp: (1.day.ago + 45.seconds).to_i, user_id: 48, latency: 0.095 },
          { timestamp: (1.day.ago + 55.seconds).to_i, user_id: 60, latency: 0.112 }
        ]
      }
    )
    puts "Created message test sample"
  rescue => e
    puts "Error creating message test: #{e.message}"
  end

  puts "Sample WebSocket tests created!"
else
  puts "WebsocketTest model not found or table doesn't exist. Skipping test creation."
end