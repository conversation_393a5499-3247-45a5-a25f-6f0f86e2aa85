# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Example:
#
#   ["Action", "Comedy", "Drama", "Horror"].each do |genre_name|
#     MovieGenre.find_or_create_by!(name: genre_name)
#   end

Lang.create(uid: 1, name: "日本語", locale: "ja", position: 1)
Lang.create(uid: 2, name: "英語", locale: "en", position: 2)
# ServerSetting.create(uid: 1, version_id: -1)
# 宝箱
chest_categories = ["wood","silver","gold","rainbow"]
chest_categories.each_with_index do |category,index|
    unless Chest.exists?(uid: index+1)
    Chest.create(uid: index+1,category:category,rewards:[
    {
        "ext": {
            "max": 40,
            "min": 10,
            "rate": 100,
            "use_range": true
        },
        "count": 100,
        "item_id": 0,
        "item_type": "dia"
    },
    {
        "ext": {
            "rate": 30
        },
        "count": 100,
        "item_id": 1,
        "item_type": "icon"
    },
    {
        "ext": {
            "rate": 10.5
        },
        "count": 100,
        "item_id": 3,
        "item_type": "title"
    }
        ])
    end
end

# ショップ関連
unless Supply.exists?(uid: 1)
    Supply.create(uid: 1, dia_cost: 100, category: "icon", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
        },
    ])
end

unless Supply.exists?(uid: 2)
    Supply.create(uid: 2, dia_cost: 100, category: "icon_frame", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon_frame",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon_frame",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
    ])
end

unless ShopBonus.exists?(uid: 1)
    ShopBonus.create(uid: 1, need_dia: 100, category: "charged_bonus", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "dia",
        "item_id": 0,
        "count": 100,
        "ext": {}
        }
    ])
end

unless ShopBonus.exists?(uid: 2)
    ShopBonus.create(uid: 2, need_dia: 100, category: "consumed_bonus", start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", rewards: [
        {
            "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
            "item_type": "dia",
            "item_id": 0,
            "count": 100,
            "ext": {}
        }
    ])
end

# ログインボーナス

3.times do |i|
    if i == 0
        is_event = false
    else
        is_event = true
    end
    unless LoginBonus.exists?(uid: i+1)
    LoginBonus.create(uid: i+1, title: {"ja"=>"ログインボーナス", "en"=>"Login Bonus"}, desc: {"ja"=>"ログインボーナス", "en"=>"Login Bonus"},
    start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", is_event: is_event, rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 3,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 4,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 5,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 6,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 7,
        "count": 1,
        "ext": {}
    },
])
    end
end

# バトルパス
unless BattlePass.exists?(uid: 1)
BattlePass.create(uid: 1, season: 1, start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", free_rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
], premium_rewards: [
    {
        "item_type": "icon",
        "item_id": 1,
        "count": 1,
        "ext": {}
    },
    {
        "item_type": "icon",
        "item_id": 2,
        "count": 1,
        "ext": {}
    }
])
end

# ショップバンドル
2.times do |i|
    unless ShopBundle.exists?(uid: i+1)
    ShopBundle.create(uid: i+1, name: {"ja"=>"バンドル#{i+1}", "en"=>"Bundle #{i+1}"}, desc: {"ja"=>"バンドル#{i+1}", "en"=>"Bundle #{i+1}"},
    cost: 100, start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", max_count: 2, rewards: [
        {
            "item_type": "icon",
            "item_id": 1,
            "count": 1,
            "ext": {}
        },
        {
            "item_type": "icon",
            "item_id": 2,
            "count": 1,
            "ext": {}
        }
    ])
    end
end

# バナー
2.times do |i|
    unless Banner.exists?(uid: i+1)
    Banner.create(uid: i+1, banner_image_url: "https://example.com/banner.png", title: " テストバナー",
    start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59", view_type: "info", view_uid: 3)
    end
end

# ギフト
2.times do |i|
    unless GiftMaster.exists?(uid: i+1)
        GiftMaster.create(uid: i+1, name: {"ja"=>"テストギフト", "en"=>"Test Gift"}, desc: {"ja"=>"テストギフト", "en"=>"Test Gift"},duration: 30,
        start_at: "2025-03-31 00:00:00", end_at: "2026-04-06 23:59:59",
        rewards: [
            {
                "item_type": "icon",
                "item_id": 1,
                "count": 1,
                "ext": {}
            },
            {
                "item_type": "icon",
                "item_id": 2,
                "count": 1,
                "ext": {}
            }
        ])
    end
end

# パック
unless Pack.exists?(uid: 1)
    Pack.create(
        id: 1,
        uid: 1,
        name: {"en"=>"", "ja"=>"テスト"},
        desc: {"en"=>"", "ja"=>"テストパック"},
        card_data:
        [[], [{"id"=>30006, "rarity"=>1, "need_dia"=>100, "need_pts"=>10, "probability"=>64.0}], [{"id"=>10006, "rarity"=>2, "need_dia"=>100, "need_pts"=>10, "probability"=>30.0}], [{"id"=>30007, "rarity"=>3, "need_dia"=>100, "need_pts"=>10, "probability"=>5.0}], [{"id"=>90003, "rarity"=>4, "need_dia"=>100, "need_pts"=>10, "probability"=>1.0}]],
        start_at: "2025-04-12 16:22:00.000000000 +0900",
        end_at: "2026-04-12 16:22:00.000000000 +0900",
        period_id: -1,
        cards_per_pack: 5,
        dia_cost: 10,
        skin_data: [{"id"=>1}, {"id"=>2}, {"id"=>3}, {"id"=>4}],
        per_skin_prob: 0.3e2
    )
end

# パックの確率
unless PackRarityProbability.exists?(uid: 1)
    PackRarityProbability.create(uid: 1, probability: 0.64e2,rarity: 1,pack_id: 1)
end
unless PackRarityProbability.exists?(uid: 2)
    PackRarityProbability.create(uid: 2, probability: 0.3e2,rarity: 2,pack_id: 1)
end
unless PackRarityProbability.exists?(uid: 3)
    PackRarityProbability.create(uid: 3, probability: 0.5e1,rarity: 3,pack_id: 1)
end
unless PackRarityProbability.exists?(uid: 4)
    PackRarityProbability.create(uid: 4, probability: 0.1e1,rarity: 4,pack_id: 1)
end