# config/environments/development.rb
require "active_support/core_ext/integer/time"

Rails.application.configure do
  # Settings specified here will take precedence over those in config/application.rb.

  # In the development environment your application's code is reloaded any time
  # it changes. This slows down response time but is perfect for development
  # since you don't have to restart the web server when you make code changes.
  config.enable_reloading = true

  # Do not eager load code on boot.
  config.eager_load = false

  # Show full error reports.
  config.consider_all_requests_local = true

  # Enable server timing.
  config.server_timing = true

  # Enable/disable caching. By default caching is disabled.
  # Run rails dev:cache to toggle caching.
  if Rails.root.join("tmp/caching-dev.txt").exist?
    config.action_controller.perform_caching = true
    config.action_controller.enable_fragment_cache_logging = true

    config.cache_store = :memory_store
    config.public_file_server.headers = { "Cache-Control" => "public, max-age=#{2.days.to_i}" }
  else
    config.action_controller.perform_caching = false

    config.cache_store = :null_store
  end

  # Store uploaded files on the local file system (see config/storage.yml for options).
  config.active_storage.service = :local

  # Don't care if the mailer can't send.
  config.action_mailer.raise_delivery_errors = false

  # Disable caching for Action Mailer templates even if Action Controller
  # caching is enabled.
  config.action_mailer.perform_caching = false

  config.action_mailer.default_url_options = { host: "localhost", port: 3000 }

  # Print deprecation notices to the Rails logger.
  config.active_support.deprecation = :log

  # Raise exceptions for disallowed deprecations.
  config.active_support.disallowed_deprecation = :raise

  # Tell Active Support which deprecation messages to disallow.
  config.active_support.disallowed_deprecation_warnings = []

  # Raise an error on page load if there are pending migrations.
  config.active_record.migration_error = :page_load

  # Highlight code that triggered database queries in logs.
  config.active_record.verbose_query_logs = true

  # Highlight code that enqueued background job in logs.
  config.active_job.verbose_enqueue_logs = true

  # Suppress logger output for asset requests.
  config.assets.quiet = true

  # Raises error for missing translations.
  # config.i18n.raise_on_missing_translations = true

  # Annotate rendered view with file names.
  config.action_view.annotate_rendered_view_with_filenames = true

  # Uncomment if you wish to allow Action Cable access from any origin.
  # config.action_cable.disable_request_forgery_protection = true

  # Raise error when a before_action's only/except options reference missing actions.
  config.action_controller.raise_on_missing_callback_actions = true

  # Apply autocorrection by RuboCop to files generated by `bin/rails generate`.
  # config.generators.apply_rubocop_autocorrect_after_generate!


  config.hosts << "neconome-dev-server.com"

  config.action_cable.disable_request_forgery_protection = true

  config.action_view.annotate_rendered_view_with_filenames = false # テンプレートコメントの出力をオフにする

#   config.after_initialize do
#    require "logger"

#    # 色付きログ出力
#    def colorize(severity, text)
#      case severity
#      when "DEBUG" then "\e[34m#{text}\e[0m" # 青
#      when "INFO" then "\e[32m#{text}\e[0m" # 緑
#      when "WARN" then "\e[33m#{text}\e[0m" # 黄
#      when "ERROR" then "\e[31m#{text}\e[0m" # 赤
#      when "FATAL" then "\e[35m#{text}\e[0m" # 紫
#      else text
#      end
#    end

#    # ✅ custom_formatter をちゃんと変数に代入
#    custom_formatter = proc do |severity, datetime, progname, msg|
#      time_str = datetime.strftime("%Y-%m-%d %H:%M:%S")

#      formatted =
#        if msg.is_a?(Hash)
#          keyvals = msg.map { |k, v| "[#{k}=#{v.inspect}]" }.join(" ")
#          "[#{severity}] #{keyvals}"
#        else
#          "#{time_str} #{severity} #{msg}"
#        end

#      "#{colorize(severity, formatted)}\n"
#    end

#    # Rails全体のロガー（DEBUGまで出す）
#    main_logger = ActiveSupport::Logger.new(STDOUT)
#    main_logger.level = Logger::DEBUG
#    main_logger.formatter = custom_formatter
#    Rails.logger = main_logger

#    # ActionCable専用ロガー（INFO以上のみ）
#    cable_logger = ActiveSupport::Logger.new(STDOUT)
#    cable_logger.level = Logger::INFO
#    cable_logger.formatter = custom_formatter
#    config.action_cable.logger = cable_logger
#  end


  # if Rails.env.development?
  #   BetterErrors::Middleware.allow_ip! "0.0.0.0/0"
  # end
end
