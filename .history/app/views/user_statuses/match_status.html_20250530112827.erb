<div class="container-fluid">
  <h1 class="mb-4"><%= params[:action] == 'rank_match' ? 'ランクマッチ状況' : 'フリーマッチ状況' %> <span id="connection-status" class="badge bg-warning">接続中...</span></h1>

  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">総プレイヤー数</div>
          <div class="font-bold h4" data-stat="総プレイヤー数"><%= @stats[:total] %></div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">マッチング中</div>
          <div class="font-bold h4" data-stat="マッチング中"><%= @stats[:matching] %></div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">対戦中</div>
          <div class="font-bold h4" data-stat="対戦中"><%= @stats[:matched] %></div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          マッチング中のプレイヤー
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped table-sm mb-0" id="matching-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名前</th>
                  <th>レート</th>
                  <th>更新時間</th>
                </tr>
              </thead>
              <tbody>
                <% @matching_users.each do |id, data| %>
                  <tr>
                    <td><%= id %></td>
                    <td><%= data[:name] %></td>
                    <td><%= data[:rate] %></td>
                    <td class="time-ago" data-timestamp="<%= data[:updated_at].to_i * 1000 %>">
                      <%= time_ago_in_words(data[:updated_at]) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          マッチング済みのプレイヤー
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped table-sm mb-0" id="matched-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名前</th>
                  <th>レート</th>
                  <th>ルームID</th>
                  <th>更新時間</th>
                </tr>
              </thead>
              <tbody>
                <% @matched_users.each do |id, data| %>
                  <tr>
                    <td><%= id %></td>
                    <td><%= data[:name] %></td>
                    <td><%= data[:rate] %></td>
                    <td><%= data[:metadata]&.dig(:room_id) || '-' %></td>
                    <td class="time-ago" data-timestamp="<%= data[:updated_at].to_i * 1000 %>">
                      <%= time_ago_in_words(data[:updated_at]) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="mt-4">
    <a href="<%= user_statuses_path %>" class="btn btn-secondary">戻る</a>
    <a href="<%= rank_match_user_statuses_path %>" class="btn <%= params[:action] == 'rank_match' ? 'btn-primary' : 'btn-outline-primary' %>">ランクマッチ</a>
    <a href="<%= free_match_user_statuses_path %>" class="btn <%= params[:action] == 'free_match' ? 'btn-primary' : 'btn-outline-primary' %>">フリーマッチ</a>
    <a href="<%= room_match_user_statuses_path %>" class="btn btn-outline-primary">ルームマッチ</a>
  </div>
</div>

<script>
  var matchStatusSubscription = null;
  var timeUpdateInterval = null;
  var lastReceivedData = null;
  var matchType = '<%= params[:action] == 'rank_match' ? 'rank' : 'free' %>';

  // Set current master user ID for WebSocket manager
  window.currentMasterUserId = <%= current_master_user&.id || 'null' %>;

  function setupWebSocketConnection() {
    console.log("Setting up WebSocket connection for " + matchType + " match status...");

    // Check if UserStatusWebSocket is available
    if (typeof window.UserStatusWebSocket === 'undefined') {
      console.log("UserStatusWebSocket not available, using fallback connection");
      setupFallbackConnection();
      return;
    }

    // Use the shared WebSocket manager
    const callbacks = {
      onConnectionChange: function(status) {
        console.log('WebSocket connection status:', status);
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
          if (status === 'connected') {
            statusElement.textContent = '接続済み';
            statusElement.className = 'badge bg-success';
          } else {
            statusElement.textContent = '接続中...';
            statusElement.className = 'badge bg-warning';
          }
        }
      }
    };

    // Set the appropriate callback based on match type
    if (matchType === 'rank') {
      callbacks.onRankData = function(data) {
        console.log("Received rank data:", data);
        lastReceivedData = data;
        updateUI(data);
        setupTimeUpdater();
      };
    } else if (matchType === 'free') {
      callbacks.onFreeData = function(data) {
        console.log("Received free data:", data);
        lastReceivedData = data;
        updateUI(data);
        setupTimeUpdater();
      };
    }

    window.UserStatusWebSocket.connect('match_status_' + matchType, callbacks);
  }

  function setupFallbackConnection() {
    if (matchStatusSubscription) {
      console.log("Cleaning up existing WebSocket connection...");
      matchStatusSubscription.unsubscribe();
      matchStatusSubscription = null;
    }

    console.log("Setting up fallback WebSocket connection for " + matchType + " match status...");

    const adminId = <%= current_master_user&.id || 'null' %>;

    let wsUrl = '/cable?client_type=admin';
    if (adminId) {
      wsUrl += '&token=admin-' + adminId;
    }

    console.log("Connecting to WebSocket at: " + wsUrl);
    const cable = ActionCable.createConsumer(wsUrl);

    matchStatusSubscription = cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: matchType },
      {
        connected() {
          console.log("Connected to Admin::MatchStatusChannel for " + matchType + " match!");
          document.getElementById('connection-status').textContent = '接続済み';
          document.getElementById('connection-status').className = 'badge bg-success';
        },

        disconnected() {
          console.log("Disconnected from Admin::MatchStatusChannel");
          document.getElementById('connection-status').textContent = '切断';
          document.getElementById('connection-status').className = 'badge bg-danger';
        },

        received(data) {
          console.log("Received data:", data);
          lastReceivedData = data;
          updateUI(data);
          setupTimeUpdater();
        }
      }
    );
  }

  function updateUI(data) {
    console.log("updateUI called with data:", data);

    if (data.stats) {
      const totalElement = document.querySelector('[data-stat="総プレイヤー数"]');
      const matchingElement = document.querySelector('[data-stat="マッチング中"]');
      const matchedElement = document.querySelector('[data-stat="対戦中"]');

      if (totalElement) totalElement.textContent = data.stats.total || 0;
      if (matchingElement) matchingElement.textContent = data.stats.matching || 0;
      if (matchedElement) matchedElement.textContent = data.stats.matched || 0;
    }

    if (data.matching_users) {
      updateTable('matching-table', data.matching_users);
    }

    if (data.matched_users) {
      updateTable('matched-table', data.matched_users);
    }

    setupTimeUpdater();
  }

  function updateTable(tableId, users) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';

    if (Object.keys(users).length === 0) {
      const tr = document.createElement('tr');
      const td = document.createElement('td');
      td.colSpan = tableId === 'matching-table' ? 4 : 5;
      td.textContent = 'データがありません';
      td.className = 'text-center p-3';
      tr.appendChild(td);
      tbody.appendChild(tr);
      return;
    }

    Object.entries(users).forEach(([id, data]) => {
      const tr = document.createElement('tr');

      const idTd = document.createElement('td');
      idTd.textContent = id;
      tr.appendChild(idTd);

      const nameTd = document.createElement('td');
      nameTd.textContent = data.name || 'Unknown';
      tr.appendChild(nameTd);

      const rateTd = document.createElement('td');
      rateTd.textContent = data.rate || 0;
      tr.appendChild(rateTd);

      if (tableId === 'matched-table') {
        const roomIdTd = document.createElement('td');
        roomIdTd.textContent = data.metadata?.room_id || '-';
        tr.appendChild(roomIdTd);
      }

      const timeTd = document.createElement('td');
      timeTd.dataset.timestamp = new Date(data.updated_at).getTime();
      timeTd.className = 'time-ago';
      updateTimeAgo(timeTd);
      tr.appendChild(timeTd);

      tbody.appendChild(tr);
    });
  }

  function updateTimeAgo(element) {
    const timestamp = parseInt(element.dataset.timestamp);
    if (!timestamp) return;

    const now = new Date();
    const diffInSeconds = Math.floor((now - timestamp) / 1000);

    if (diffInSeconds < 60) {
      element.textContent = `${diffInSeconds}秒前`;
      element.style.color = '#28a745';
    } else if (diffInSeconds < 3600) {
      element.textContent = `${Math.floor(diffInSeconds / 60)}分前`;
      element.style.color = '#17a2b8';
    } else {
      element.textContent = `${Math.floor(diffInSeconds / 3600)}時間前`;
      element.style.color = '#6c757d';
    }
  }

  function updateAllTimeAgo() {
    const timeElements = document.querySelectorAll('.time-ago');
    timeElements.forEach(updateTimeAgo);
  }

  function setupTimeUpdater() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
    }

    timeUpdateInterval = setInterval(updateAllTimeAgo, 10000);
  }

  function cleanupOnPageLeave() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
      timeUpdateInterval = null;
    }
    // Don't disconnect WebSocket here - let UserStatusWebSocket manager handle it
  }

  document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event detected for " + matchType + " match view");
    setupWebSocketConnection();
    updateAllTimeAgo();
    setupTimeUpdater();

    window.addEventListener('pageshow', function(event) {
      if (event.persisted) {
        console.log("Page loaded from cache, reconnecting WebSocket");
        setupWebSocketConnection();
        updateAllTimeAgo();
        setupTimeUpdater();
      }
    });

    window.addEventListener('beforeunload', function() {
      console.log("Page unload event detected");
      cleanupOnPageLeave();
    });
  });
</script>
<style>
  .card {
    height: 100%;
  }

  .table-sm td, .table-sm th {
    padding: 0.4rem 0.5rem;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  .card-header {
    font-size: 1.1rem;
    font-weight: 600;
  }
</style>
