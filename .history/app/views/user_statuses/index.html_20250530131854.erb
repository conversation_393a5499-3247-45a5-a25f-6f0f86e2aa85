<div class="container-fluid">
  <h1 class="mb-4">ランクマッチ状況 <span id="connection-status" class="badge bg-warning">接続中...</span></h1>

  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">総プレイヤー数</div>
          <div class="font-bold h4" data-stat="総プレイヤー数"><%= @stats[:total] %></div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">マッチング中</div>
          <div class="font-bold h4" data-stat="マッチング中"><%= @stats[:by_status]['matching'] || 0 %></div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">対戦中</div>
          <div class="font-bold h4" data-stat="対戦中"><%= @stats[:by_status]['matched'] || 0 %></div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">ルーム内</div>
          <div class="font-bold h4" data-stat="ルーム内"><%= @stats[:by_status]['in_room'] || 0 %></div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          マッチング中のプレイヤー
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped table-sm mb-0" id="matching-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名前</th>
                  <th>レート</th>
                  <th>更新時間</th>
                </tr>
              </thead>
              <tbody>
                <% @matching_users.each do |id, data| %>
                  <tr>
                    <td><%= id %></td>
                    <td><%= data[:name] %></td>
                    <td><%= data[:rate] %></td>
                    <td class="time-ago" data-timestamp="<%= data[:updated_at].to_i * 1000 %>">
                      <%= time_ago_in_words(data[:updated_at]) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          マッチング済みのプレイヤー
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped table-sm mb-0" id="matched-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名前</th>
                  <th>レート</th>
                  <th>ルームID</th>
                  <th>更新時間</th>
                </tr>
              </thead>
              <tbody>
                <% @matched_users.each do |id, data| %>
                  <tr>
                    <td><%= id %></td>
                    <td><%= data[:name] %></td>
                    <td><%= data[:rate] %></td>
                    <td><%= data[:metadata]&.dig(:room_id) || '-' %></td>
                    <td class="time-ago" data-timestamp="<%= data[:updated_at].to_i * 1000 %>">
                      <%= time_ago_in_words(data[:updated_at]) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          ルーム内のプレイヤー
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-striped table-sm mb-0" id="in-room-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名前</th>
                  <th>レート</th>
                  <th>ルームID</th>
                  <th>更新時間</th>
                </tr>
              </thead>
              <tbody>
                <% @in_room_users.each do |id, data| %>
                  <tr>
                    <td><%= id %></td>
                    <td><%= data[:name] %></td>
                    <td><%= data[:rate] %></td>
                    <td><%= data[:metadata]&.dig(:room_id) || '-' %></td>
                    <td class="time-ago" data-timestamp="<%= data[:updated_at].to_i * 1000 %>">
                      <%= time_ago_in_words(data[:updated_at]) %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  var adminStatusSubscription = null;
  var timeUpdateInterval = null;
  var lastReceivedData = null;

  function setupWebSocketConnection() {
    if (adminStatusSubscription) {
      console.log("Cleaning up existing WebSocket connection...");
      adminStatusSubscription.unsubscribe();
      adminStatusSubscription = null;
    }

    // Use the shared WebSocket manager (now always available)
    window.UserStatusWebSocket.connect('user_statuses_index', {
      onAdminData: function(data) {
        console.log("Received admin data:", data);
        lastReceivedData = data;
        updateUI(data);
        setupTimeUpdater();
      }
    });
  }





  function updateUI(data) {
    if (data.stats) {
      document.querySelector('[data-stat="総プレイヤー数"]').textContent = data.stats.total || 0;
      document.querySelector('[data-stat="マッチング中"]').textContent = data.stats.by_status?.matching || 0;
      document.querySelector('[data-stat="対戦中"]').textContent = data.stats.by_status?.matched || 0;
      document.querySelector('[data-stat="ルーム内"]').textContent = data.stats.by_status?.in_room || 0;
    }

    if (data.matching) {
      updateTable('matching-table', data.matching);
    }

    if (data.matched) {
      updateTable('matched-table', data.matched);
    }

    if (data.in_room) {
      updateTable('in-room-table', data.in_room);
    }
  }

  function updateChannelStats(channelType, data) {
    if (data.stats) {
      const prefix = channelType === 'rank_matching' ? 'rank' :
                     channelType === 'free_matching' ? 'free' : 'room';

      if (channelType === 'room_matching') {
        document.querySelector(`[data-stat="${prefix}-total"]`).textContent = data.stats.total || 0;
        document.querySelector(`[data-stat="${prefix}-count"]`).textContent = data.stats.rooms || 0;
      } else {
        document.querySelector(`[data-stat="${prefix}-total"]`).textContent = data.stats.total || 0;
        document.querySelector(`[data-stat="${prefix}-matching"]`).textContent = data.stats.matching || 0;
        document.querySelector(`[data-stat="${prefix}-matched"]`).textContent = data.stats.matched || 0;
      }
    }
  }

  function updateTable(tableId, users) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';

    Object.entries(users).forEach(([id, data]) => {
      const tr = document.createElement('tr');

      const idTd = document.createElement('td');
      idTd.textContent = id;
      tr.appendChild(idTd);

      const nameTd = document.createElement('td');
      nameTd.textContent = data.name || 'Unknown';
      tr.appendChild(nameTd);

      const rateTd = document.createElement('td');
      rateTd.textContent = data.rate || 0;
      tr.appendChild(rateTd);

      const roomIdTd = document.createElement('td');
      roomIdTd.textContent = data.metadata?.room_id || '-';
      tr.appendChild(roomIdTd);

      const timeTd = document.createElement('td');
      timeTd.dataset.timestamp = new Date(data.updated_at).getTime();
      timeTd.className = 'time-ago';
      updateTimeAgo(timeTd);
      tr.appendChild(timeTd);

      tbody.appendChild(tr);
    });
  }

  function updateTimeAgo(element) {
    const timestamp = parseInt(element.dataset.timestamp);
    if (!timestamp) return;

    const now = new Date();
    const diffInSeconds = Math.floor((now - timestamp) / 1000);

    if (diffInSeconds < 60) {
      element.textContent = `${diffInSeconds}秒前`;
      element.style.color = '#28a745';
    } else if (diffInSeconds < 3600) {
      element.textContent = `${Math.floor(diffInSeconds / 60)}分前`;
      element.style.color = '#17a2b8';
    } else {
      element.textContent = `${Math.floor(diffInSeconds / 3600)}時間前`;
      element.style.color = '#6c757d';
    }
  }

  function updateAllTimeAgo() {
    const timeElements = document.querySelectorAll('.time-ago');
    timeElements.forEach(updateTimeAgo);
  }

  function setupTimeUpdater() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
    }

    timeUpdateInterval = setInterval(updateAllTimeAgo, 1000);
  }

  function cleanupOnPageLeave() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
      timeUpdateInterval = null;
    }
    // UserStatusWebSocket manager handles connection cleanup
  }

  // Initialize when page loads
  document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event detected for index view");
    setupWebSocketConnection();
    updateAllTimeAgo();
    setupTimeUpdater();
  });

  // Handle page navigation
  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      console.log("Page loaded from cache, reconnecting WebSocket");
      setupWebSocketConnection();
      updateAllTimeAgo();
      setupTimeUpdater();
    }
  });

  // Cleanup on page leave (but don't disconnect WebSocket if going to another user_status page)
  window.addEventListener('beforeunload', function() {
    console.log("Page unload event detected");
    cleanupOnPageLeave();
  });
</script>
<style>
  .card {
    height: 100%;
  }

  .table-sm td, .table-sm th {
    padding: 0.4rem 0.5rem;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  .card-header {
    font-size: 1.1rem;
    font-weight: 600;
  }

  .table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
    font-weight: 600;
    font-size: 0.95rem;
  }

  .table-responsive {
    max-height: 450px;
    overflow-y: auto;
    overflow-x: auto;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .p-0 {
    padding: 0 !important;
  }

  .font-bold.h4 {
    font-size: 1.8rem;
    font-weight: 700;
  }

  .text-sm.text-gray-600 {
    font-size: 1rem;
    color: #6c757d;
  }

  .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.03);
  }

  .table-striped tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
  }
</style>
