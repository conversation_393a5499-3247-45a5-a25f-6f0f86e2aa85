<div class="container-fluid">
  <h1 class="mb-4">ルームマッチ状況 <span id="connection-status" class="badge bg-warning">接続中...</span></h1>

  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">総プレイヤー数</div>
          <div class="font-bold h4" data-stat="総プレイヤー数"><%= @stats[:total] %></div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-body text-center">
          <div class="text-sm text-gray-600">ルーム数</div>
          <div class="font-bold h4" data-stat="ルーム数"><%= @stats[:rooms] %></div>
        </div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="card-header">
      ルーム一覧
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-striped table-sm mb-0" id="rooms-table">
          <thead>
            <tr>
              <th>No.</th>
              <th>ルームID</th>
              <th>プレイヤー情報</th>
              <th>Open ID</th>
              <th>更新時間</th>
            </tr>
          </thead>
          <tbody>
            <% if @rooms.empty? %>
              <tr>
                <td colspan="5" class="text-center p-3">データがありません</td>
              </tr>
            <% else %>
              <% @rooms.each_with_index do |(room_id, room_data), index| %>
                <tr>
                  <td><%= index + 1 %></td>
                  <td><%= room_id %></td>
                  <td>
                    <% room_data[:players].each_with_index do |player, player_index| %>
                      <div class="player-info">
                        <strong>P<%= player_index + 1 %>:</strong> 
                        ID: <%= player[:id] %>, 
                        名前: <%= player[:name] || 'Unknown' %>, 
                        準備状態: <span class="<%= player[:ready] ? 'ready-check' : 'not-ready' %>"><%= player[:ready] ? '✅' : '❌' %></span>
                      </div>
                    <% end %>
                  </td>
                  <td><%= room_data[:players].first[:open_id] rescue '-' %></td>
                  <td class="time-ago" data-timestamp="<%= room_data[:updated_at].to_i * 1000 %>">
                    <%= time_ago_in_words(room_data[:updated_at]) %>
                  </td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="mt-4">
    <a href="<%= user_statuses_path %>" class="btn btn-secondary">戻る</a>
    <a href="<%= rank_match_user_statuses_path %>" class="btn btn-outline-primary">ランクマッチ</a>
    <a href="<%= free_match_user_statuses_path %>" class="btn btn-outline-primary">フリーマッチ</a>
    <a href="<%= room_match_user_statuses_path %>" class="btn btn-primary">ルームマッチ</a>
  </div>
</div>

<script>
  var matchStatusSubscription = null;
  var timeUpdateInterval = null;
  var lastReceivedData = null;

  function setupWebSocketConnection() {
    if (matchStatusSubscription) {
      console.log("Cleaning up existing WebSocket connection...");
      matchStatusSubscription.unsubscribe();
      matchStatusSubscription = null;
    }

    console.log("Setting up admin WebSocket connection for room match status...");

    const adminId = <%= current_master_user&.id || 'null' %>;

    let wsUrl = '/cable?client_type=admin';
    if (adminId) {
      wsUrl += '&token=admin-' + adminId;
    }

    console.log("Connecting to WebSocket at: " + wsUrl);
    const cable = ActionCable.createConsumer(wsUrl);

    matchStatusSubscription = cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: 'room' },
      {
        connected() {
          console.log("Connected to Admin::MatchStatusChannel for room match!");
          document.getElementById('connection-status').textContent = '接続済み';
          document.getElementById('connection-status').className = 'badge bg-success';
        },

        disconnected() {
          console.log("Disconnected from Admin::MatchStatusChannel");
          document.getElementById('connection-status').textContent = '切断';
          document.getElementById('connection-status').className = 'badge bg-danger';
        },

        received(data) {
          console.log("Received data:", data);
          lastReceivedData = data;
          updateUI(data);
          setupTimeUpdater();
        }
      }
    );
  }

  function updateUI(data) {
    console.log("Room updateUI called with data:", data);

    if (data.stats) {
      const totalElement = document.querySelector('[data-stat="総プレイヤー数"]');
      const roomsElement = document.querySelector('[data-stat="ルーム数"]');

      if (totalElement) totalElement.textContent = data.stats.total || 0;
      if (roomsElement) roomsElement.textContent = data.stats.rooms || 0;
    }

    if (data.rooms) {
      updateRoomsTable(data.rooms);
    }

    setupTimeUpdater();
  }

  function updateRoomsTable(rooms) {
    const table = document.getElementById('rooms-table');
    if (!table) return;

    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '';

    if (Object.keys(rooms).length === 0) {
      const tr = document.createElement('tr');
      const td = document.createElement('td');
      td.colSpan = 5;
      td.textContent = 'データがありません';
      td.className = 'text-center p-3';
      tr.appendChild(td);
      tbody.appendChild(tr);
      return;
    }

    let index = 1;
    Object.entries(rooms).forEach(([roomId, roomData]) => {
      const tr = document.createElement('tr');

      const indexTd = document.createElement('td');
      indexTd.textContent = index++;
      tr.appendChild(indexTd);

      // Room ID
      const roomIdTd = document.createElement('td');
      roomIdTd.textContent = roomId;
      tr.appendChild(roomIdTd);

      // Player info
      const playerInfoTd = document.createElement('td');
      if (roomData.players && roomData.players.length > 0) {
        roomData.players.forEach((player, playerIndex) => {
          const playerDiv = document.createElement('div');
          playerDiv.className = 'player-info';
          
          const playerText = document.createElement('strong');
          playerText.textContent = `P${playerIndex + 1}: `;
          playerDiv.appendChild(playerText);
          
          playerDiv.appendChild(document.createTextNode(`ID: ${player.id}, 名前: ${player.name || 'Unknown'}, 準備状態: `));
          
          const readySpan = document.createElement('span');
          readySpan.className = player.ready ? 'ready-check' : 'not-ready';
          readySpan.textContent = player.ready ? '✅' : '❌';
          playerDiv.appendChild(readySpan);
          
          playerInfoTd.appendChild(playerDiv);
        });
      } else {
        playerInfoTd.textContent = 'プレイヤーなし';
      }
      tr.appendChild(playerInfoTd);

      // Open ID
      const openIdTd = document.createElement('td');
      openIdTd.textContent = roomData.players && roomData.players.length > 0 ? roomData.players[0].open_id : '-';
      tr.appendChild(openIdTd);

      // Updated time
      const timeTd = document.createElement('td');
      timeTd.dataset.timestamp = new Date(roomData.updated_at).getTime();
      timeTd.className = 'time-ago';
      updateTimeAgo(timeTd);
      tr.appendChild(timeTd);

      tbody.appendChild(tr);
    });
  }

  function updateTimeAgo(element) {
    const timestamp = parseInt(element.dataset.timestamp);
    if (!timestamp) return;

    const now = new Date();
    const diffInSeconds = Math.floor((now - timestamp) / 1000);

    if (diffInSeconds < 60) {
      element.textContent = `${diffInSeconds}秒前`;
      element.style.color = '#28a745';
    } else if (diffInSeconds < 3600) {
      element.textContent = `${Math.floor(diffInSeconds / 60)}分前`;
      element.style.color = '#17a2b8';
    } else {
      element.textContent = `${Math.floor(diffInSeconds / 3600)}時間前`;
      element.style.color = '#6c757d';
    }
  }

  function updateAllTimeAgo() {
    const timeElements = document.querySelectorAll('.time-ago');
    timeElements.forEach(updateTimeAgo);
  }

  function setupTimeUpdater() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
    }

    timeUpdateInterval = setInterval(updateAllTimeAgo, 10000);
  }

  function cleanupOnPageLeave() {
    if (matchStatusSubscription) {
      console.log("Cleaning up WebSocket connection on page leave");
      matchStatusSubscription.unsubscribe();
      matchStatusSubscription = null;
    }

    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval);
      timeUpdateInterval = null;
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event detected for room match view");
    setupWebSocketConnection();
    updateAllTimeAgo();
    setupTimeUpdater();

    window.addEventListener('pageshow', function(event) {
      if (event.persisted) {
        console.log("Page loaded from cache, reconnecting WebSocket");
        setupWebSocketConnection();
        updateAllTimeAgo();
        setupTimeUpdater();
      }
    });

    window.addEventListener('beforeunload', function() {
      console.log("Page unload event detected");
      cleanupOnPageLeave();
    });
  });
</script>
<style>
  .card {
    height: 100%;
  }

  .table-sm td, .table-sm th {
    padding: 0.4rem 0.5rem;
    font-size: 0.9rem;
    white-space: nowrap;
  }

  .card-header {
    font-size: 1.1rem;
    font-weight: 600;
  }

  .player-info {
    margin-bottom: 0.5rem;
  }

  .player-info:last-child {
    margin-bottom: 0;
  }

  .ready-check {
    color: #28a745;
    font-weight: bold;
  }

  .not-ready {
    color: #dc3545;
    font-weight: bold;
  }
</style>
