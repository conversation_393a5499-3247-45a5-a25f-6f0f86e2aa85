<div class="container-fluid">
  <h1 class="mb-4">🎮 Matching System Test Page</h1>
  
  <!-- Current Status Overview -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card border-primary">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">🏆 Rank Matching</h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-4">
              <div class="text-sm text-gray-600">Total</div>
              <div class="font-bold h5" id="rank-total"><%= @current_stats[:rank][:total] %></div>
            </div>
            <div class="col-4">
              <div class="text-sm text-gray-600">Matching</div>
              <div class="font-bold h5" id="rank-matching"><%= @current_stats[:rank][:matching] %></div>
            </div>
            <div class="col-4">
              <div class="text-sm text-gray-600">Matched</div>
              <div class="font-bold h5" id="rank-matched"><%= @current_stats[:rank][:matched] %></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card border-success">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">🎯 Free Matching</h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-4">
              <div class="text-sm text-gray-600">Total</div>
              <div class="font-bold h5" id="free-total"><%= @current_stats[:free][:total] %></div>
            </div>
            <div class="col-4">
              <div class="text-sm text-gray-600">Matching</div>
              <div class="font-bold h5" id="free-matching"><%= @current_stats[:free][:matching] %></div>
            </div>
            <div class="col-4">
              <div class="text-sm text-gray-600">Matched</div>
              <div class="font-bold h5" id="free-matched"><%= @current_stats[:free][:matched] %></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card border-warning">
        <div class="card-header bg-warning text-white">
          <h5 class="mb-0">🏠 Room Matching</h5>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-6">
              <div class="text-sm text-gray-600">Total Players</div>
              <div class="font-bold h5" id="room-total"><%= @current_stats[:room][:total] %></div>
            </div>
            <div class="col-6">
              <div class="text-sm text-gray-600">Rooms</div>
              <div class="font-bold h5" id="room-count"><%= @current_stats[:room][:rooms] %></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Connection Status -->
  <div class="row mb-3">
    <div class="col-md-12">
      <div class="alert alert-info">
        <strong>WebSocket Status:</strong>
        <span id="connection-status" class="badge bg-warning">接続中...</span>
      </div>
    </div>
  </div>

  <!-- User Limit Control -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">👥 User Selection Control</h5>
        </div>
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-6">
              <label for="userLimit" class="form-label">Number of users to display:</label>
              <select id="userLimit" class="form-select" onchange="changeUserLimit()">
                <option value="30" <%= @current_limit == 30 ? 'selected' : '' %>>30 users</option>
                <option value="50" <%= @current_limit == 50 ? 'selected' : '' %>>50 users</option>
                <option value="100" <%= @current_limit == 100 ? 'selected' : '' %>>100 users</option>
                <option value="200" <%= @current_limit == 200 ? 'selected' : '' %>>200 users</option>
                <option value="500" <%= @current_limit == 500 ? 'selected' : '' %>>500 users</option>
                <option value="1000" <%= @current_limit == 1000 ? 'selected' : '' %>>All users (1000)</option>
              </select>
            </div>
            <div class="col-md-6">
              <div class="text-muted">
                Showing <strong><%= @test_users.count %></strong> of <strong><%= @total_test_users %></strong> total test users
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Controls -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">🎮 Test Controls</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <button class="btn btn-primary w-100 mb-2" onclick="simulateRankMatching()">
                🏆 Start Rank Matching
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-success w-100 mb-2" onclick="simulateFreeMatching()">
                🎯 Start Free Matching
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-warning w-100 mb-2" onclick="simulateRoomMatching()">
                🏠 ルームマッチング
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-info w-100 mb-2" onclick="simulateMatchSuccess()">
                ⚔️ Create Match
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-3">
              <button class="btn btn-secondary w-100 mb-2" onclick="selectAllUsers()">
                ✅ Select All Users
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-warning w-100 mb-2" onclick="disconnectSelectedUsers()">
                🔌 Disconnect Selected
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-success w-100 mb-2" onclick="autoReadyRooms()">
                🚀 Auto Ready All Rooms
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-danger w-100 mb-2" onclick="clearAllStatus()">
                🗑️ Clear All Status
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-3">
              <button class="btn btn-outline-secondary w-100 mb-2" onclick="deselectAllUsers()">
                ❌ Deselect All
              </button>
            </div>
            <div class="col-md-3">
              <button class="btn btn-outline-info w-100 mb-2" onclick="testBroadcast()">
                📡 Test Broadcast
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Users Selection -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">👥 Test Users (Select users to test with)</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <% @test_users.each do |user| %>
              <div class="col-md-4 col-lg-3 mb-2">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" value="<%= user.open_id %>" id="user_<%= user.open_id %>" name="test_users">
                  <label class="form-check-label" for="user_<%= user.open_id %>">
                    <strong><%= user.name %></strong><br>
                    <small class="text-muted">Rate: <%= user.rate %></small>
                  </label>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Current Rooms -->
  <% if @rooms.any? %>
    <div class="row mb-4">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">🏠 Current Rooms</h5>
          </div>
          <div class="card-body">
            <% @rooms.each do |room_id, room_data| %>
              <div class="card mb-2">
                <div class="card-header">
                  <strong>Room: <%= room_id %></strong>
                  <span class="badge bg-info"><%= room_data[:players].size %> players</span>
                </div>
                <div class="card-body">
                  <div class="row">
                    <% room_data[:players].each do |player| %>
                      <div class="col-md-6 mb-2">
                        <div class="card border-<%= player[:ready] ? 'success' : 'warning' %>">
                          <div class="card-body p-2">
                            <div class="d-flex justify-content-between align-items-center">
                              <div>
                                <strong><%= player[:name] %></strong><br>
                                <small class="text-muted">ID: <%= player[:id] %></small><br>
                                <span class="badge bg-secondary"><%= player[:role]&.upcase || 'PLAYER' %></span>
                              </div>
                              <div class="text-center">
                                <div class="mb-1">
                                  <span class="badge bg-<%= player[:ready] ? 'success' : 'warning' %>">
                                    <%= player[:ready] ? '✅ READY' : '⏳ NOT READY' %>
                                  </span>
                                </div>
                                <button class="btn btn-sm btn-outline-primary" onclick="toggleReady('<%= player[:id] %>')">
                                  Toggle Ready
                                </button>
                                <button class="btn btn-sm btn-outline-danger ms-1" onclick="disconnectUser('<%= player[:id] %>')">
                                  Disconnect
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Quick Links -->
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">🔗 Quick Links</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <%= link_to "📊 All Status View", all_user_statuses_path, class: "btn btn-outline-primary w-100 mb-2" %>
            </div>
            <div class="col-md-3">
              <%= link_to "🏆 Rank Match View", rank_match_user_statuses_path, class: "btn btn-outline-primary w-100 mb-2" %>
            </div>
            <div class="col-md-3">
              <%= link_to "🎯 Free Match View", free_match_user_statuses_path, class: "btn btn-outline-success w-100 mb-2" %>
            </div>
            <div class="col-md-3">
              <%= link_to "🏠 Room Match View", room_match_user_statuses_path, class: "btn btn-outline-warning w-100 mb-2" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Result Modal -->
<div class="modal fade" id="resultModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Test Result</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="resultModalBody">
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
  var adminStatusSubscription = null;

  // Setup WebSocket connection for realtime updates
  function setupWebSocketConnection() {
    if (adminStatusSubscription) {
      console.log("Cleaning up existing WebSocket connection...");
      adminStatusSubscription.unsubscribe();
      adminStatusSubscription = null;
    }

    console.log("Setting up WebSocket connection for matching test...");

    const adminId = <%= current_master_user&.id || 'null' %>;

    let wsUrl = '/cable?client_type=admin';
    if (adminId) {
      wsUrl += '&token=admin-' + adminId;
    }

    console.log("Connecting to WebSocket at: " + wsUrl);
    const cable = ActionCable.createConsumer(wsUrl);

    // Subscribe to admin channel
    adminStatusSubscription = cable.subscriptions.create("Admin::UserStatusChannel", {
      connected() {
        console.log("Connected to Admin::UserStatusChannel!");
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
          statusElement.textContent = '接続済み';
          statusElement.className = 'badge bg-success';
        }
      },

      disconnected() {
        console.log("Disconnected from Admin::UserStatusChannel");
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
          statusElement.textContent = '切断';
          statusElement.className = 'badge bg-danger';
        }
      },

      received(data) {
        console.log("Received admin data:", data);
        updateRealTimeData(data);
      }
    });

    // Subscribe to match channels
    cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "rank" },
      {
        received(data) {
          console.log("Received rank match data:", data);
          updateRankStats(data);
        }
      }
    );

    cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "free" },
      {
        received(data) {
          console.log("Received free match data:", data);
          updateFreeStats(data);
        }
      }
    );

    cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "room" },
      {
        received(data) {
          console.log("Received room match data:", data);
          updateRoomStats(data);
        }
      }
    );
  }

  function updateRealTimeData(data) {
    console.log("Received admin realtime data:", data);
    // Admin channel data - general updates
    // Specific stats are handled by individual channel callbacks
  }

  function updateRankStats(data) {
    console.log("Updating rank stats:", data);
    if (data.stats) {
      const stats = data.stats;

      const rankTotalElement = document.getElementById('rank-total');
      const rankMatchingElement = document.getElementById('rank-matching');
      const rankMatchedElement = document.getElementById('rank-matched');

      if (rankTotalElement) rankTotalElement.textContent = stats.total || 0;
      if (rankMatchingElement) rankMatchingElement.textContent = stats.matching || 0;
      if (rankMatchedElement) rankMatchedElement.textContent = stats.matched || 0;
    }
  }

  function updateFreeStats(data) {
    console.log("Updating free stats:", data);
    if (data.stats) {
      const stats = data.stats;

      const freeTotalElement = document.getElementById('free-total');
      const freeMatchingElement = document.getElementById('free-matching');
      const freeMatchedElement = document.getElementById('free-matched');

      if (freeTotalElement) freeTotalElement.textContent = stats.total || 0;
      if (freeMatchingElement) freeMatchingElement.textContent = stats.matching || 0;
      if (freeMatchedElement) freeMatchedElement.textContent = stats.matched || 0;
    }
  }

  function updateRoomStats(data) {
    console.log("Updating room stats:", data);
    if (data.stats) {
      const stats = data.stats;

      const roomTotalElement = document.getElementById('room-total');
      const roomCountElement = document.getElementById('room-count');

      if (roomTotalElement) roomTotalElement.textContent = stats.total || 0;
      if (roomCountElement) roomCountElement.textContent = stats.rooms || 0;
    }

    // Update room display if rooms data is available
    if (data.rooms) {
      updateRoomsDisplay(data.rooms);
    }
  }

  function updateRoomsDisplay(rooms) {
    const roomsContainer = document.querySelector('.card-body');
    if (!roomsContainer || Object.keys(rooms).length === 0) {
      return;
    }

    // Find the rooms section and update it
    const currentRoomsSection = document.querySelector('.card-header h5:contains("Current Rooms")');
    if (currentRoomsSection) {
      // Reload page to show updated rooms (simple approach)
      // In production, you might want to update DOM directly
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }

  function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('input[name="test_users"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
  }

  function selectAllUsers() {
    const checkboxes = document.querySelectorAll('input[name="test_users"]');
    checkboxes.forEach(cb => cb.checked = true);
  }

  function deselectAllUsers() {
    const checkboxes = document.querySelectorAll('input[name="test_users"]');
    checkboxes.forEach(cb => cb.checked = false);
  }

  function changeUserLimit() {
    const limit = document.getElementById('userLimit').value;
    window.location.href = `/matching_test?limit=${limit}`;
  }

  function showResult(data) {
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    const body = document.getElementById('resultModalBody');

    if (data.success) {
      body.innerHTML = `
        <div class="alert alert-success">
          <h6>✅ ${data.message}</h6>
          ${data.results ? `
            <ul class="mt-2">
              ${data.results.map(r => `
                <li><strong>${r.name}</strong> (Rate: ${r.rate}) - Status: ${r.status}
                  ${r.deck_ready !== undefined ? ` - Deck: ${r.deck_ready ? '✅ Ready' : '❌ Not Ready'}` : ''}
                  ${r.ready !== undefined ? ` - Ready: ${r.ready ? '✅' : '❌'}` : ''}
                  ${r.role ? ` - Role: ${r.role.toUpperCase()}` : ''}
                  ${r.room_id ? ` - Room: ${r.room_id}` : ''}
                </li>
              `).join('')}
            </ul>
          ` : ''}
          ${data.logic ? `<p class="mt-2"><strong>Logic:</strong> ${data.logic}</p>` : ''}
          ${data.rooms_created ? `<p><strong>Rooms Created:</strong> ${data.rooms_created}</p>` : ''}
          ${data.battle_room_id ? `<p><strong>Battle Room:</strong> ${data.battle_room_id}</p>` : ''}
        </div>
      `;
    } else {
      body.innerHTML = `
        <div class="alert alert-danger">
          <h6>❌ ${data.message}</h6>
        </div>
      `;
    }

    modal.show();

    // Auto-close modal after 3 seconds (WebSocket will handle realtime updates)
    setTimeout(() => {
      modal.hide();
    }, 3000);
  }

  function simulateRankMatching() {
    const userIds = getSelectedUsers();
    if (userIds.length === 0) {
      alert('少なくとも1人のユーザーを選択してください');
      return;
    }

    fetch('/matching_test/simulate_rank_matching', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({ user_ids: userIds })
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => console.error('Error:', error));
  }

  function simulateFreeMatching() {
    const userIds = getSelectedUsers();
    if (userIds.length === 0) {
      alert('少なくとも1人のユーザーを選択してください');
      return;
    }

    fetch('/matching_test/simulate_free_matching', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({ user_ids: userIds })
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => console.error('Error:', error));
  }

  function simulateRoomMatching() {
    const userIds = getSelectedUsers();
    if (userIds.length === 0) {
      alert('少なくとも1人のユーザーを選択してください');
      return;
    }

    if (confirm(`${userIds.length}人のユーザーでルームマッチングを開始しますか？`)) {
      fetch('/matching_test/simulate_room_matching', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({ user_ids: userIds })
      })
      .then(response => response.json())
      .then(data => showResult(data))
      .catch(error => console.error('Error:', error));
    }
  }

  function simulateMatchSuccess() {
    fetch('/matching_test/simulate_match_success', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => console.error('Error:', error));
  }

  function clearAllStatus() {
    if (confirm('すべてのユーザーステータスをクリアしますか？')) {
      fetch('/matching_test/clear_all_status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        showResult(data);
        // Reload page to reflect cleared status
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      })
      .catch(error => console.error('Error:', error));
    }
  }

  function toggleReady(userId) {
    fetch('/matching_test/toggle_ready', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({ user_id: userId })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show result in modal (WebSocket will handle realtime updates)
        showResult(data);
      } else {
        alert(data.message);
      }
    })
    .catch(error => console.error('Error:', error));
  }

  function disconnectUser(userId) {
    if (confirm(`ユーザー ${userId} を切断しますか？`)) {
      fetch('/matching_test/disconnect_user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({ user_id: userId })
      })
      .then(response => response.json())
      .then(data => {
        showResult(data);
      })
      .catch(error => console.error('Error:', error));
    }
  }

  function disconnectSelectedUsers() {
    const userIds = getSelectedUsers();
    if (userIds.length === 0) {
      alert('少なくとも1人のユーザーを選択してください');
      return;
    }

    if (confirm(`選択された${userIds.length}人のユーザーを切断しますか？`)) {
      fetch('/matching_test/disconnect_all_users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({ user_ids: userIds })
      })
      .then(response => response.json())
      .then(data => {
        showResult(data);
      })
      .catch(error => console.error('Error:', error));
    }
  }

  function autoReadyRooms() {
    if (confirm('Auto ready all users in rooms and create matches?')) {
      fetch('/matching_test/auto_ready_rooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const modal = new bootstrap.Modal(document.getElementById('resultModal'));
          const body = document.getElementById('resultModalBody');

          body.innerHTML = `
            <div class="alert alert-success">
              <h6>✅ ${data.message}</h6>
              ${data.matches_created && data.matches_created.length > 0 ? `
                <h6 class="mt-3">Matches Created:</h6>
                <ul class="mt-2">
                  ${data.matches_created.map(match => `
                    <li><strong>Battle Room:</strong> ${match.battle_room_id}<br>
                        <strong>Players:</strong> ${match.players.map(p => p.name).join(' vs ')}
                    </li>
                  `).join('')}
                </ul>
              ` : '<p>No matches were created.</p>'}
            </div>
          `;

          modal.show();

          // Refresh page after 3 seconds to see updated stats
          setTimeout(() => {
            window.location.reload();
          }, 3000);
        } else {
          alert(data.message);
        }
      })
      .catch(error => console.error('Error:', error));
    }
  }

  function testBroadcast() {
    console.log('Testing WebSocket broadcast...');

    fetch('/matching_test/test_broadcast', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      }
    })
    .then(response => response.json())
    .then(data => {
      console.log('Broadcast test result:', data);

      if (data.success) {
        alert(`✅ ${data.message}\nTimestamp: ${data.timestamp}\n\nCheck console for WebSocket messages.`);
      } else {
        alert(`❌ ${data.message}`);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error testing broadcast');
    });
  }

  function cleanupOnPageLeave() {
    if (adminStatusSubscription) {
      console.log("Cleaning up WebSocket connection on page leave");
      adminStatusSubscription.unsubscribe();
      adminStatusSubscription = null;
    }
  }

  // Initialize WebSocket connection when page loads
  document.addEventListener('DOMContentLoaded', function() {
    console.log("DOMContentLoaded event detected for matching test");
    setupWebSocketConnection();
  });

  // Handle page navigation
  window.addEventListener('pageshow', function(event) {
    if (event.persisted) {
      console.log("Page loaded from cache, reconnecting WebSocket");
      setupWebSocketConnection();
    }
  });

  // Cleanup on page leave
  window.addEventListener('beforeunload', function() {
    console.log("Page unload event detected");
    cleanupOnPageLeave();
  });
</script>
