<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- ブランドロゴ -->
    <a href="<%= root_path %>" class="brand-link">
        <img src="https://adminlte.io/themes/v3/dist/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">T1管理サイト</span>
    </a>

    <!-- サイドバーメニュー -->
    <div class="sidebar">
        <!-- サイドバー検索 -->
        <div class="form-inline mt-2">
            <div class="input-group" data-widget="sidebar-search">
                <input class="form-control form-control-sidebar" type="search" placeholder="メニュー検索" aria-label="Search">
                <div class="input-group-append">
                    <button class="btn btn-sidebar">
                        <i class="fas fa-search fa-fw"></i>
                    </button>
                </div>
            </div>
        </div>

        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar nav-compact nav-child-indent flex-column" data-widget="treeview" role="menu" data-accordion="true">
            
                <%# ----- ダッシュボード ----- %>
                <li class="nav-item">
                    <%= link_to root_path, class: "nav-link #{'active' if current_page?(root_path)}" do %>
                    <i class="nav-icon fas fa-home"></i>
                    <p>ダッシュボード</p>
                    <% end %>
                </li>

                <%# ----- ホーム機能群 ----- %>
                <li class="nav-header">ホーム</li>
                
                <!-- お知らせ -->
                <li class="nav-item">
                    <%= link_to infos_path, class: "nav-link #{'active' if request.path.start_with?('/infos')}" do %>
                    <i class="nav-icon fas fa-bullhorn"></i>
                    <p>お知らせ</p>
                    <% end %>
                </li>
                
                <!-- ギフト -->
                <li class="nav-item">
                    <%= link_to gift_masters_path, class: "nav-link #{'active' if request.path.start_with?('/gift_masters')}" do %>
                    <i class="nav-icon fas fa-gift"></i>
                    <p>ギフト</p>
                    <% end %>
                </li>

                <!-- 宝箱 -->
                <li class="nav-item">
                    <%= link_to chests_path, class: "nav-link #{'active' if request.path.start_with?('/chests')}" do %>
                    <i class="nav-icon fas fa-box"></i>
                    <p>宝箱</p>
                    <% end %>
                </li>

                <!-- バトルパス -->
                <li class="nav-item">
                    <%= link_to battle_passes_path, class: "nav-link #{'active' if request.path.start_with?('/battle_passes')}" do %>
                    <i class="nav-icon fas fa-trophy"></i>
                    <p>バトルパス</p>
                    <% end %>
                </li>

                <!-- ログインボーナス -->
                <li class="nav-item">
                    <%= link_to login_bonuses_path, class: "nav-link #{'active' if request.path.start_with?('/login_bonuses')}" do %>
                    <i class="nav-icon fas fa-calendar-check"></i>
                    <p>ログインボーナス</p>
                    <% end %>
                </li>

                <!-- ミッション -->
                <% if false %>
                <li class="nav-item">
                    <%= link_to missions_path, class: "nav-link #{'active' if request.path.start_with?('/missions')}" do %>
                    <i class="nav-icon fas fa-tasks"></i>
                    <p>ミッション</p>
                    <% end %>
                </li>

                <!-- バナー -->
                <li class="nav-item">
                    <%= link_to banners_path, class: "nav-link #{'active' if request.path.start_with?('/banners')}" do %>
                    <i class="nav-icon fas fa-image"></i>
                    <p>バナー</p>
                    <% end %>
                </li>

                <!-- ポップアップ -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/popups')}" do %>
                    <i class="nav-icon fas fa-window-restore"></i>
                    <p>ポップアップ *</p>
                    <% end %>
                </li>


                <% end %>

                <%# ----- バトル機能群 ----- %>
                <li class="nav-header">バトル</li>
                
                <!-- マッチデータ -->
                <li class="nav-item">
                    <%= link_to matches_path, class: "nav-link #{'active' if request.path.start_with?('/matches')}" do %>
                    <i class="nav-icon fas fa-users"></i>
                    <p>対戦データ</p>
                    <% end %>
                </li>

                <!-- カードステータス -->
                <li class="nav-item">
                    <%= link_to card_stats_index_path, class: "nav-link #{'active' if request.path.start_with?('/card_stats')}" do %>
                    <i class="nav-icon fas fa-users"></i>
                    <p>カードステータス</p>
                    <% end %>
                </li>

                <!-- ユーザーステータス -->
                <li class="nav-item">
                    <%= link_to user_statuses_path, class: "nav-link #{'active' if request.path.start_with?('/user_statuses')}" do %>
                    <i class="nav-icon fas fa-chart-line"></i>
                    <p>ユーザーステータス</p>
                    <% end %>
                </li>

                <!-- マッチングテスト -->
                <li class="nav-item">
                    <%= link_to matching_test_index_path, class: "nav-link #{'active' if request.path.start_with?('/matching_test')}" do %>
                    <i class="nav-icon fas fa-gamepad"></i>
                    <p>🎮 マッチングテスト</p>
                    <% end %>
                </li>

                <!-- ランクマッチ状況 -->
                <li class="nav-item">
                    <%= link_to user_statuses_path, class: "nav-link #{'active' if request.path.start_with?('/user_statuses')}" do %>
                    <i class="nav-icon fas fa-chart-linenav-icon fas fa-chart-line"></i>
                    <p>ランクマッチ状況</p>
                    <% end %>
                </li>

                <% if false %>
                <!-- イベントバトル -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/event_battles')}" do %>
                    <i class="nav-icon fas fa-fire"></i>
                    <p>イベントバトル *</p>
                    <% end %>
                </li>
                <% end %>
                <%# ----- パック機能群 ----- %>
                <li class="nav-header">パック</li>

                <li class="nav-item">
                    <%= link_to packs_path, class: "nav-link #{'active' if request.path.start_with?('/packs')}" do %>
                    <i class="nav-icon fas fa-dice"></i>
                    <p>パック</p>
                    <% end %>
                </li>   
                
                <%# ----- ショップ機能群 ----- %>
                <li class="nav-header">ショップ</li>
                <!-- 通常課金 -->
                <li class="nav-item">
                    <%= link_to charging_items_path, class: "nav-link #{'active' if request.path.start_with?('/charging_items')}" do %>
                    <i class="nav-icon fas fa-coins"></i>
                    <p>通常課金</p>
                    <% end %>
                </li>

                <!-- バンドル購入 -->
                <li class="nav-item">
                    <%= link_to shop_bundles_path, class: "nav-link #{'active' if request.path.start_with?('/shop_bundles')}" do %>
                    <i class="nav-icon fas fa-shopping-cart"></i>
                    <p>バンドル購入</p>
                    <% end %>
                </li>

                <!-- サプライ購入 -->
                <li class="nav-item">
                    <%= link_to supplies_path, class: "nav-link #{'active' if request.path.start_with?('/supplies')}" do %>
                    <i class="nav-icon fas fa-store"></i>
                    <p>サプライ購入</p>
                    <% end %>
                </li>

                
                <% if false %>

                <!-- 累計チャージ報酬 -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/total_charge_rewards')}" do %>
                    <i class="nav-icon fas fa-gem"></i>
                    <p>累計チャージ報酬 *</p>
                    <% end %>
                </li>

                <!-- 累計消費報酬 -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/total_use_rewards')}" do %>
                    <i class="nav-icon fas fa-gem"></i>
                    <p>累計消費報酬 *</p>
                    <% end %>
                </li>

                <!-- 願い返し -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/wish_returns')}" do %>
                    <i class="nav-icon fas fa-star"></i>
                    <p>願い返し *</p>
                    <% end %>
                </li>

                <!-- 構築済みデッキ -->
                <li class="nav-item">
                    <%= link_to "#", class: "nav-link #{'active' if request.path.start_with?('/prebuilt_decks')}" do %>
                    <i class="nav-icon fas fa-layer-group"></i>
                    <p>構築済みデッキ *</p>
                    <% end %>
                </li>

                <% end %>
                
                <%# ----- マスターデータ ----- %>
                <li class="nav-header">マスターデータ</li>

                <!-- マスターデータ -->
                <li class="nav-item">
                    <%= link_to master_data_index_path, class: "nav-link #{'active' if request.path.start_with?('/master_data')}" do %>
                    <i class="nav-icon fas fa-database"></i>
                    <p>マスターデータ</p>
                    <% end %>
                </li>
                
                <%# ----- カード修正 ----- %>
                <li class="nav-header">カード修正</li>
                
                <li class="nav-item">
                    <%= link_to card_modifiers_path, class: "nav-link #{'active' if request.path.start_with?('/card_modifiers')}" do %>
                    <i class="nav-icon fas fa-layer-group"></i>
                    <p>カード修正設定</p>
                    <% end %>
                </li>

                <!-- カード修正前 -->
                <li class="nav-item">
                    <%= link_to cards_default_path, class: "nav-link #{'active' if current_page?(cards_default_path)}" do %>
                        <i class="nav-icon fas fa-clipboard-list"></i>
                        <p>カード修正前</p>
                    <% end %>
                </li>

                <!-- カード修正後 -->
                <li class="nav-item">
                    <%= link_to cards_modified_path, class: "nav-link #{'active' if current_page?(cards_modified_path)}" do %>
                        <i class="nav-icon fas fa-clipboard-check"></i>
                        <p>カード修正後</p>
                    <% end %>
                </li>

                <%# ----- デッキ管理 ----- %>
                <li class="nav-header">デッキ管理</li>

                <!-- デッキ管理 -->
                <li class="nav-item">
                    <%= link_to decks_path, class: "nav-link #{'active' if request.path.start_with?('/decks')}" do %>
                    <i class="nav-icon fas fa-layer-group"></i>
                    <p>デッキ管理</p>
                    <% end %>
                </li>
                
                <%# ----- プレイデータ ----- %>
                <li class="nav-header">プレイデータ</li>

                <!-- ユーザー管理 -->
                <li class="nav-item">
                    <%= link_to user_page_find_path, class: "nav-link #{'active' if request.path.start_with?('/user_page/find')}" do %>
                    <i class="nav-icon fas fa-users"></i>
                    <p>ユーザー管理</p>
                    <% end %>
                </li>

                <!-- ランキング表示 -->
                <li class="nav-item">
                    <%= link_to user_page_ranking_path(category: "rate"), class: "nav-link #{'active' if request.path.start_with?('/user_page/ranking')}" do %>
                    <i class="nav-icon fas fa-chart-line"></i>
                    <p>ランキング表示</p>
                    <% end %>
                </li>

                 <!-- ユーザーバン -->
                <li class="nav-item">
                    <%= link_to new_user_ban_path, class: "nav-link #{'active' if request.path.start_with?('/user_bans/new')}" do %>
                    <i class="nav-icon fas fa-user-slash"></i>
                    <p>ユーザー禁止</p>
                    <% end %>
                </li>

                <%# ----- その他の設定 ----- %>
                <li class="nav-header">その他の設定</li>


                <!-- 言語管理 -->
                <li class="nav-item">
                    <%= link_to langs_path, class: "nav-link #{'active' if request.path.start_with?('/langs')}" do %>
                    <i class="nav-icon fas fa-language"></i>
                    <p>言語管理</p>
                    <% end %>
                </li>

                <!-- 期間設定 -->
                <li class="nav-item">
                    <%= link_to periods_path, class: "nav-link #{'active' if request.path.start_with?('/periods')}" do %>
                    <i class="nav-icon fas fa-calendar-alt"></i>
                    <p>期間設定</p>
                    <% end %>
                </li>

                <%# ----- サーバー管理 ----- %>
                <li class="nav-header">サーバー管理</li>

                <!-- バージョン管理 -->
                <li class="nav-item">
                    <%= link_to server_versions_path, class: "nav-link #{'active' if request.path.start_with?('/server_versions')}" do %>
                    <i class="nav-icon fas fa-history"></i>
                    <p>バージョン管理</p>
                    <% end %>
                </li>

                <!-- パラメータ設定 -->
                <li class="nav-item">
                    <%= link_to parameter_settings_path, class: "nav-link #{'active' if request.path.start_with?('/parameter_settings')}" do %>
                    <i class="nav-icon fas fa-sliders-h"></i>
                    <p>パラメータ設定</p>
                    <% end %>
                </li>

                <!-- アカウント設定 -->
                <li class="nav-item">
                    <%= link_to account_show_path, class: "nav-link #{'active' if current_page?(controller: 'account', action: 'show')}" do %>
                    <i class="nav-icon fas fa-user-cog"></i>
                    <p>アカウント設定</p>
                    <% end %>
                </li>
                
                <!-- バトル記録 -->
                <li class="nav-item">
                    <%= link_to battle_records_path, class: "nav-link #{'active' if request.path.start_with?('/battle_records')}" do %>
                    <i class="nav-icon fas fa-gamepad"></i>
                    <p>バトル記録</p>
                    <% end %>
                </li>

                <!-- Play Ground -->
                <li class="nav-item">
                    <%= link_to play_grounds_path, class: "nav-link #{'active' if request.path.start_with?('/play_grounds')}" do %>
                        <i class="nav-icon fas fa-play"></i>
                        <p>Play Ground</p>
                    <% end %>
                </li>
            </ul>
        </nav>
    </div>
</aside>
