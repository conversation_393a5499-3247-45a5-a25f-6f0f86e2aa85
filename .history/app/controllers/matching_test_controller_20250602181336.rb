class MatchingTestController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @current_limit = params[:limit] ? params[:limit].to_i : 30
    limit = @current_limit
    
    @test_users = User.where("open_id LIKE ?", "test_user_%")
                     .order(:rate)
                     .limit(limit)
    @current_stats = {
      rank: {
        total: UserStatusService.count_by_channel_type('rank_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched').size
      },
      free: {
        total: UserStatusService.count_by_channel_type('free_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('free_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('free_matching', 'matched').size
      },
      room: {
        total: UserStatusService.count_by_channel_type('room_matching'),
        rooms: UserStatusService.get_room_count
      }
    }
    
    @rooms = UserStatusService.get_rooms_data

    UserStatusService.broadcast_to_admin
  end

  def simulate_rank_matching
    user_ids = params[:user_ids] || []
    
    if user_ids.empty?
      render json: { success: false, message: "少なくとも1人のユーザーを選択してください" }
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      UserStatusService.update_status(user_id, "matching", {
        rank: user.rate,
        timecount: 0,
        queue_size: user_ids.size,
        started_at: Time.now,
        channel_type: "rank_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching"
      }
    end

    UserStatusService.broadcast_to_admin

    render json: {
      success: true,
      message: "#{results.size}人のユーザーがランクマッチングを開始しました",
      results: results
    }
  end

  def simulate_free_matching
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "少なくとも1人のユーザーを選択してください" }
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      default_deck = generate_default_deck

      UserStatusService.update_status(user_id, "matching", {
        timecount: 0,
        started_at: Time.now,
        channel_type: "free_matching",
        deck: default_deck,
        deck_ready: true
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching",
        deck_ready: true
      }
    end

    UserStatusService.broadcast_to_admin

    render json: {
      success: true,
      message: "#{results.size}人のユーザーがデッキ付きフリーマッチングを開始しました",
      results: results
    }
  end

  def simulate_room_matching
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "少なくとも1人のユーザーを選択してください" }
      return
    end

    # ロジック: ユーザーを半分に分ける - 前半はルーム作成、後半は既存ルームに参加
    total_users = user_ids.size
    room_creators_count = (total_users / 2.0).ceil  # 切り上げ
    joiners_count = total_users - room_creators_count

    room_creators = user_ids[0...room_creators_count]
    room_joiners = user_ids[room_creators_count..-1]

    results = []
    created_rooms = []

    # ステップ1: 前半ユーザーのルーム作成
    room_creators.each_with_index do |user_id, index|
      user = User.find_by(open_id: user_id)
      next unless user

      room_id = "ROOM_#{Time.now.to_i}_#{index}"
      created_rooms << room_id

      # ルーム作成者 → 自動レディ（最初の人なので）
      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        joined_at: Time.now,
        ready: true,  # 作成者は自動レディ
        role: "creator",
        channel_type: "room_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        room_id: room_id,
        ready: true,
        role: "creator",
        status: "in_room"
      }
    end

    # ステップ2: 後半ユーザーを作成済みルームに参加（最大2人/ルーム）
    available_rooms = created_rooms.dup  # 参加可能ルームリストのコピー

    room_joiners.each_with_index do |user_id, index|
      user = User.find_by(open_id: user_id)
      next unless user

      room_id = find_available_room(available_rooms)

      if room_id.nil?
        room_id = "ROOM_#{Time.now.to_i}_#{created_rooms.size + index}"
        created_rooms << room_id
        available_rooms << room_id

        UserStatusService.update_status(user_id, "in_room", {
          room_id: room_id,
          joined_at: Time.now,
          ready: true,
          role: "creator",
          channel_type: "room_matching",
        })

        results << {
          user_id: user_id,
          name: user.name,
          rate: user.rate,
          room_id: room_id,
          ready: true,
          role: "creator",
          status: "in_room"
        }
      else
        UserStatusService.update_status(user_id, "in_room", {
          room_id: room_id,
          joined_at: Time.now,
          ready: false,
          role: "joiner",
          channel_type: "room_matching",
        })

        results << {
          user_id: user_id,
          name: user.name,
          rate: user.rate,
          room_id: room_id,
          ready: false,
          role: "joiner",
          status: "in_room"
        }

        available_rooms.delete(room_id)
      end
    end

    total_rooms = created_rooms.size
    creators_count = results.count { |r| r[:role] == "creator" }
    joiners_count = results.count { |r| r[:role] == "joiner" }

    UserStatusService.broadcast_to_admin

    render json: {
      success: true,
      message: "ルームマッチング完了: 最大2人のプレイヤーで#{total_rooms}個のルームが作成されました",
      results: results,
      rooms_created: total_rooms,
      logic: "#{creators_count}人のクリエイター（自動レディ）+ #{joiners_count}人のジョイナー（レディ必要）= #{total_rooms}個のルームに#{results.size}人のユーザー"
    }
  end

  def simulate_match_success
    matching_users = UserStatusService.get_users_by_status('matching')

    if matching_users.size < 2
      render json: { success: false, message: "Need at least 2 users in matching status" }
      return
    end

    user_pairs = matching_users.first(2)
    user1_id = user_pairs[0][0]
    user2_id = user_pairs[1][0]

    battle_room_id = "BATTLE_#{Time.now.to_i}"

    UserStatusService.update_status(user1_id, "matched", {
      opponent_id: user2_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 0,
      channel_type: user_pairs[0][1][:metadata][:channel_type] || "rank_matching",
    })

    UserStatusService.update_status(user2_id, "matched", {
      opponent_id: user1_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 1,
      channel_type: user_pairs[1][1][:metadata][:channel_type] || "rank_matching",
    })

    render json: {
      success: true,
      message: "Match created between #{user_pairs[0][1][:name]} and #{user_pairs[1][1][:name]}",
      battle_room_id: battle_room_id,
      players: [
        { id: user1_id, name: user_pairs[0][1][:name] },
        { id: user2_id, name: user_pairs[1][1][:name] }
      ]
    }
  end

  def clear_all_status
    # Redis-based clear all status
    begin
      # Get all active players
      active_players = Redis.current.smembers("active_players")

      # Remove all player sessions
      active_players.each do |player_id|
        UserStatusService.remove_status(player_id)
      end

      # Clear the active players set
      Redis.current.del("active_players")

      # Broadcast update
      UserStatusService.broadcast_to_admin

      render json: {
        success: true,
        message: "All user statuses cleared (#{active_players.size} users)"
      }
    rescue => e
      render json: {
        success: false,
        message: "Error clearing statuses: #{e.message}"
      }
    end
  end

  def toggle_ready
    user_id = params[:user_id]

    current_status = UserStatusService.get_status(user_id)
    if current_status && current_status[:status] == "in_room"
      # Redis data structure uses string keys
      current_ready = current_status[:metadata]['ready'] || false
      room_id = current_status[:metadata]['room_id']

      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        ready: !current_ready,
        role: current_status[:metadata]['role'],
        channel_type: "room_matching",
      })

      match_result = check_and_create_room_match(room_id)

      # Trigger broadcast after ready toggle
      UserStatusService.broadcast_to_admin

      if match_result[:match_created]
        render json: {
          success: true,
          message: "レディ状態が切り替えられ、マッチが作成されました！",
          ready: !current_ready,
          match_created: true,
          battle_room_id: match_result[:battle_room_id],
          players: match_result[:players]
        }
      else
        render json: {
          success: true,
          message: "ユーザー #{user_id} のレディ状態が切り替えられました",
          ready: !current_ready,
          match_created: false
        }
      end
    else
      render json: { success: false, message: "ユーザーがルームにいません" }
    end
  end

  def auto_ready_rooms
    rooms = UserStatusService.get_rooms_data
    matches_created = []

    rooms.each do |room_id, room_data|
      if room_data[:players].size == 2
        room_data[:players].each do |player|
          # Use player_id instead of id for Redis data structure
          UserStatusService.update_status(player[:player_id], "in_room", {
            room_id: room_id,
            ready: true,
            role: player[:role] || "player",
            channel_type: "room_matching",
          })
        end

        match_result = check_and_create_room_match(room_id)
        if match_result[:match_created]
          matches_created << match_result
        end
      end
    end

    # Broadcast update
    UserStatusService.broadcast_to_admin

    render json: {
      success: true,
      message: "Auto-readied all rooms and created #{matches_created.size} matches",
      matches_created: matches_created
    }
  end

  def disconnect_user
    user_id = params[:user_id]

    current_status = UserStatusService.get_status(user_id)
    if current_status
      UserStatusService.remove_status(user_id)

      render json: {
        success: true,
        message: "User #{user_id} disconnected and removed from all channels"
      }
    else
      render json: { success: false, message: "User not found in any channel" }
    end
  end

  def disconnect_all_users
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    disconnected_count = 0
    user_ids.each do |user_id|
      current_status = UserStatusService.get_status(user_id)
      if current_status
        UserStatusService.remove_status(user_id)
        disconnected_count += 1
      end
    end

    render json: {
      success: true,
      message: "#{disconnected_count} users disconnected from all channels"
    }
  end

  def test_broadcast
    UserStatusService.broadcast_to_admin

    render json: {
      success: true,
      message: "Broadcast triggered manually for testing",
      timestamp: Time.now.to_i
    }
  end

  private

  def find_available_room(available_rooms)
    available_rooms.each do |room_id|
      room_users = UserStatusService.get_users_by_status('in_room').select do |user_id, user_data|
        user_data[:metadata][:room_id] == room_id
      end

      if room_users.size < 2
        return room_id
      end
    end

    nil
  end

  def check_and_create_room_match(room_id)
    room_users = UserStatusService.get_users_by_status('in_room').select do |user_id, user_data|
      user_data[:metadata][:room_id] == room_id
    end

    if room_users.size == 2
      all_ready = room_users.all? { |user_id, user_data| user_data[:metadata][:ready] }

      if all_ready
        battle_room_id = "BATTLE_#{Time.now.to_i}_#{room_id}"

        user_pairs = room_users.to_a
        user1_id = user_pairs[0][0]
        user2_id = user_pairs[1][0]

        UserStatusService.update_status(user1_id, "matched", {
          opponent_id: user2_id,
          room_id: battle_room_id,
          matched_at: Time.now,
          index_in_room: 0,
          channel_type: "room_matching",
          from_room: room_id
        })

        UserStatusService.update_status(user2_id, "matched", {
          opponent_id: user1_id,
          room_id: battle_room_id,
          matched_at: Time.now,
          index_in_room: 1,
          channel_type: "room_matching",
          from_room: room_id
        })

        return {
          match_created: true,
          battle_room_id: battle_room_id,
          players: [
            { id: user1_id, name: user_pairs[0][1][:name] },
            { id: user2_id, name: user_pairs[1][1][:name] }
          ]
        }
      end
    end

    { match_created: false }
  end

  def generate_default_deck(user = nil)
    {
      cards: [
        { id: 1, count: 3 },
        { id: 2, count: 3 },
        { id: 3, count: 3 },
        { id: 4, count: 3 },
        { id: 5, count: 3 },
        { id: 6, count: 3 },
        { id: 7, count: 3 },
        { id: 8, count: 3 },
        { id: 9, count: 3 },
        { id: 10, count: 3 }
      ],
      deck_name: "Test Deck",
      created_at: Time.now
    }
  end
end
