class UserStatusesController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @stats = UserStatusService.get_stats
    @matching_users = UserStatusService.get_users_by_status('matching')
    @matched_users = UserStatusService.get_users_by_status('matched')
    @in_room_users = UserStatusService.get_users_by_status('in_room')

    # 各チャンネルタイプ別の統計を取得
    @rank_stats = {
      total: UserStatusService.count_by_channel_type('rank_matching'),
      matching: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching').size,
      matched: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched').size
    }

    @free_stats = {
      total: UserStatusService.count_by_channel_type('free_matching'),
      matching: UserStatusService.get_users_by_channel_and_status('free_matching', 'matching').size,
      matched: UserStatusService.get_users_by_channel_and_status('free_matching', 'matched').size
    }

    @room_stats = {
      total: UserStatusService.count_by_channel_type('room_matching'),
      rooms: UserStatusService.get_room_count
    }
  end

  # Các action mới cho từng loại trận đấu
  def rank_match
    @total_users = UserStatusService.count_by_channel_type('rank_matching')
    @matching_users = UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching')
    @matched_users = UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched')

    @stats = {
      total: @total_users,
      matching: @matching_users.size,
      matched: @matched_users.size
    }

    render :match_status
  end

  def free_match
    @total_users = UserStatusService.count_by_channel_type('free_matching')
    @matching_users = UserStatusService.get_users_by_channel_and_status('free_matching', 'matching')
    @matched_users = UserStatusService.get_users_by_channel_and_status('free_matching', 'matched')

    @stats = {
      total: @total_users,
      matching: @matching_users.size,
      matched: @matched_users.size
    }

    render :match_status
  end

  def room_match
    @total_users = UserStatusService.count_by_channel_type('room_matching')
    @room_count = UserStatusService.get_room_count
    @rooms = UserStatusService.get_rooms_data

    @stats = {
      total: @total_users,
      rooms: @room_count
    }

    render :room_status
  end

  def matching
    @users = UserStatusService.get_users_by_status('matching')
    @title = "ユーザーはマッチング相手を探している"
    render :user_list
  end

  def matched
    @users = UserStatusService.get_users_by_status('matched')
    @title = "ペアリングされたユーザー"
    render :user_list
  end

  def in_room
    @users = UserStatusService.get_users_by_status('in_room')
    @title = "ユーザーは部屋の中にいます"
    render :user_list
  end

  def all
    @statuses = UserStatusService.get_all_statuses
    @title = "すべてのユーザー"
  end

  def remove
    user_id = params[:user_id]
    if user_id.present? && UserStatusService.remove_status(user_id)
      redirect_to user_statuses_path, notice: "ユーザーステータスが削除されました#{user_id}"
    else
      redirect_to user_statuses_path, alert: "ユーザーステータスを削除できません#{user_id}"
    end
  end
end
