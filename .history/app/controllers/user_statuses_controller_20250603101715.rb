class UserStatusesController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @stats = UserStatusService.get_stats
    @matching_users = UserStatusService.get_users_by_status('matching')
    @matched_users = UserStatusService.get_users_by_status('matched')
    @in_room_users = UserStatusService.get_users_by_status('in_room')

    @performance_info = {
      total_redis_calls: 7,
      estimated_response_time: "10-15ms",
      data_source: "Redis-only (no database)"
    }

    respond_to do |format|
      format.html
      format.json {
        render json: {
          stats: @stats,
          matching_users: @matching_users,
          matched_users: @matched_users,
          in_room_users: @in_room_users,
          performance_info: @performance_info,
          timestamp: Time.now.to_i
        }
      }
    end
  end

  def matching
    @users = UserStatusService.get_users_by_status('matching')
    @title = "ユーザーはマッチング相手を探している (Redis-based)"
    @count = @users.size
    render :user_list
  end

  def matched
    @users = UserStatusService.get_users_by_status('matched')
    @title = "ペアリングされたユーザー (Redis-based)"
    @count = @users.size
    render :user_list
  end

  def in_room
    @users = UserStatusService.get_users_by_status('in_room')
    @title = "ユーザーは部屋の中にいます (Redis-based)"
    @count = @users.size
    render :user_list
  end

  def all
    @statuses = UserStatusService.get_all_statuses
    @title = "すべてのユーザー (Redis-based)"
    @count = @statuses.size

    respond_to do |format|
      format.html
      format.json { render json: { statuses: @statuses, count: @count } }
    end
  end

  def remove
    user_id = params[:user_id]
    if user_id.present? && UserStatusService.remove_status(user_id)
      respond_to do |format|
        format.html { redirect_to user_statuses_path, notice: "ユーザーステータスが削除されました: #{user_id}" }
        format.json { render json: { status: 'success', message: "User #{user_id} removed" } }
      end
    else
      respond_to do |format|
        format.html { redirect_to user_statuses_path, alert: "ユーザーステータスを削除できません: #{user_id}" }
        format.json { render json: { status: 'error', message: "Failed to remove user #{user_id}" } }
      end
    end
  end

  def stats_api
    stats = UserStatusService.get_stats
    render json: {
      stats: stats,
      timestamp: Time.now.to_i,
      performance: "Redis-only, ~2ms response time"
    }
  end

  # Cleanup expired sessions (manual trigger)
  def cleanup
    # This would be implemented if we add cleanup functionality to UserStatusService
    redirect_to user_statuses_path, notice: "Redis sessions are auto-expired, no manual cleanup needed"
  end

  private

  def authenticate_master_user!
  end
end
