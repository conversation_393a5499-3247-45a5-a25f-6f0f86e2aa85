require 'securerandom'

# UserStatusService のバッチ処理。ロック競合を減らしパフォーマンスを向上させる。
# メモリ内バッチ処理と Redis を使った分散バッチ処理の両方をサポートする
class BatchUserStatusService
  REDIS_LOCK_KEY = "batch_user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5秒

  # スレッドセーフなバッチモード用のストレージ
  @batch_mode = false
  @batch_updates = []
  @mutex = Mutex.new

  class << self
    attr_reader :batch_mode, :batch_updates, :mutex

    # メインのバッチ処理メソッド - 更新を集めて一括で処理する
    def batch_update(&block)
      # スレッドローカルストレージを使用して、並列バッチ処理をサポートする
      Thread.current[:batch_user_status_service] ||= { mode: false, updates: [] }
      batch_data = Thread.current[:batch_user_status_service]

      # ネストされたバッチ処理を防止
      if batch_data[:mode]
        log_warn("Nested batch_update detected, executing block directly")
        yield
        return
      end

      batch_data[:mode] = true
      batch_data[:updates] = []

      begin
        # ブロックを実行 - 更新が収集される
        yield

        # 収集されたすべての更新を一括で処理
        if batch_data[:updates].any?
          process_collected_updates(batch_data[:updates])
        end
      rescue => e
        log_error("Error in batch_update: #{e.message}")
        log_error(e.backtrace.join("\n"))
        raise
      ensure
        # スレッドローカルストレージをクリーンアップ
        batch_data[:mode] = false
        batch_data[:updates] = []
      end
    end

    # 状態を更新する - モードに応じてバッチまたは即時処理
    def update_status(user_id, status, metadata = {})
      batch_data = Thread.current[:batch_user_status_service]

      if batch_data&.dig(:mode)
        # バッチモード: 後で処理するために更新を収集
        batch_data[:updates] << {
          user_id: user_id,
          status: status,
          metadata: metadata,
          timestamp: Time.now
        }
        log_debug("Batched update for user #{user_id} → #{status}")
      else
        # 通常モード: UserStatusService を即時呼び出し
        UserStatusService.update_status(user_id, status, metadata)
        log_debug("Direct update for user #{user_id} → #{status}")
      end
    end

    private

    # 収集された更新を重複排除して Redis ロックで保護しながら処理
    def process_collected_updates(updates)
      return if updates.empty?

      log_info("BatchUserStatusService: Processing #{updates.size} collected updates")

      # user_id ごとにグループ化し、最新の更新のみを残す
      grouped_updates = updates.group_by { |update| update[:user_id] }
      final_updates = grouped_updates.map do |user_id, user_updates|
        # timestamp でソートして最新を選択
        user_updates.max_by { |update| update[:timestamp] }
      end

      log_info("BatchUserStatusService: Deduplicated to #{final_updates.size} unique user updates")

      # Redis ロックを使用して更新を安全に処理
      with_redis_lock do
        # original_update_status を使って個別のブロードキャストを避ける
        final_updates.each do |update|
          begin
            # Call original method to avoid broadcasting on each update
            if UserStatusService.respond_to?(:original_update_status)
              UserStatusService.original_update_status(
                update[:user_id],
                update[:status],
                update[:metadata]
              )
            else
              UserStatusService.update_status(
                update[:user_id],
                update[:status],
                update[:metadata]
              )
            end
            log_debug("Processed update for user #{update[:user_id]} → #{update[:status]}")
          rescue => e
            log_error("Failed to process update for user #{update[:user_id]}: #{e.message}")
            # 他の更新は継続して処理する
          end
        end

        # すべての更新完了後に一度だけブロードキャスト
        begin
          if UserStatusService.respond_to?(:broadcast_status_update)
            UserStatusService.broadcast_status_update
            log_debug("Broadcasted status updates for #{final_updates.size} users")
          end
        rescue => e
          log_error("Failed to broadcast status updates: #{e.message}")
        end
      end

      log_info("BatchUserStatusService: #{final_updates.size} 件の更新処理を完了")
    end

    # Redis ロックによるプロセス間同期処理
    def with_redis_lock(&block)
      return yield unless redis_available?

      lock_acquired = false
      lock_value = SecureRandom.hex(10)

      begin
        # Redis ロックの取得を試みる
        lock_acquired = Redis.current.set(REDIS_LOCK_KEY, lock_value, nx: true, px: LOCK_TIMEOUT)

        if lock_acquired
          log_debug("バッチ処理用の Redis ロックを取得しました")
          yield
        else
          log_warn("Redis ロックの取得に失敗。ロックなしで処理を継続します")
          yield
        end
      rescue Redis::BaseError => e
        log_error("Redis ロック操作中にエラーが発生: #{e.message}")
        yield
      ensure
        # ロックを取得していた場合のみリリース
        if lock_acquired
          release_redis_lock(lock_value)
        end
      end
    end

    # Lua スクリプトを使った Redis ロックの安全な解放
    def release_redis_lock(lock_value)
      return unless redis_available?

      lua_script = <<~LUA
        if redis.call('GET', KEYS[1]) == ARGV[1] then
          return redis.call('DEL', KEYS[1])
        else
          return 0
        end
      LUA

      begin
        result = Redis.current.eval(lua_script, keys: [REDIS_LOCK_KEY], argv: [lock_value])
        log_debug("Redis ロックを解放しました（結果: #{result}）")
      rescue Redis::BaseError => e
        log_error("Redis ロック解放中にエラー: #{e.message}")
      end
    end

    # Redis が利用可能かをチェック
    def redis_available?
      defined?(Redis) && Redis.respond_to?(:current) && Redis.current
    rescue
      false
    end

    # ログ出力メソッド
    def log_info(message)
      log_message(:info, message)
    end

    def log_warn(message)
      log_message(:warn, message)
    end

    def log_error(message)
      log_message(:error, message)
    end

    def log_debug(message)
      log_message(:debug, message)
    end

    def log_message(level, message)
      if defined?(Rails) && Rails.logger
        Rails.logger.send(level, "[BatchUserStatusService] #{message}")
      else
        puts "[#{level.upcase}] [BatchUserStatusService] #{message}"
      end
    end
  end
end
