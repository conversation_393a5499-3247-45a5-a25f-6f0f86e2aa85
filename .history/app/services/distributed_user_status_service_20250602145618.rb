# DEPRECATED: Use BatchUserStatusService instead
# This service has design issues and is kept for reference only
class DistributedUserStatusService
  REDIS_LOCK_KEY = "user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5 seconds

  class << self
    # DEPRECATED: Use BatchUserStatusService.update_status instead
    def update_status(user_id, status, metadata = {})
      Rails.logger.warn("DistributedUserStatusService is deprecated. Use BatchUserStatusService instead.")

      # Delegate to BatchUserStatusService for better implementation
      BatchUserStatusService.update_status(user_id, status, metadata)
    end

    # DEPRECATED: Use BatchUserStatusService.batch_update instead
    def batch_update(&block)
      Rails.logger.warn("DistributedUserStatusService.batch_update is deprecated. Use BatchUserStatusService.batch_update instead.")

      # Delegate to BatchUserStatusService for better implementation
      BatchUserStatusService.batch_update(&block)
    end
    
    private

    # DEPRECATED: Legacy methods kept for reference
    def with_redis_lock(key, timeout_ms)
      Rails.logger.warn("DistributedUserStatusService.with_redis_lock is deprecated")
      yield # Just execute the block without lock
    end

    def process_batch_updates
      Rails.logger.warn("DistributedUserStatusService.process_batch_updates is deprecated")
      # No-op
    end
  end
end
