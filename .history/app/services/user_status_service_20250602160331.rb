module UserStatusService
  @@mutex = Mutex.new
  @@user_statuses = Concurrent::Map.new

  class << self
    def update_status(user_id, status, metadata = {})
      return false unless user_id.present?

      @@mutex.synchronize do
        @@user_statuses[user_id] = {
          status: status,
          updated_at: Time.now,
          metadata: metadata
        }
      end
      true
    rescue => e
      Rails.logger.error("UserStatus: Error updating status for user #{user_id}: #{e.message}")
      false
    end

    def get_status(user_id)
      return nil unless user_id.present?

      @@mutex.synchronize do
        @@user_statuses[user_id]
      end
    rescue => e
      Rails.logger.error("UserStatus: Error getting status for user #{user_id}: #{e.message}")
      nil
    end

    def get_users_by_status(status)
      result = {}

      @@mutex.synchronize do
        @@user_statuses.each do |user_id, data|
          if data[:status].to_s == status.to_s
            user = User.find_by(open_id: user_id)
            result[user_id] = {
              name: user&.name,
              rate: 0,
              status: data[:status],
              updated_at: data[:updated_at],
              metadata: data[:metadata]
            }
          end
        end
      end

      result
    end

    def get_stats
      stats = {
        total: 0,
        by_status: {}
      }

      @@mutex.synchronize do
        @@user_statuses.each do |_, data|
          status = data[:status].to_s

          stats[:total] += 1
          stats[:by_status][status] ||= 0
          stats[:by_status][status] += 1
        end
      end

      stats
    end

    def get_all_statuses
      result = {}

      @@mutex.synchronize do
        @@user_statuses.each do |user_id, data|
          result[user_id] = {
            status: data[:status],
            updated_at: data[:updated_at],
            metadata: data[:metadata]
          }
        end
      end

      result
    end

    def handle_disconnect(user_id)
      return false unless user_id.present?

      @@mutex.synchronize do
        current_data = @@user_statuses[user_id]
        return false unless current_data
        Rails.logger.info("UserStatus: User #{user_id} disconnected while matching, changed to connected")

        # current_time = Time.now
        # current_status = current_data[:status]

        # if current_status == "matching"
        #   @@user_statuses[user_id] = {
        #     status: "connected",
        #     updated_at: current_time,
        #     metadata: {
        #       disconnected_at: current_time,
        #       previous_status: current_status
        #     }
        #   }
        #   Rails.logger.info("UserStatus: User #{user_id} disconnected while matching, changed to connected")
        #   return true
        # elsif ["matched", "in_room"].include?(current_status)
        #   @@user_statuses.delete(user_id)
        #   Rails.logger.info("UserStatus: User #{user_id} disconnected while in game, status removed")
        #   return true
        # else
        #   @@user_statuses[user_id] = {
        #     status: "connected",
        #     updated_at: current_time,
        #     metadata: {
        #       disconnected_at: current_time,
        #       previous_status: current_status
        #     }
        #   }
        #   Rails.logger.info("UserStatus: User #{user_id} disconnected, changed to connected")
        #   return true
        # end
        @@user_statuses.delete(user_id)
      end
      false
    rescue => e
      Rails.logger.error("UserStatus: Error handling disconnect for user #{user_id}: #{e.message}")
      false
    end

    def count_by_status(status)
      count = 0
      
      @@mutex.synchronize do
        @@user_statuses.each do |_, data|
          count += 1 if data[:status].to_s == status.to_s
        end
      end
      
      count
    end
  end

  def self.broadcast_to_admin
    stats = get_stats
    matching_users = get_users_by_status('matching')
    matched_users = get_users_by_status('matched')
    in_room_users = get_users_by_status('in_room')

    data = {
      matching: matching_users,
      matched: matched_users,
      in_room: in_room_users,
      stats: stats,
      timestamp: Time.now.to_i
    }
    
    ActionCable.server.broadcast("admin_user_status_updates", data)
  end
  
  class << self
    alias_method :original_broadcast_status_update, :broadcast_status_update if method_defined?(:broadcast_status_update)
    
    def broadcast_status_update
      original_broadcast_status_update if method_defined?(:original_broadcast_status_update)
      
      broadcast_to_admin
    end
    
    alias_method :original_update_status, :update_status
    
    def update_status(user_id, status, metadata = {})
      result = original_update_status(user_id, status, metadata)
      
      if result
        broadcast_status_update
      end
      
      result
    end
    
    alias_method :original_handle_disconnect, :handle_disconnect
    
    def handle_disconnect(user_id)
      result = original_handle_disconnect(user_id)
      
      broadcast_status_update
      
      result
    end
  end

  def self.get_user_metadata(user_id)
    user_status = @@user_statuses[user_id.to_s]
    return nil unless user_status
    
    user_status[:metadata]
  end
end
