# Batch UserStatusService to reduce lock contention and improve performance
# Supports both in-memory batching and Redis-based distributed batching
class BatchUserStatusService
  REDIS_LOCK_KEY = "batch_user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5 seconds

  # Thread-safe storage for batch mode
  @batch_mode = false
  @batch_updates = []
  @mutex = Mutex.new

  class << self
    attr_reader :batch_mode, :batch_updates, :mutex

    # Main batch processing method - collects updates and processes them atomically
    def batch_update(&block)
      # Use thread-local storage to support concurrent batches
      Thread.current[:batch_user_status_service] ||= { mode: false, updates: [] }
      batch_data = Thread.current[:batch_user_status_service]

      # Prevent nested batching
      if batch_data[:mode]
        log_warn("Nested batch_update detected, executing block directly")
        yield
        return
      end

      batch_data[:mode] = true
      batch_data[:updates] = []

      begin
        # Execute the block - updates will be collected
        yield

        # Process all collected updates atomically
        if batch_data[:updates].any?
          process_collected_updates(batch_data[:updates])
        end
      rescue => e
        log_error("Error in batch_update: #{e.message}")
        log_error(e.backtrace.join("\n"))
        raise
      ensure
        # Clean up thread-local storage
        batch_data[:mode] = false
        batch_data[:updates] = []
      end
    end

    # Update status - either batch or direct depending on mode
    def update_status(user_id, status, metadata = {})
      batch_data = Thread.current[:batch_user_status_service]

      if batch_data&.dig(:mode)
        # Batch mode: collect update for later processing
        batch_data[:updates] << {
          user_id: user_id,
          status: status,
          metadata: metadata,
          timestamp: Time.now
        }
        log_debug("Batched update for user #{user_id} → #{status}")
      else
        # Direct mode: call UserStatusService immediately
        UserStatusService.update_status(user_id, status, metadata)
        log_debug("Direct update for user #{user_id} → #{status}")
      end
    end

    private

    def with_redis_lock(&block)
      lock_acquired = false
      lock_value = SecureRandom.hex(10)

      begin
        lock_acquired = Redis.current.set(REDIS_LOCK_KEY, lock_value, nx: true, px: LOCK_TIMEOUT)
        if lock_acquired
          yield
        else
          log_info("BatchUserStatusService: Failed to acquire Redis lock, skipping batch processing")
        end
      ensure
        if lock_acquired
          lua_script = <<~LUA
            if redis.call('GET', KEYS[1]) == ARGV[1] then
              return redis.call('DEL', KEYS[1])
            else
              return 0
            end
          LUA
          Redis.current.eval(lua_script, keys: [REDIS_LOCK_KEY], argv: [lock_value])
        end
      end
    end

    def log_info(msg)
      if defined?(Rails) && Rails.logger
        Rails.logger.info(msg)
      else
        puts msg
      end
    end
  end
end
