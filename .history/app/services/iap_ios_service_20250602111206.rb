class IapIosService
    require 'net/http'
    require 'uri'
    require 'json'
    require 'jwt'
    require 'openssl'

    # jws認証用
    APPLE_JWKS_URL = 'https://appleid.apple.com/auth/keys'

    @@apple_public_keys = nil
    @@apple_public_keys_fetched_at = nil

    APP_STORE_SERVER_API_URL = 'https://api.storekit.itunes.apple.com/inApps/v1'
    APP_STORE_SERVER_API_SANDBOX_URL = 'https://api.storekit-sandbox.itunes.apple.com/inApps/v1'

    IOS_BUNDLE_ID = 'com.neconome.mythologiatheoracle.t1'

    @@jwt_token = nil
    @@jwt_token_expiration = 0

    def self.iap_ios(user, params)
        transaction_id = params[:transaction_id]
        product_id = params[:product_id]

        if IapIos.exists?(transaction_data: transaction_id)
            raise StandardError.new("already_processed_purchase")
        end

        verification_result = verify_transaction(transaction_id)
        unless verification_result[:success]
            raise StandardError.new("invalid_transaction")
        end

        Rails.logger.info(category: "iap_ios_service.iap_ios", user: user.open_id, product_id: product_id, transaction_id: transaction_id)
        handle_jws(user,verification_result[:data]['signedTransactionInfo'])

        rescue => e
            ErrorService.handle_exception(e, "IapIosService", "iap_ios")
    end

    def self.handle_jws(user,jws)
        transaction_info = verify_and_decode_jws(jws)
        return { error: true, message: "通知の署名検証に失敗しました" } unless transaction_info
        save_transaction(user, transaction_info)
    end

    # appleサーバーからのS2S通知を受け取る処理(継続型の課金処理)
    def self.handle_s2s_notification(params)
        signed_payload = params[:signedPayload]
        return { error: true, message: "署名データがありません" } unless signed_payload

        begin
            payload = verify_and_decode_jws(signed_payload)
            raise StandardError.new("failed_to_verify_notification") unless payload

            notification_type = payload['notificationType']
            transaction_info_encoded = payload['data']['signedTransactionInfo']

            Rails.logger.info(category: "iap_ios_service.s2s_notification", notification_type: notification_type)

            transaction_info = verify_and_decode_jws(transaction_info_encoded)
            raise StandardError.new("failed_to_verify_transaction_info") unless transaction_info

            # トランザクションIDを取得
            transaction_id = transaction_info['transactionId']
            original_transaction_id = transaction_info['originalTransactionId']
            product_id = transaction_info['productId']

            # ユーザーを検索
            user = find_user_by_transaction(original_transaction_id)
            if user.nil?
                Rails.logger.warn("ユーザーが見つかりません: #{original_transaction_id}")
                return { success: false, message: "ユーザーが見つかりません" }
            end
            Rails.logger.info(category: "iap_ios_service.handle_s2s_notification", user: user.open_id, product_id: product_id, transaction_id: transaction_id)

            # 購入の時にもS2S通知は来るが、購入時の処理はiap_iosで行っているため、ここではDID_RENEWのみ処理する
            case notification_type
            when 'DID_RENEW' # 更新時はoriginalTransactionIdは初回購入時と同じでtransactionIdが変わる
                save_transaction(user, transaction_info)
            when 'REFUND', 'CANCEL'
                # 既存のIapIosレコードを更新
                iap_data = IapIos.find_by(original_transaction_id: original_transaction_id)

                if iap_data
                    iap_data.update(
                        is_refunded: true,
                        refund_date: Time.zone.now,
                        auto_renew: false,
                        notification_type: notification_type
                    )
                end
                # ユーザーのサブスクリプション状態を更新
                pass_product_keys = ConstantService::PASS_PRODUCT_KEYS rescue ["pack_pass", "analysis_pass"]
                analysis_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[1]) || {}
                pack_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[0]) || {}

                if product_id == pass_product_keys[0] # pack_pass
                    SaveDataService.update_user_save_data_hash?(user, "shop", {
                        passes: {
                            pass_product_keys[0] => {is_active: pack_pass_info["is_active"] || false, expires_date: pack_pass_info["expires_date"] || "",auto_renew: false},
                            pass_product_keys[1] => {is_active: analysis_pass_info["is_active"] || false, expires_date: analysis_pass_info["expires_date"] || "",auto_renew: analysis_pass_info["auto_renew"] || false}
                        }
                    })
                elsif product_id == pass_product_keys[1] # analysis_pass
                    SaveDataService.update_user_save_data_hash?(user, "shop", {
                        passes: {
                            pass_product_keys[0] => {is_active: pack_pass_info["is_active"] || false, expires_date: pack_pass_info["expires_date"] || "",auto_renew: pack_pass_info["auto_renew"] || false},
                            pass_product_keys[1] => {is_active: analysis_pass_info["is_active"] || false, expires_date: analysis_pass_info["expires_date"] || "",auto_renew: false}
                        }
                    })
                end
                # 返金やキャンセルが来てもその月はそのまま通す。その場合はログイン時のexpires_dateで勝手にfalseに変わる
            when 'DID_FAIL_TO_RENEW'
                # 更新に失敗した場合
                iap_data = IapIos.find_by(original_transaction_id: original_transaction_id)
                if iap_data
                    iap_data.update(
                        auto_renew: false,
                        notification_type: notification_type
                    )
                end
            when 'EXPIRED'
                # 期限切れの場合
                ShopPassService.check_pass_active(user)
            else
                Rails.logger.warn("未対応の通知タイプ: #{notification_type}")
            end
            user.save! if user
            { success: true }
        rescue => e
            ErrorService.handle_exception(e, "IapIosService", "handle_s2s_notification")
        end
    end

    private

    def self.save_transaction(user, transaction_info)
        ActiveRecord::Base.transaction do
            transaction_id = transaction_info['transactionId']

            return { error: true, message: "既に処理済みのトランザクションです" } if IapIos.exists?(transaction_data: transaction_id)

            original_transaction_id = transaction_info['originalTransactionId']
            product_id = transaction_info['productId']
            purchase_date = Time.zone.at(transaction_info['purchaseDate'] / 1000).to_datetime
            quantity = transaction_info['quantity'] || 1
            type = transaction_info['type']
            expires_date = transaction_info['expiresDate'] ? Time.zone.at(transaction_info['expiresDate'] / 1000).to_datetime : nil
            auto_renew = type == "Auto-Renewable Subscription"

            iap_data = IapIos.new(
                platform: 0,
                product: product_id,
                open_id: user.open_id,
                transaction_data: transaction_id,
                buy_date: purchase_date,
                quantity: quantity,
                original_transaction_id: original_transaction_id,
                expires_date: expires_date,
                auto_renew: auto_renew,
                notification_type: "INITIAL_PURCHASE"
            )
            result = nil
            # 継続型の課金処理
            if type == "Auto-Renewable Subscription"
                result = IapService.subscription_action(user,product_id,expires_date)
            else # 消費型の課金処理
                result = IapService.bought_process(user, product_id, "ios")
            end
            user.save!
            iap_data.save!
            result
        end
        rescue => e
            ErrorService.handle_exception(e, "IapIosService", "save_transaction")
    end

    # jwtはhttps://developer.apple.com/documentation/appstoreserverapi/generating-json-web-tokens-for-api-requestsを参照
    def self.generate_jwt_token
        now = Time.now.to_i
        if @@jwt_token && (@@jwt_token_expiration - 60 > now)
            return @@jwt_token
        end
        private_key_string_pem = Rails.application.credentials.apple[:app_store_connect_private_key]
        private_key = OpenSSL::PKey::EC.new(private_key_string_pem)
        key_id = Rails.application.credentials.apple[:app_store_connect_key_id]
        issuer_id = Rails.application.credentials.apple[:app_store_connect_issuer_id]

        payload = {
            iss: issuer_id,
            iat: now,
            exp: now + 600, # 有効期間は600秒
            aud: 'appstoreconnect-v1',
            bid: IOS_BUNDLE_ID
        }

        headers = {
            kid: key_id,
            alg: 'ES256',
            typ: 'JWT'
        }

        token = JWT.encode(payload, private_key, 'ES256', headers)

        @@jwt_token = token
        @@jwt_token_expiration = now + 600

        token
        rescue => e
            Rails.logger.error("JWT token generation error: #{e.message}")
        nil
    end

    def self.verify_transaction(transaction_id)
        base_url = APP_STORE_SERVER_API_SANDBOX_URL
        endpoint = "/transactions/#{transaction_id}"

        response = send_request(base_url, endpoint)

        if response[:code] == '404'
            base_url = APP_STORE_SERVER_API_URL
            response = send_request(base_url, endpoint)
        end

        if response[:code] == '200'
            # "data"の中にjwsが入っている
            { success: true, data: JSON.parse(response[:body]) }
        else
            { success: false, message: "トランザクション検証失敗: #{response[:body]}" }
        end
    end

    def self.send_request(base_url, endpoint)
        uri = URI.parse("#{base_url}#{endpoint}")
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true
        request = Net::HTTP::Get.new(uri.request_uri)
        request['Authorization'] = "Bearer #{generate_jwt_token}"

        response = http.request(request)
        { code: response.code, body: response.body }
    end

    def self.find_user_by_transaction(original_transaction_id)
        iap = IapIos.find_by(original_transaction_id: original_transaction_id) || 
              IapIos.find_by(transaction_data: original_transaction_id)
        return nil unless iap
        User.find_by(open_id: iap.open_id)
    end

    # jwsの署名検証を行う
    def self.verify_and_decode_jws(jws)
        header = JWT.decode(jws, nil, false)[1]

        x5c = header["x5c"]
        raise StandardError.new("x5c_not_found") unless x5c && x5c[0]

        # 証明書をデコードして公開鍵を取得
        certificate_pem = "-----BEGIN CERTIFICATE-----\n#{x5c[0].scan(/.{1,64}/).join("\n")}\n-----END CERTIFICATE-----"
        certificate = OpenSSL::X509::Certificate.new(certificate_pem)
        public_key = certificate.public_key

        JWT.decode(jws, public_key, true, { algorithm: 'ES256' })[0]
    rescue => e
        Rails.logger.error("JWS検証失敗: #{e.message}")
        nil
    end

    # アップルの公開鍵を取得する 使わないかも
    def self.find_apple_public_key(kid)
        refresh_apple_public_keys if @@apple_public_keys.nil? || Time.now - @@apple_public_keys_fetched_at > 3600
        key_data = @@apple_public_keys.find { |k| k['kid'] == kid }
        return nil unless key_data

        # rubyで使える鍵の形式に変換
        OpenSSL::PKey::EC.new(
        JWT::JWK.import(key_data).key
        )
    rescue => e
        Rails.logger.error("Apple公開鍵取得失敗: #{e.message}")
        nil
    end

    # アップルの公開鍵を更新する
    def self.refresh_apple_public_keys
        uri = URI.parse(APPLE_JWKS_URL)
        response = Net::HTTP.get(uri)
        data = JSON.parse(response)
        @@apple_public_keys = data['keys']
        @@apple_public_keys_fetched_at = Time.now
    rescue => e
        Rails.logger.error("Apple公開鍵リフレッシュ失敗: #{e.message}")
        @@apple_public_keys = nil
    end
end
