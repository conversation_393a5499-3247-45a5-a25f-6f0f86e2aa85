# Redis-based Player Session Service for high performance and low load
# すべてRedisから取得して表示、データベースは使わない方式
class RedisPlayerSessionService
  # Redis key patterns
  SESSION_KEY_PREFIX = "player_session"
  ACTIVE_PLAYERS_SET = "active_players"
  BATTLE_MODES_SET = "battle_modes"

  # Session expiration (30 minutes)
  SESSION_EXPIRE = 1800

  class << self
    # プレイヤーセッション作成 (connection時)
    def create_session(player_id, initial_data = {})
      session_key = "#{SESSION_KEY_PREFIX}_#{player_id}"

      default_session = {
        player_id: player_id,
        battle_mode: nil,
        battle_c: false,
        rank_match_channel: false,
        free_match_channel: false,
        room_match_channel: false,
        status: "connected",
        connected_at: Time.now.to_i,
        last_activity: Time.now.to_i,
        metadata: {}
      }.merge(initial_data)

      # Redis operations
      Redis.current.multi do |multi|
        multi.hset(session_key, default_session)
        multi.expire(session_key, SESSION_EXPIRE)
        multi.sadd(ACTIVE_PLAYERS_SET, player_id)
      end

      log_info("Created session for player #{player_id}")
      default_session
    rescue => e
      log_error("Failed to create session for player #{player_id}: #{e.message}")
      nil
    end
    
    # プレイヤーセッション更新
    def update_session(player_id, updates = {})
      session_key = "#{SESSION_KEY_PREFIX}_#{player_id}"
      
      # Add timestamp
      updates[:last_activity] = Time.now.to_i
      
      # Update Redis hash
      Redis.current.multi do |multi|
        multi.hset(session_key, updates)
        multi.expire(session_key, SESSION_EXPIRE)
      end
      
      log_debug("Updated session for player #{player_id}: #{updates}")
      true
    rescue => e
      log_error("Failed to update session for player #{player_id}: #{e.message}")
      false
    end
    
    # プレイヤーセッション取得
    def get_session(player_id)
      session_key = "#{SESSION_KEY_PREFIX}_#{player_id}"
      session_data = Redis.current.hgetall(session_key)
      
      return nil if session_data.empty?
      
      # Convert string keys to symbols and parse values
      parsed_session = {}
      session_data.each do |key, value|
        parsed_key = key.to_sym
        parsed_session[parsed_key] = parse_value(value)
      end
      
      parsed_session
    rescue => e
      log_error("Failed to get session for player #{player_id}: #{e.message}")
      nil
    end
    
    # プレイヤーセッション削除 (disconnect時)
    def destroy_session(player_id)
      session_key = "#{SESSION_KEY_PREFIX}_#{player_id}"
      
      Redis.current.multi do |multi|
        multi.del(session_key)
        multi.srem(ACTIVE_PLAYERS_SET, player_id)
      end
      
      log_info("Destroyed session for player #{player_id}")
      true
    rescue => e
      log_error("Failed to destroy session for player #{player_id}: #{e.message}")
      false
    end
    
    # 全アクティブプレイヤー取得 (管理画面用)
    def get_all_active_sessions
      active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
      sessions = {}
      
      # Batch get all sessions
      if active_players.any?
        session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }
        
        # Pipeline for performance
        results = Redis.current.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
        
        active_players.each_with_index do |player_id, index|
          session_data = results[index]
          next if session_data.empty?
          
          # Parse session data
          parsed_session = {}
          session_data.each do |key, value|
            parsed_key = key.to_sym
            parsed_session[parsed_key] = parse_value(value)
          end
          
          sessions[player_id] = parsed_session
        end
      end
      
      sessions
    rescue => e
      log_error("Failed to get all active sessions: #{e.message}")
      {}
    end
    
    # ステータス別プレイヤー取得
    def get_players_by_status(status)
      all_sessions = get_all_active_sessions
      all_sessions.select { |player_id, session| session[:status] == status }
    end
    
    # バトルモード別プレイヤー取得
    def get_players_by_battle_mode(battle_mode)
      all_sessions = get_all_active_sessions
      all_sessions.select { |player_id, session| session[:battle_mode] == battle_mode }
    end
    
    # 統計情報取得 (管理画面用)
    def get_stats
      all_sessions = get_all_active_sessions
      
      stats = {
        total_active: all_sessions.size,
        by_status: {},
        by_battle_mode: {},
        by_channel: {
          rank_match: 0,
          free_match: 0,
          room_match: 0
        }
      }
      
      all_sessions.each do |player_id, session|
        # Count by status
        status = session[:status] || "unknown"
        stats[:by_status][status] ||= 0
        stats[:by_status][status] += 1
        
        # Count by battle mode
        battle_mode = session[:battle_mode] || "none"
        stats[:by_battle_mode][battle_mode] ||= 0
        stats[:by_battle_mode][battle_mode] += 1
        
        # Count by channel
        stats[:by_channel][:rank_match] += 1 if session[:rank_match_channel]
        stats[:by_channel][:free_match] += 1 if session[:free_match_channel]
        stats[:by_channel][:room_match] += 1 if session[:room_match_channel]
      end
      
      stats
    end
    
    # 期限切れセッションのクリーンアップ
    def cleanup_expired_sessions
      active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
      expired_count = 0
      
      active_players.each do |player_id|
        session_key = "#{SESSION_KEY_PREFIX}_#{player_id}"
        exists = Redis.current.exists?(session_key)
        
        unless exists
          Redis.current.srem(ACTIVE_PLAYERS_SET, player_id)
          expired_count += 1
        end
      end
      
      log_info("Cleaned up #{expired_count} expired sessions") if expired_count > 0
      expired_count
    rescue => e
      log_error("Failed to cleanup expired sessions: #{e.message}")
      0
    end
    
    private
    
    # Parse Redis string values to appropriate types
    def parse_value(value)
      case value
      when "true"
        true
      when "false"
        false
      when /^\d+$/
        value.to_i
      when /^\d+\.\d+$/
        value.to_f
      else
        value
      end
    end
    
    # Logging methods
    def log_info(message)
      log_message(:info, message)
    end
    
    def log_debug(message)
      log_message(:debug, message)
    end
    
    def log_error(message)
      log_message(:error, message)
    end
    
    def log_message(level, message)
      if defined?(Rails) && Rails.logger
        Rails.logger.send(level, "[RedisPlayerSessionService] #{message}")
      else
        puts "[#{level.upcase}] [RedisPlayerSessionService] #{message}"
      end
    end
  end
end
