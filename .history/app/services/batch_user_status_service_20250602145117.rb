require 'securerandom'

# Batch UserStatusService to reduce lock contention and improve performance
# Supports both in-memory batching and Redis-based distributed batching
class BatchUserStatusService
  REDIS_LOCK_KEY = "batch_user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5 seconds

  # Thread-safe storage for batch mode
  @batch_mode = false
  @batch_updates = []
  @mutex = Mutex.new

  class << self
    attr_reader :batch_mode, :batch_updates, :mutex

    # Main batch processing method - collects updates and processes them atomically
    def batch_update(&block)
      # Use thread-local storage to support concurrent batches
      Thread.current[:batch_user_status_service] ||= { mode: false, updates: [] }
      batch_data = Thread.current[:batch_user_status_service]

      # Prevent nested batching
      if batch_data[:mode]
        log_warn("Nested batch_update detected, executing block directly")
        yield
        return
      end

      batch_data[:mode] = true
      batch_data[:updates] = []

      begin
        # Execute the block - updates will be collected
        yield

        # Process all collected updates atomically
        if batch_data[:updates].any?
          process_collected_updates(batch_data[:updates])
        end
      rescue => e
        log_error("Error in batch_update: #{e.message}")
        log_error(e.backtrace.join("\n"))
        raise
      ensure
        # Clean up thread-local storage
        batch_data[:mode] = false
        batch_data[:updates] = []
      end
    end

    # Update status - either batch or direct depending on mode
    def update_status(user_id, status, metadata = {})
      batch_data = Thread.current[:batch_user_status_service]

      if batch_data&.dig(:mode)
        # Batch mode: collect update for later processing
        batch_data[:updates] << {
          user_id: user_id,
          status: status,
          metadata: metadata,
          timestamp: Time.now
        }
        log_debug("Batched update for user #{user_id} → #{status}")
      else
        # Direct mode: call UserStatusService immediately
        UserStatusService.update_status(user_id, status, metadata)
        log_debug("Direct update for user #{user_id} → #{status}")
      end
    end

    private

    # Process collected updates with deduplication and Redis lock protection
    def process_collected_updates(updates)
      return if updates.empty?

      log_info("BatchUserStatusService: Processing #{updates.size} collected updates")

      # Deduplicate by user_id, keeping the latest update for each user
      grouped_updates = updates.group_by { |update| update[:user_id] }
      final_updates = grouped_updates.map do |user_id, user_updates|
        # Sort by timestamp and take the latest
        user_updates.max_by { |update| update[:timestamp] }
      end

      log_info("BatchUserStatusService: Deduplicated to #{final_updates.size} unique user updates")

      # Process updates with Redis lock for cross-process safety
      with_redis_lock do
        final_updates.each do |update|
          begin
            UserStatusService.update_status(
              update[:user_id],
              update[:status],
              update[:metadata]
            )
            log_debug("Processed update for user #{update[:user_id]} → #{update[:status]}")
          rescue => e
            log_error("Failed to process update for user #{update[:user_id]}: #{e.message}")
            # Continue processing other updates even if one fails
          end
        end
      end

      log_info("BatchUserStatusService: Completed processing #{final_updates.size} updates")
    end

    # Redis lock implementation for cross-process synchronization
    def with_redis_lock(&block)
      return yield unless redis_available?

      lock_acquired = false
      lock_value = SecureRandom.hex(10)

      begin
        # Try to acquire Redis lock
        lock_acquired = Redis.current.set(REDIS_LOCK_KEY, lock_value, nx: true, px: LOCK_TIMEOUT)

        if lock_acquired
          log_debug("Acquired Redis lock for batch processing")
          yield
        else
          log_warn("Failed to acquire Redis lock, processing without lock protection")
          # Fallback: process without lock (better than not processing at all)
          yield
        end
      rescue Redis::BaseError => e
        log_error("Redis error during lock operation: #{e.message}")
        # Fallback: process without lock if Redis fails
        yield
      ensure
        # Release lock only if we acquired it
        if lock_acquired
          release_redis_lock(lock_value)
        end
      end
    end

    # Atomic lock release using Lua script
    def release_redis_lock(lock_value)
      return unless redis_available?

      lua_script = <<~LUA
        if redis.call('GET', KEYS[1]) == ARGV[1] then
          return redis.call('DEL', KEYS[1])
        else
          return 0
        end
      LUA

      begin
        result = Redis.current.eval(lua_script, keys: [REDIS_LOCK_KEY], argv: [lock_value])
        log_debug("Released Redis lock (result: #{result})")
      rescue Redis::BaseError => e
        log_error("Error releasing Redis lock: #{e.message}")
      end
    end

    # Check if Redis is available
    def redis_available?
      defined?(Redis) && Redis.respond_to?(:current) && Redis.current
    rescue
      false
    end

    # Logging methods with different levels
    def log_info(message)
      log_message(:info, message)
    end

    def log_warn(message)
      log_message(:warn, message)
    end

    def log_error(message)
      log_message(:error, message)
    end

    def log_debug(message)
      log_message(:debug, message)
    end

    def log_message(level, message)
      if defined?(Rails) && Rails.logger
        Rails.logger.send(level, "[BatchUserStatusService] #{message}")
      else
        puts "[#{level.upcase}] [BatchUserStatusService] #{message}"
      end
    end
  end
end
