require 'json'

module UserStatusService
  def self.redis_connection
    if defined?(REDIS)
      REDIS
    else
      @mock_redis ||= MockRedisPool.new
    end
  end

  class MockRedisPool
    def initialize
      @data = {}
      @sets = {}
      @mutex = Mutex.new
    end

    def with
      yield(MockRedisClient.new(@data, @sets, @mutex))
    end
  end

  class MockRedisClient
    def initialize(data, sets, mutex)
      @data = data
      @sets = sets
      @mutex = mutex
    end

    def multi
      yield(self)
    end

    def hset(key, hash)
      @mutex.synchronize do
        @data[key] = hash.transform_keys(&:to_s).transform_values(&:to_s)
      end
    end

    def hgetall(key)
      @mutex.synchronize do
        @data[key] || {}
      end
    end

    def hget(key, field)
      @mutex.synchronize do
        @data[key]&.dig(field.to_s)
      end
    end

    def expire(key, seconds)
      true
    end

    def sadd(set_key, member)
      @mutex.synchronize do
        @sets[set_key] ||= Set.new
        @sets[set_key].add(member.to_s)
      end
    end

    def srem(set_key, member)
      @mutex.synchronize do
        @sets[set_key]&.delete(member.to_s)
      end
    end

    def smembers(set_key)
      @mutex.synchronize do
        (@sets[set_key] || Set.new).to_a
      end
    end

    def del(key)
      @mutex.synchronize do
        @data.delete(key)
        @sets.delete(key)
      end
    end

    def pipelined
      results = []
      yield(PipelineMock.new(results, @data, @sets, @mutex))
      results
    end

    class PipelineMock
      def initialize(results, data, sets, mutex)
        @results = results
        @data = data
        @sets = sets
        @mutex = mutex
      end

      def hgetall(key)
        @results << (@data[key] || {})
      end

      def hget(key, field)
        @results << @data[key]&.dig(field.to_s)
      end
    end
  end

  SESSION_KEY_PREFIX = "player_session"
  ACTIVE_PLAYERS_SET = "active_players"
  SESSION_EXPIRE = 1800

  def self.update_status(user_id, status, metadata = {})
    return false if user_id.nil? || user_id.to_s.empty? || status.nil? || status.to_s.empty?

    session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

    begin
      session_data = {
        'player_id' => user_id.to_s,
        'status' => status.to_s,
        'updated_at' => Time.now.to_i.to_s,
        'metadata' => metadata.to_json
      }

      redis_connection.with do |redis|
        redis.multi do |multi|
          multi.hset(session_key, session_data)
          multi.expire(session_key, SESSION_EXPIRE)
          multi.sadd(ACTIVE_PLAYERS_SET, user_id)
        end
      end

      log_debug("Updated status for user #{user_id} → #{status}")
      true
    rescue => e
      log_error("Error updating status for user #{user_id}: #{e.message}")
      false
    end
  end

  def self.get_status(user_id)
    return nil if user_id.nil? || user_id.to_s.empty?

    session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

    begin
      session_data = redis_connection.with { |redis| redis.hgetall(session_key) }
      return nil if session_data.empty?

      {
        player_id: session_data['player_id'],
        status: session_data['status'],
        updated_at: Time.at(session_data['updated_at'].to_i),
        metadata: JSON.parse(session_data['metadata'] || '{}')
      }
    rescue => e
      log_error("Error getting status for user #{user_id}: #{e.message}")
      nil
    end
  end

  # Redis-based get users by status (high performance)
  def self.get_users_by_status(status)
    result = {}

    begin
      # Get all active players from Redis
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return result if active_players.empty?

      # Pipeline for performance - get all sessions at once
      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
      end

      # Filter by status and build result (NO DATABASE QUERIES)
      active_players.each_with_index do |player_id, index|
        session_data = sessions[index]
        next if session_data.empty?

        if session_data['status'] == status.to_s
          result[player_id] = {
            player_id: player_id,
            status: session_data['status'],
            updated_at: Time.at(session_data['updated_at'].to_i),
            metadata: JSON.parse(session_data['metadata'] || '{}')
          }
        end
      end
    rescue => e
      log_error("Error getting users by status #{status}: #{e.message}")
    end

    result
  end

  # Redis-based statistics (high performance)
  def self.get_stats
    stats = {
      total: 0,
      by_status: {}
    }

    begin
      # Get all active players from Redis
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      stats[:total] = active_players.size

      return stats if active_players.empty?

      # Pipeline to get all statuses efficiently
      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'status') }
        end
      end

      # Count by status
      sessions.each do |status|
        next if status.nil? || status.empty?

        stats[:by_status][status] ||= 0
        stats[:by_status][status] += 1
      end
    rescue => e
      log_error("Error getting stats: #{e.message}")
    end

    stats
  end

  # Redis-based all statuses (for admin dashboard)
  def self.get_all_statuses
    result = {}

    begin
      # Get all active players
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return result if active_players.empty?

      # Pipeline for performance
      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
      end

      # Build result hash
      active_players.each_with_index do |player_id, index|
        session_data = sessions[index]
        next if session_data.empty?

        result[player_id] = {
          status: session_data['status'],
          updated_at: Time.at(session_data['updated_at'].to_i),
          metadata: JSON.parse(session_data['metadata'] || '{}')
        }
      end
    rescue => e
      log_error("Error getting all statuses: #{e.message}")
    end

    result
  end

  # Redis-based disconnect handling
  def self.handle_disconnect(user_id)
    return false unless user_id && !user_id.to_s.empty?

    session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

    begin
      # Remove from Redis
      redis_connection.with do |redis|
        redis.multi do |multi|
          multi.del(session_key)
          multi.srem(ACTIVE_PLAYERS_SET, user_id)
        end
      end

      log_info("User #{user_id} disconnected, session removed from Redis")
      true
    rescue => e
      log_error("Error handling disconnect for user #{user_id}: #{e.message}")
      false
    end
  end

  # Redis-based count by status
  def self.count_by_status(status)
    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return 0 if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      statuses = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'status') }
        end
      end

      statuses.count { |s| s == status.to_s }
    rescue => e
      log_error("Error counting by status #{status}: #{e.message}")
      0
    end
  end

  # Add remove_status method for controller compatibility
  def self.remove_status(user_id)
    handle_disconnect(user_id)
  end

  # Optimized broadcast for admin dashboard (Redis-based)
  def self.broadcast_to_admin
    begin
      stats = get_stats

      # Get users by status efficiently (single Redis call per status)
      matching_users = get_users_by_status('matching')
      matched_users = get_users_by_status('matched')
      in_room_users = get_users_by_status('in_room')

      data = {
        matching: matching_users,
        matched: matched_users,
        in_room: in_room_users,
        stats: stats,
        timestamp: Time.now.to_i
      }

      ActionCable.server.broadcast("admin_user_status_updates", data)
      log_debug("Broadcasted admin update with #{stats[:total]} total users")
    rescue => e
      log_error("Error broadcasting to admin: #{e.message}")
    end
  end

  # Broadcast methods for specific channel types
  def self.broadcast_rank_match_data
    begin
      total_users = count_by_channel_type('rank_matching')
      matching_users = get_users_by_channel_and_status('rank_matching', 'matching')
      matched_users = get_users_by_channel_and_status('rank_matching', 'matched')

      stats = {
        total: total_users,
        matching: matching_users.size,
        matched: matched_users.size
      }

      data = {
        matching_users: matching_users,
        matched_users: matched_users,
        stats: stats,
        timestamp: Time.now.to_i
      }

      ActionCable.server.broadcast("admin_rank_match_status", data)
      log_debug("Broadcasted rank match data: #{stats[:total]} total users")
    rescue => e
      log_error("Error broadcasting rank match data: #{e.message}")
    end
  end

  def self.broadcast_free_match_data
    begin
      total_users = count_by_channel_type('free_matching')
      matching_users = get_users_by_channel_and_status('free_matching', 'matching')
      matched_users = get_users_by_channel_and_status('free_matching', 'matched')

      stats = {
        total: total_users,
        matching: matching_users.size,
        matched: matched_users.size
      }

      data = {
        matching_users: matching_users,
        matched_users: matched_users,
        stats: stats,
        timestamp: Time.now.to_i
      }

      ActionCable.server.broadcast("admin_free_match_status", data)
      log_debug("Broadcasted free match data: #{stats[:total]} total users")
    rescue => e
      log_error("Error broadcasting free match data: #{e.message}")
    end
  end

  def self.broadcast_room_match_data
    begin
      total_users = count_by_channel_type('room_matching')
      room_count = get_room_count
      rooms = get_rooms_data

      stats = {
        total: total_users,
        rooms: room_count
      }

      data = {
        rooms: rooms,
        stats: stats,
        timestamp: Time.now.to_i
      }

      ActionCable.server.broadcast("admin_room_match_status", data)
      log_debug("Broadcasted room match data: #{stats[:total]} total users, #{room_count} rooms")
    rescue => e
      log_error("Error broadcasting room match data: #{e.message}")
    end
  end

  # Redis-based channel-specific methods
  def self.get_users_by_channel_type(channel_type)
    result = {}

    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return result if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
      end

      active_players.each_with_index do |player_id, index|
        session_data = sessions[index]
        next if session_data.empty?

        metadata = JSON.parse(session_data['metadata'] || '{}')
        if metadata['channel_type'] == channel_type
          result[player_id] = {
            player_id: player_id,
            status: session_data['status'],
            updated_at: Time.at(session_data['updated_at'].to_i),
            metadata: metadata
          }
        end
      end
    rescue => e
      log_error("Error getting users by channel type #{channel_type}: #{e.message}")
    end

    result
  end

  def self.get_users_by_channel_and_status(channel_type, status)
    result = {}

    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return result if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
      end

      active_players.each_with_index do |player_id, index|
        session_data = sessions[index]
        next if session_data.empty?

        metadata = JSON.parse(session_data['metadata'] || '{}')
        if metadata['channel_type'] == channel_type && session_data['status'] == status.to_s
          result[player_id] = {
            player_id: player_id,
            status: session_data['status'],
            updated_at: Time.at(session_data['updated_at'].to_i),
            metadata: metadata
          }
        end
      end
    rescue => e
      log_error("Error getting users by channel and status #{channel_type}/#{status}: #{e.message}")
    end

    result
  end

  def self.count_by_channel_type(channel_type)
    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return 0 if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      metadatas = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'metadata') }
        end
      end

      metadatas.count do |metadata_json|
        next false if metadata_json.nil? || metadata_json.empty?
        metadata = JSON.parse(metadata_json)
        metadata['channel_type'] == channel_type
      end
    rescue => e
      log_error("Error counting by channel type #{channel_type}: #{e.message}")
      0
    end
  end

  def self.get_room_count
    rooms = Set.new

    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return 0 if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      metadatas = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'metadata') }
        end
      end

      metadatas.each do |metadata_json|
        next if metadata_json.nil? || metadata_json.empty?
        metadata = JSON.parse(metadata_json)
        if metadata['channel_type'] == 'room_matching' && metadata['room_id']
          rooms.add(metadata['room_id'])
        end
      end
    rescue => e
      log_error("Error getting room count: #{e.message}")
    end

    rooms.size
  end

  def self.get_rooms_data
    rooms = {}

    begin
      active_players = redis_connection.with { |redis| redis.smembers(ACTIVE_PLAYERS_SET) }
      return rooms if active_players.empty?

      session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

      sessions = redis_connection.with do |redis|
        redis.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end
      end

      active_players.each_with_index do |player_id, index|
        session_data = sessions[index]
        next if session_data.empty?

        metadata = JSON.parse(session_data['metadata'] || '{}')
        if metadata['channel_type'] == 'room_matching' && metadata['room_id']
          room_id = metadata['room_id']

          rooms[room_id] ||= {
            players: [],
            updated_at: Time.at(session_data['updated_at'].to_i)
          }

          rooms[room_id][:players] << {
            id: player_id,
            player_id: player_id,
            ready: metadata['ready'] || false,
            role: metadata['role'],
            updated_at: Time.at(session_data['updated_at'].to_i)
          }

          room_updated_at = Time.at(session_data['updated_at'].to_i)
          if room_updated_at > rooms[room_id][:updated_at]
            rooms[room_id][:updated_at] = room_updated_at
          end
        end
      end
    rescue => e
      log_error("Error getting rooms data: #{e.message}")
    end

    rooms
  end

  # Simplified broadcast method (no automatic broadcasting on every update)
  def self.broadcast_status_update
    broadcast_to_admin
  end

  # Get user metadata from Redis
  def self.get_user_metadata(user_id)
    session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

    begin
      metadata_json = redis_connection.with { |redis| redis.hget(session_key, 'metadata') }
      return nil if metadata_json.nil? || metadata_json.empty?

      JSON.parse(metadata_json)
    rescue => e
      log_error("Error getting user metadata for #{user_id}: #{e.message}")
      nil
    end
  end

  private

  # Logging methods
  def self.log_info(message)
    log_message(:info, message)
  end

  def self.log_debug(message)
    log_message(:debug, message)
  end

  def self.log_error(message)
    log_message(:error, message)
  end

  def self.log_message(level, message)
    if defined?(Rails) && Rails.logger
      Rails.logger.send(level, "[UserStatusService] #{message}")
    else
      puts "[#{level.upcase}] [UserStatusService] #{message}"
    end
  end
end