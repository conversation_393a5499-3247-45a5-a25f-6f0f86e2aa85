# Distributed UserStatusService with Redis locks for multi-worker safety
class DistributedUserStatusService
  REDIS_LOCK_KEY = "user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5 seconds
  
  class << self
    def update_status(user_id, status, metadata = {})
      # Use Redis lock for cross-process synchronization
      with_redis_lock(REDIS_LOCK_KEY, LOCK_TIMEOUT) do
        UserStatusService.update_status(user_id, status, metadata)
      end
    rescue => e
      Rails.logger.error("DistributedUserStatusService error: #{e.message}")
      # Fallback to direct call if Redis lock fails
      UserStatusService.update_status(user_id, status, metadata)
    end
    
    def batch_update(&block)
      @batch_mode = true
      @batch_updates = []
      
      begin
        yield
        
        # Process all updates with single Redis lock
        if @batch_updates.any?
          with_redis_lock(REDIS_LOCK_KEY, LOCK_TIMEOUT) do
            process_batch_updates
          end
        end
      ensure
        @batch_mode = false
        @batch_updates = []
      end
    end
    
    private
    
    def with_redis_lock(key, timeout_ms)
      lock_acquired = false
      lock_value = SecureRandom.hex(10)
      
      begin
        # Try to acquire lock
        lock_acquired = Redis.current.set(key, lock_value, nx: true, px: timeout_ms)
        
        if lock_acquired
          yield
        else
          Rails.logger.warn("Failed to acquire Redis lock for UserStatusService")
          # Could implement retry logic here
          raise "Lock acquisition failed"
        end
      ensure
        # Release lock only if we acquired it
        if lock_acquired
          # Use Lua script for atomic check-and-delete
          lua_script = <<~LUA
            if redis.call('GET', KEYS[1]) == ARGV[1] then
              return redis.call('DEL', KEYS[1])
            else
              return 0
            end
          LUA
          
          Redis.current.eval(lua_script, keys: [key], argv: [lock_value])
        end
      end
    end
    
    def process_batch_updates
      return if @batch_updates.empty?
      
      Rails.logger.info("DistributedUserStatusService: Processing #{@batch_updates.size} updates")
      
      # Group updates by user_id to avoid duplicate updates
      grouped_updates = @batch_updates.group_by { |update| update[:user_id] }
      
      # Process each user's final status
      grouped_updates.each do |user_id, updates|
        # Use the last update for each user
        final_update = updates.last
        UserStatusService.update_status(final_update[:user_id], final_update[:status], final_update[:metadata])
      end
      
      Rails.logger.info("DistributedUserStatusService: Completed #{grouped_updates.size} unique user updates")
    end
  end
end
