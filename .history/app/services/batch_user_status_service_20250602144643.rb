require 'securerandom'
class BatchUserStatusService
  REDIS_LOCK_KEY = "batch_user_status_service_lock"
  LOCK_TIMEOUT = 5000 # 5 seconds

  class << self
    def batch_update(&block)
      @batch_mode = true
      @batch_updates = []

      begin
        yield

        # Process all updates in a single batch with Redis lock for multi-worker safety
        if @batch_updates.any?
          with_redis_lock do
            process_batch_updates
          end
        end
      ensure
        @batch_mode = false
        @batch_updates = []
      end
    end

    def update_status(user_id, status, metadata = {})
      if @batch_mode
        @batch_updates << { user_id: user_id, status: status, metadata: metadata }
      else
        UserStatusService.update_status(user_id, status, metadata)
      end
    end

    private

    def process_batch_updates
      return if @batch_updates.empty?

      log_info("BatchUserStatusService: Processing #{@batch_updates.size} updates")

      # Group updates by user_id to avoid duplicate updates
      grouped_updates = @batch_updates.group_by { |update| update[:user_id] }

      # Process each user's final status
      grouped_updates.each do |user_id, updates|
        # Use the last update for each user
        final_update = updates.last
        UserStatusService.update_status(final_update[:user_id], final_update[:status], final_update[:metadata])
      end

      log_info("BatchUserStatusService: Completed #{grouped_updates.size} unique user updates")
    end

    def log_info(message)
      if defined?(Rails) && Rails.logger
        Rails.logger.info(message)
      else
        puts message
      end
    end

    def with_redis_lock(&block)
      lock_acquired = false
      lock_value = SecureRandom.hex(10)

      begin
        # Try to acquire Redis lock for cross-process synchronization
        if defined?(Redis) && Redis.respond_to?(:current)
          lock_acquired = Redis.current.set(REDIS_LOCK_KEY, lock_value, nx: true, px: LOCK_TIMEOUT)
        end

        if lock_acquired
          yield
        else
          log_info("BatchUserStatusService: Failed to acquire Redis lock, falling back to direct processing")
          # Fallback to direct processing without Redis lock
          yield
        end
      rescue => e
        log_info("BatchUserStatusService: Redis lock error: #{e.message}, falling back to direct processing")
        # Fallback to direct processing if Redis fails
        yield
      ensure
        # Release lock only if we acquired it
        if lock_acquired && defined?(Redis) && Redis.respond_to?(:current)
          begin
            # Use Lua script for atomic check-and-delete
            lua_script = <<~LUA
              if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
              else
                return 0
              end
            LUA

            Redis.current.eval(lua_script, keys: [REDIS_LOCK_KEY], argv: [lock_value])
          rescue => e
            log_info("BatchUserStatusService: Error releasing Redis lock: #{e.message}")
          end
        end
      end
    end
  end
end
