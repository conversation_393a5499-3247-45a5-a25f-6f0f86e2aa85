class BattleChannel < ApplicationCable::Channel
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end

    # ロックの状態をチェックするヘルパーメソッド
    def check_lock_exists(key)
      begin
        # Redisに直接問い合わせてロック情報を取得
        lock_key = "redlock:#{key}"
        value = redis.get(lock_key)
        if value
          DEBUG("BattleChannel", "Lock exists for #{key}: #{value}")
          true
        else
          false
        end
      rescue => e
        ERROR("BattleChannel", "Error checking lock: #{e.message}")
        false
      end
    end
  end
  # 終了理由の定数
  RESULT_REASONS = {
    0 => "none",
    1 => "life_0",
    2 => "deck_hand_0",
    3 => "turn_limit",
    4 => "surrender",
    5 => "disconnected",
  }

  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  # Redisヘルパーメソッド
  def redis_get(key)
    self.class.redis.get(key)
  rescue Redis::BaseError => e
    self.class.ERROR("BattleChannel", "Redis error when getting #{key}: #{e.message}")
    nil
  end

  def redis_set(key, value, expire = nil)
    self.class.redis.set(key, value)
    if expire
      self.class.redis.expire(key, expire)
    end
    true
  rescue Redis::BaseError => e
    self.class.ERROR("BattleChannel", "Redis error when setting #{key}: #{e.message}")
    false
  end

  def redis_del(key)
    result = self.class.redis.del(key)
    # バトルルーム削除時はSetからも削除
    if key.start_with?("battle_room:")
      room_id = key.sub("battle_room:", "")
      self.class.redis.srem("battle_rooms", room_id)
    end
    result
  rescue Redis::BaseError => e
    self.class.ERROR("BattleChannel", "Redis error when deleting #{key}: #{e.message}")
    false
  end

  # より安全なwith_lockヘルパー - TTLを短くして接続解放を確実に行う
  def with_lock(lock_key, ttl = nil)
    settings = self.class.get_env_settings
    start_time = Time.now
    success = false
    retry_count = 0
    max_retries = settings[:retry_count]

    # ロックが存在しているか確認（デバッグ用）
    self.class.check_lock_exists(lock_key)

    # TTLを適切に設定（指定がない場合はデフォルト値を使用）
    ttl = ttl || settings[:default_ttl]
    # 短いTTLに強制的に調整（長すぎるTTLを防止）
    ttl = [ ttl, settings[:max_lock_ttl] ].min

    # 処理全体のタイムアウト（TTLの3倍を目安）
    timeout = ttl * 3
    timeout_time = start_time + (timeout / 1000.0)

    while !success && retry_count < max_retries && Time.now < timeout_time
      begin
        # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
        remaining_time = (timeout_time - Time.now) * 1000
        if remaining_time < ttl
          adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
          self.class.WARN("BattleChannel", "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
          ttl = adjusted_ttl
        end

        self.class.redlock.lock(lock_key, ttl) do |locked|
          if locked
            begin
              self.class.DEBUG("BattleChannel", "Lock acquired for #{lock_key}")
              success = true
              yield
            rescue => e
              self.class.ERROR("BattleChannel", "Error in lock block for #{lock_key}: #{e.message}")
              self.class.ERROR("BattleChannel", e.backtrace.join("\n"))
              success = false
            ensure
              # ロック処理が長すぎる場合は警告
              duration = ((Time.now - start_time) * 1000).to_i
              if duration > ttl / 2
                self.class.WARN("BattleChannel", "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
              end
            end
          else
            retry_count += 1
            # 環境に応じたリトライ間隔を使用
            sleep_time = settings[:retry_interval]
            self.class.WARN("BattleChannel", "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
            sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
          end
        end
      rescue => e
        retry_count += 1
        self.class.ERROR("BattleChannel", "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
        sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
      end
    end

    # タイムアウトした場合の処理
    if Time.now >= timeout_time && !success
      self.class.ERROR("BattleChannel", "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
    end

    if !success && retry_count >= max_retries
      self.class.ERROR("BattleChannel", "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
    end

    success
  end

  def subscribed
  end

  def join_room
    self.class.DEBUG("subscribed", "params: #{params}", room_id)
    self.class.DEBUG("subscribed", "Player #{player_id} Try subscribed to room", room_id)
    if params[:spectator].to_s == "true"
      self.class.DEBUG("subscribed", "1")
      spectator_subscribed
    elsif params[:spectator].to_s == "false"
      self.class.DEBUG("subscribed", "2")
      player_subscribed
    end
  end

  def unsubscribed
    handle_with_rescue("接続解除処理") do
      self.class.DEBUG("Connection", "Player #{player_id} disconnected from room", room_id)
      # プレイヤーの切断処理
      if params[:spectator].to_s == "true"
      elsif params[:spectator].to_s == "false"
        room = find_room
        player_id = connection.get_player_id

        if room
          current_room_id = room[:room_id]

          metadata = UserStatusService.get_user_metadata(player_id)
          from_room_match = metadata && metadata[:from_room_match]

          if from_room_match
            UserStatusService.update_status(player_id, "in_room", {
              room_id:     current_room_id,
              returned_at: Time.now,
              from_battle: true,
            })
          else
            UserStatusService.update_status(player_id, "connected", {
              left_at: Time.now,
              room_id: current_room_id,
              reason:  "battle_ended",
            }) if connection_alive?
          end

          handle_player_disconnection(room)
        end
      end
    end
  end

  def set_deck_data0(data)
    handle_set_deck_data(player_index: 0, data: data)
  end

  def set_deck_data1(data)
    handle_set_deck_data(player_index: 1, data: data)
  end

  def handle_set_deck_data(player_index:, data:)
    with_lock("lock:battle_room:#{room_id}") do
      room = find_room
      room["deck_#{player_index}"] = data["text"]["deck_data"]
      room["p#{player_index}lv"] = data["text"]["level"]
      room["p#{player_index}Icid"] = data["text"]["icon_id"]
      room["p#{player_index}Frid"] = data["text"]["frame_id"]
      room["p#{player_index}Tiid"] = data["text"]["title_id"]
      self.class.DEBUG("SetDeckData#{player_index}", "Setting deck data for player #{player_index} #{data["text"]}", room_id)

      self.class.DEBUG("SetDeckData#{player_index}", "Setting deck data for player #{player_index} #{room["deck_#{player_index}"].present?}", room_id)
      save_room(room)
      self.class.DEBUG("SetDeckData#{player_index}", "Setting deck data for player #{player_index} #{room["deck_#{player_index}"].present?}", room_id)

      start_room_if_ready
    end
  end

  def battle_input0(data)
    handle_battle_input(player_index: 0, inputdata: data)
  end

  def battle_input1(data)
    handle_battle_input(player_index: 1, inputdata: data)
  end

  def handle_battle_input(player_index:, inputdata:)
    with_lock("lock:battle_room:#{room_id}") do
      self.class.DEBUG("BattleInput#{player_index}", "Battle input #{player_index} #{inputdata}", room_id)
      self.class.DEBUG("BattleInput#{player_index}", "Battle input #{inputdata[:text]}", room_id)

      room = find_room

      # roomがnilの場合のエラーハンドリング
      if room.nil?
        self.class.ERROR("BattleInput#{player_index}", "Room not found for room_id: #{room_id}", room_id)
        return
      end

      input_text = inputdata["text"]

      # input_textがnilの場合のエラーハンドリング
      if input_text.nil?
        self.class.ERROR("BattleInput#{player_index}", "Input text is nil for inputdata: #{inputdata}", room_id)
        return
      end

      # play_inputがnilの場合のエラーハンドリング
      if input_text["play_input"].nil?
        self.class.ERROR("BattleInput#{player_index}", "play_input is nil for input_text: #{input_text}", room_id)
        return
      end

      # input_data_arrayの初期化（存在しない場合）
      room[:input_data_array] ||= []

      if input_text["play_input"]["InputType"] == 3
        message = {
          command: "Player#{player_index}BattleInput",
          body:    input_text,
        }
        ActionCable.server.broadcast("battle_room:#{room_id}", message)
        room[:input_data_array] << input_text
      else
        room[:"player_#{player_index}_input_data"] = input_text
        room[:input_data_array] << input_text
        check_all_input(room)
      end

      save_room(room)
    end
  end

  def check_all_input(room)
    if room[:player_0_input_data].present? && room[:player_1_input_data].present?
      message0 = {
        command: "Player0BattleInput",
        body:    room[:player_0_input_data],
      }
      message1 = {
        command: "Player1BattleInput",
        body:    room[:player_1_input_data],
      }
      ActionCable.server.broadcast("battle_room:#{room_id}", message0)
      ActionCable.server.broadcast("battle_room:#{room_id}", message1)
      self.class.DEBUG("CheckAllInput", "All input data sent", message0)
      self.class.DEBUG("CheckAllInput", "All input data sent", message1)
      room[:player_0_input_data] = nil
      room[:player_1_input_data] = nil
      save_room(room)
    end
  end

  def emote_input0(data)
    handle_emote_input(player_index: 0, data: data)
  end

  def emote_input1(data)
    handle_emote_input(player_index: 1, data: data)
  end

  def handle_emote_input(player_index:, data:)
    message = {
      command: "Player#{player_index}EmoteInput",
      body:    data["text"],
    }
    self.class.DEBUG("Emote", "Player#{player_index} sent emote", room_id)
    ActionCable.server.broadcast("battle_room:#{room_id}", message)
  end

  # NONE,
  # Life0, // ライフが0
  # DeckHand0, // デッキと手札が0枚
  # TurnLimig, // ターン数上限
  # Surrender, // 降参 # サーバーからのデータ
  # Disconnected, // 切断 # サーバーからのデータ
  def finish_input0(inputdata)
    handle_finish_input(player_index: 0, inputdata: inputdata)
  end

  def finish_input1(inputdata)
    handle_finish_input(player_index: 1, inputdata: inputdata)
  end

  def handle_finish_input(player_index:, inputdata:)
    with_lock("lock:battle_room:#{room_id}") do
      room = find_room
      return if room.nil?

      reason = inputdata["text"]["finish_reason"]
      room[:reason] = reason

      if [ 4, 5 ].include?(reason)
        message = {
          command: "Player#{player_index}FinishInput",
          body:    {
            winner:        0,
            finish_reason: reason,
          },
        }
        self.class.DEBUG("FinishInput#{player_index}", "Player#{player_index} finish input #{message}", room_id)
        ActionCable.server.broadcast("battle_room:#{room_id}", message)
        room[:"finish_result_#{player_index}"] = inputdata["text"]["winner"]
      else
        self.class.DEBUG("FinishInput#{player_index}", "Player#{player_index} finish input #{inputdata}", room_id)
        room[:"finish_result_#{player_index}"] = inputdata["text"]["winner"]
      end

      if player_index == 0
        if room[:finish_result_0].present?
          self.class.DEBUG("FinishInput#{player_index}", "Player#{player_index} finish input #{room[:finish_result_0]}", room_id)
          room[:finish_result_0] == 0 ? room[:result] = "win_0" : room[:result] = "win_1"
        end
      elsif player_index == 1
        if room[:finish_result_1].present?
          self.class.DEBUG("FinishInput#{player_index}", "Player#{player_index} finish input #{room[:finish_result_1]}", room_id)
          room[:finish_result_1] == 0 ? room[:result] = "win_1" : room[:result] = "win_0"
        end
      else
        room[:result] = "draw"
      end
      archive_and_close_room(room)
    end
  end

  private

    def spectator_subscribed
      if find_room
        stream_from "battle_room:#{room_id}"
        self.class.DEBUG("subscribed", "Player #{player_id} Name: #{params[:name]} joined room as spectator", room_id)
      else
        reject_with_error("ESBC-0001", "Room not found")
      end
    end

    def player_subscribed
      if params[:index_in_room].to_s == "0"
        set_player0_id
      elsif params[:index_in_room].to_s == "1"
        set_player1_id
      else
        self.class.ERROR("UnknownError", "不明のエラー #{params[:index_in_room]}", room_id)
        reject_with_error("ESBC-0004", "不明のエラー")
      end
      transmit({
        command: "PlayerJoined",
      })
    end

    def set_player0_id
      handle_set_player_id(player_index: 0)
    end

    def set_player1_id
      handle_set_player_id(player_index: 1)
    end

    def handle_set_player_id(player_index:)
      # Database query OUTSIDE lock to avoid lock contention
      user = User.find_by(open_id: player_id)
      player_rate = user&.rate || 0

      success = with_lock("lock:battle_room:#{room_id}", 2000) do
        room = create_or_get_room
        if room[:game_state] == "playing"
          self.class.ERROR("RoomAlreadyStarted", "Room already started #{params}")# 再接続の実装予定
          reject_with_error("ESBC-0002", "Room already started #{params}")
          false
        else
          stream_from "battle_room:#{room_id}"
          room["player_#{player_index}_id"] = player_id
          room["matching_time_#{player_index}"] = params[:timecount]
          room["player_#{player_index}_name"] = params[:name]
          room["before_rate_#{player_index}"] = player_rate
          self.class.DEBUG("SetPlayer#{player_index}Id", "Setting player #{player_index} id #{player_id.present?}", room_id)
          self.class.DEBUG("SetPlayer#{player_index}Id", "Setting player #{player_index} id #{room["player_#{player_index}_id"].present?}", room_id)
          self.class.DEBUG("Player#{player_index}Joined", "Player#{player_index} #{player_id} joined room #{room_id}")

          save_room(room)
          true
        end
      end

      unless success
        self.class.ERROR("LockError", "player#{player_index}登録ロック取得失敗: #{room_id}")
        reject_with_error("ESBC-009#{player_index}", "接続競合により登録に失敗しました")
      end
    end

    def find_room
      json = redis_get("battle_room:#{room_id}")
      return nil if json.nil?
      JSON.parse(json, symbolize_names: true)
    end

    def create_or_get_room
      room = nil
      success = with_lock("lock:battle_room:#{room_id}:create", 2000) do
        room = find_room_or_nil || create_new_room
        true
      end

      unless success
        self.class.ERROR("LockError", "ルーム作成ロック取得失敗: #{room_id}")
        raise "ルーム作成競合：リトライしてください"
      end

      room
    end

    def find_room_or_nil
      json = redis_get("battle_room:#{room_id}")
      if json
        self.class.DEBUG("RoomLoad", "ルーム読み込み: #{room_id}")
        JSON.parse(json, symbolize_names: true)
      else
        nil
      end
    end

    def create_new_room
      room = {
        room_id:             room_id,
        random_seed:         0,
        player_0_id:         nil,
        player_1_id:         nil,
        player_0_name:       nil,
        player_1_name:       nil,
        p0lv:                0,
        p1lv:                0,
        p0Icid:              0,
        p1Icid:              0,
        p0Frid:              0,
        p1Frid:              0,
        p0Tiid:              0,
        p1Tiid:              0,
        deck_0:              nil,
        deck_1:              nil,
        matching_time_0:     0,
        matching_time_1:     0,
        before_rate_0:       0,
        before_rate_1:       0,
        after_rate_0:        0,
        after_rate_1:        0,
        finish_result_0:     nil,
        finish_result_1:     nil,
        reason:              "no_reason",
        result:              "no_result",
        played_at:           Time.now,
        game_mode:           params[:game_mode],
        event_id:            "0",
        player_0_input_data: nil,
        player_1_input_data: nil,
        input_data_array:    [],
        game_state:          "preparing",
      }
      save_room(room)
      # バトルルームをSetに追加（パフォーマンス改善）
      self.class.redis.sadd("battle_rooms", room_id)
      self.class.INFO("RoomCreated", "ルーム作成: #{room_id}")
      room
    end

    def start_room_if_ready
      room = find_room
      return unless room_ready?(room)

      start_battle(room)
    end

    def room_ready?(room)
      room[:player_0_id].present? && room[:player_1_id].present? &&
        room[:deck_0].present? && room[:deck_1].present?
    end

    def start_battle(room)
      room[:random_seed] = Random.new_seed % (2**31)
      message = {
        command:     "PrepareData",
        Player0ID:   room[:player_0_id],
        Player0Deck: room[:deck_0],
        P0Name:      room[:player_0_name],
        P0Lv:        room[:p0lv],
        P0Icid:      room[:p0Icid],
        P0Frid:      room[:p0Frid],
        P0Tiid:      room[:p0Tiid],
        P0Rate:      room[:before_rate_0],
        Player1ID:   room[:player_1_id],
        Player1Deck: room[:deck_1],
        P1Name:      room[:player_1_name],
        P1Lv:        room[:p1lv],
        P1Icid:      room[:p1Icid],
        P1Frid:      room[:p1Frid],
        P1Tiid:      room[:p1Tiid],
        P1Rate:      room[:before_rate_1],
        RandomSeed:  room[:random_seed],
      }
      ActionCable.server.broadcast("battle_room:#{room_id}", message)
      room[:game_state] = "playing"
      save_room(room)
      self.class.DEBUG("GameStart", "Battle started room_id: #{room_id}")
    end

    def handle_player_disconnection(room)
      # 切断データ
      disconnect_data = {
        "text" => {
          "winner"        => 1,
          "finish_reason" => 5,
        },
      }
      if room[:player_0_id].to_s == player_id.to_s
        self.class.DEBUG("Disconnect", "Player0 disconnected", room_id)
        finish_input0(disconnect_data)
      elsif room[:player_1_id].to_s == player_id.to_s
        self.class.DEBUG("Disconnect", "Player1 disconnected", room_id)
        finish_input1(disconnect_data)
      else
        self.class.DEBUG("Disconnect", "Spectator player disconnected", room_id)
      end
    end

  private
    # ヘルパーメソッド
    def room_id
      params[:room_id].to_s
    end

    def player_id
      connection.get_player_id
    end

    def save_room(room)
      # roomのLIFETIMEを1日（86400秒）に設定
      redis_set("battle_room:#{room_id}", room.to_json, 3600)
    end

    def handle_with_rescue(operation_name)
      yield
    rescue => e
      self.class.ERROR("Exception", "Error in #{operation_name}: #{e.message}", room_id)
      self.class.ERROR("Backtrace", "#{e.backtrace.join("\n")}", room_id)
      false
    end

    def archive_and_close_room(room)
      room[:game_state] = "finished"

      # Database queries OUTSIDE any locks to avoid lock contention
      user0 = User.find_by(open_id: room[:player_0_id])
      user1 = User.find_by(open_id: room[:player_1_id])

      if room[:result] == "win_0"
        if user0.present?
          update_chest_win_count(user0, 0, room)
        end

        if room[:game_mode] == "rank"
          # プレイヤー0の勝利時
          if room[:before_rate_0] && room[:before_rate_0] >= 10000
            room[:after_rate_0] = room[:before_rate_0] + EloCalculatorService.delta_win(room[:before_rate_0], room[:before_rate_1])
          else
            rate_table = MasterDatum.get_rank_rate_table(room[:before_rate_0]) if room[:before_rate_0]
            if rate_table && rate_table["WinDiff"]
              room[:after_rate_0] = room[:before_rate_0] + rate_table["WinDiff"]
            else
              room[:after_rate_0] = room[:before_rate_0] || 0
            end
          end

          if room[:before_rate_1] && room[:before_rate_1] >= 10000
            room[:after_rate_1] = room[:before_rate_1] + EloCalculatorService.delta_lose(room[:before_rate_1], room[:before_rate_0])
          else
            rate_table = MasterDatum.get_rank_rate_table(room[:before_rate_1]) if room[:before_rate_1]
            if rate_table && rate_table["LoseDiff"]
              room[:after_rate_1] = room[:before_rate_1] - rate_table["LoseDiff"]
            else
              room[:after_rate_1] = room[:before_rate_1] || 0
            end
          end
          room[:after_rate_1] = RateStopperService.apply_stopper(room[:after_rate_1], room[:before_rate_1])

          if user0.present?
            user0.rate = room[:after_rate_0]
            user0.wins += 1
            user0.save!
          end

          if user1.present?
            user1.rate = room[:after_rate_1]
            user1.save!
          end
        end
      elsif room[:result] == "win_1"
        # user1 already fetched above
        if user1.present?
          update_chest_win_count(user1, 1, room)
        end

        if room[:game_mode] == "rank"
          # プレイヤー1の勝利時
          if room[:before_rate_1] && room[:before_rate_1] >= 10000
            room[:after_rate_1] = room[:before_rate_1] + EloCalculatorService.delta_win(room[:before_rate_1], room[:before_rate_0])
          else
            rate_table = MasterDatum.get_rank_rate_table(room[:before_rate_1]) if room[:before_rate_1]
            if rate_table && rate_table["WinDiff"]
              room[:after_rate_1] = room[:before_rate_1] + rate_table["WinDiff"]
            else
              room[:after_rate_1] = room[:before_rate_1] || 0
            end
          end

          if room[:before_rate_0] && room[:before_rate_0] >= 10000
            room[:after_rate_0] = room[:before_rate_0] + EloCalculatorService.delta_lose(room[:before_rate_0], room[:before_rate_1])
          else
            rate_table = MasterDatum.get_rank_rate_table(room[:before_rate_0]) if room[:before_rate_0]
            if rate_table && rate_table["LoseDiff"]
              room[:after_rate_0] = room[:before_rate_0] - rate_table["LoseDiff"]
            else
              room[:after_rate_0] = room[:before_rate_0] || 0
            end
          end
          room[:after_rate_0] = RateStopperService.apply_stopper(room[:after_rate_0], room[:before_rate_0])

          # user0 and user1 already fetched above
          if user0.present?
            user0.rate = room[:after_rate_0]
            user0.save!
          end

          if user1.present?
            user1.rate = room[:after_rate_1]
            user1.wins += 1
            user1.save!
          end
        end
      end

      # 必須データのnullチェック
      required_fields = [
        room[:player_0_id],
        room[:player_1_id],
        room[:result],
        room[:played_at],
        room[:deck_0],
        room[:deck_1],
        room[:before_rate_0],
        room[:before_rate_1]
      ]

      if required_fields.any?(&:nil?)
        self.class.ERROR("SAVEDB_ROOM", "Required fields are missing, skipping match data save for room_id: #{room[:room_id]}")
        self.class.DEBUG("RoomClosed", "Room closed without saving match data due to missing fields: #{room}")
        redis_del("battle_room:#{room_id}")
        return
      end

      begin
        match_data = {
          player_0:        room[:player_0_id],
          player_1:        room[:player_1_id],
          reason:          RESULT_REASONS[room[:reason]],
          result:          room[:result],
          played_at:       room[:played_at],
          game_mode:       room[:game_mode],
          event_id:        room[:event_id],
          matching_time_0: room[:matching_time_0],
          matching_time_1: room[:matching_time_1],
          deck_0:          safe_parse_deck_cards(deck_0_data[:Cards]),
          deck_1:          safe_parse_deck_cards(deck_1_data[:Cards]),
          group_0:         deck_0_data[:Group] || default_group,
          group_1:         deck_1_data[:Group] || default_group,
          rank:            room[:rank] || 0,
          before_rate_0:   room[:before_rate_0],
          before_rate_1:   room[:before_rate_1],
          after_rate_0:    room[:after_rate_0],
          after_rate_1:    room[:after_rate_1],
          replay_data:     room[:input_data_array] || [],
          other_data:      {
            seed:     room[:random_seed] || 0,
            player_0: {
              leader: deck_0_data[:Leader] || default_leader,
              name: room[:player_0_name] || "Unknown",
              card_sleeve_id: deck_0_data[:CardSleeveId] || default_card_sleeve_id
            },
            player_1: {
              leader: deck_1_data[:Leader] || default_leader,
              name: room[:player_1_name] || "Unknown",
              card_sleeve_id: deck_1_data[:CardSleeveId] || default_card_sleeve_id
            },
          },
        }

        # JSONで出力
        self.class.DEBUG("SAVEDB_ROOM", "Match.create payload: #{match_data.to_json}", room[:room_id])

        Match.create!(match_data)
        self.class.DEBUG("SAVEDB_ROOM", "Saved match record for room_id: #{room[:room_id]}")
      rescue => e
        self.class.ERROR("SAVEDB_ROOM", "Failed to save match record: #{e.message}", room[:room_id])
        self.class.ERROR("SAVEDB_ROOM", "Backtrace: #{e.backtrace.join("\n")}", room[:room_id])
      end

      self.class.DEBUG("RoomClosed", "Sending match data to all players: #{room}")
      message = {
        command: "MatchData",
        body:    match_data,
      }
      ActionCable.server.broadcast("battle_room:#{room_id}", message)

      self.class.DEBUG("RoomClosed", "Room closed: #{room}")
      redis_del("battle_room:#{room_id}")
    end

    # 対人戦勝利時に宝箱の勝利数を更新する
    def update_chest_win_count(user, player_index, room)
      # どのようなゲームモードでも勝ったら宝箱の勝利数を更新する
      # return unless ["free", "rank"].include?(room[:game_mode])
      ChestService.add_win_count(user)
    end

    # Safe deck cards parsing to prevent nil errors
    def safe_parse_deck_cards(cards_data)
      return [] if cards_data.nil?

      begin
        if cards_data.is_a?(String)
          JSON.parse(cards_data)
        elsif cards_data.respond_to?(:to_json)
          JSON.parse(cards_data.to_json)
        else
          []
        end
      rescue JSON::ParserError => e
        self.class.ERROR("SAVEDB_ROOM", "Failed to parse deck cards: #{e.message}")
        []
      rescue => e
        self.class.ERROR("SAVEDB_ROOM", "Unexpected error parsing deck cards: #{e.message}")
        []
      end
    end
end
