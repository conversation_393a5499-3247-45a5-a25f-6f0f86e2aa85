class FreeMatchingChannel < ApplicationCable::Channel
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end

    # ロックの状態をチェックするヘルパーメソッド
    def check_lock_exists(key)
      begin
        # Redisに直接問い合わせてロック情報を取得
        lock_key = "redlock:#{key}"
        value = redis.get(lock_key)
        if value
          DEBUG("FreeMatchingChannel", "Lock exists for #{key}: #{value}")
          true
        else
          false
        end
      rescue => e
        ERROR("FreeMatchingChannel", "Error checking lock: #{e.message}")
        false
      end
    end

    def create_battle_room(player1_id, player2_id, player1_timecount, player2_timecount)
      # Redisのアトミックカウンターを使用してユニークなroom_idを生成
      counter = self.redis.incr("room_id_counter")
      room_id = "free_#{counter}"

      self.DEBUG("FreeMatchingChannel", "Create battle room with unique room_id: #{room_id} (counter: #{counter})")

      # ロック競合を軽減するためにバッチで更新を実行
      BatchUserStatusService.batch_update do
        BatchUserStatusService.update_status(player1_id, "matched", {
          opponent_id:   player2_id,
          room_id:       room_id,
          matched_at:    Time.now,
          index_in_room: 0,
          channel_type:  "free_matching",
        })

        BatchUserStatusService.update_status(player2_id, "matched", {
          opponent_id:   player1_id,
          room_id:       room_id,
          matched_at:    Time.now,
          index_in_room: 1,
          channel_type:  "free_matching",
        })
      end

      ActionCable.server.broadcast("free_matching:#{player1_id}",
        { command:       "FreeMatchingSuccess",
          room_id:       room_id,
          index_in_room: 0,
          timecount:     player1_timecount,
        })
      ActionCable.server.broadcast("free_matching:#{player2_id}",
        { command:       "FreeMatchingSuccess",
          room_id:       room_id,
          index_in_room: 1,
          timecount:     player2_timecount,
        })
    end
  end

  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  # より安全なwith_lockヘルパー - TTLを短くして接続解放を確実に行う
  def with_lock(lock_key, ttl = nil)
    settings = self.class.get_env_settings
    start_time = Time.now
    success = false
    retry_count = 0
    max_retries = settings[:retry_count]

    # ロックが存在しているか確認（デバッグ用）
    self.class.check_lock_exists(lock_key)

    # TTLを適切に設定（指定がない場合はデフォルト値を使用）
    ttl = ttl || settings[:default_ttl]
    # 短いTTLに強制的に調整（長すぎるTTLを防止）
    ttl = [ ttl, settings[:max_lock_ttl] ].min

    # 処理全体のタイムアウト（TTLの3倍を目安）
    timeout = ttl * 3
    timeout_time = start_time + (timeout / 1000.0)

    while !success && retry_count < max_retries && Time.now < timeout_time
      begin
        # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
        remaining_time = (timeout_time - Time.now) * 1000
        if remaining_time < ttl
          adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
          self.class.WARN("FreeMatchingChannel", "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
          ttl = adjusted_ttl
        end

        self.class.redlock.lock(lock_key, ttl) do |locked|
          if locked
            begin
              self.class.DEBUG("FreeMatchingChannel", "Lock acquired for #{lock_key}")
              success = true
              yield
            rescue => e
              self.class.ERROR("FreeMatchingChannel", "Error in lock block for #{lock_key}: #{e.message}")
              self.class.ERROR("FreeMatchingChannel", e.backtrace.join("\n"))
              success = false
            ensure
              # ロック処理が長すぎる場合は警告
              duration = ((Time.now - start_time) * 1000).to_i
              if duration > ttl / 2
                self.class.WARN("FreeMatchingChannel", "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
              end
            end
          else
            retry_count += 1
            # 環境に応じたリトライ間隔を使用
            sleep_time = settings[:retry_interval]
            self.class.WARN("FreeMatchingChannel", "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
            sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
          end
        end
      rescue => e
        retry_count += 1
        self.class.ERROR("FreeMatchingChannel", "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
        sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
      end
    end

    # タイムアウトした場合の処理
    if Time.now >= timeout_time && !success
      self.class.ERROR("FreeMatchingChannel", "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
    end

    if !success && retry_count >= max_retries
      self.class.ERROR("FreeMatchingChannel", "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
    end

    success
  end

  # Redisヘルパーメソッド
  def redis_get(key)
    self.class.redis.get(key)
  rescue Redis::BaseError => e
    self.class.ERROR("FreeMatchingChannel", "Redis error when getting #{key}: #{e.message}")
    nil
  end

  def redis_set(key, value, expire = nil)
    self.class.redis.set(key, value)
    if expire
      self.class.redis.expire(key, expire)
    end
    true
  rescue Redis::BaseError => e
    self.class.ERROR("FreeMatchingChannel", "Redis error when setting #{key}: #{e.message}")
    false
  end

  def redis_del(key)
    self.class.redis.del(key)
    true
  rescue Redis::BaseError => e
    self.class.ERROR("FreeMatchingChannel", "Redis error when deleting #{key}: #{e.message}")
    false
  end

  def subscribed
    self.class.DEBUG("FreeMatchingChannel", "Subscribed")
  end

  def join_free_matching
    begin
      self.class.DEBUG("FreeMatchingChannel", "Join free matching")
      player_id = connection.get_player_id

      # 専用のRedis接続とロックを使用
      with_lock("free_matching:#{player_id}:join", 1000) do
        redis_set("free_matching_data:#{player_id}", { id: player_id, timecount: 0, start_time: Time.now.to_i }.to_json)
        # プレイヤーIDをSetに追加（パフォーマンス改善）
        self.class.redis.sadd("free_matching_players", player_id)
      end

      # Redis-only status update (high performance, no database access)
      UserStatusService.update_status(player_id, "matching", {
        timecount: 0,
        started_at: Time.now,
        channel_type: "free_matching",
        battle_mode: "free",
        free_match_channel: true
      })

      stream_from "free_matching:#{player_id}"
    rescue Redis::BaseError => e
      self.class.ERROR("FreeMatchingChannel", "Redis error in join_free_matching: #{e.message}", player_id)
    rescue => e
      self.class.ERROR("FreeMatchingChannel", "Error in join_free_matching: #{e.message}", player_id)
    end
  end

  def unsubscribed
    begin
      player_id = connection.get_player_id

      # 専用のRedlock接続とwith_lockヘルパーを使用
      with_lock("free_matching:#{player_id}:unsubscribe", 1000) do
        redis_del("free_matching_data:#{player_id}")
        # プレイヤーIDをSetから削除（パフォーマンス改善）
        self.class.redis.srem("free_matching_players", player_id)
        self.class.DEBUG("FreeMatchingChannel", "Player removed from free matching", player_id)
      end
    rescue Redis::BaseError => e
      self.class.ERROR("FreeMatchingChannel", "Redis error in unsubscribed: #{e.message}", player_id)
    rescue => e
      self.class.ERROR("FreeMatchingChannel", "Error in unsubscribed: #{e.message}", player_id)
    end
  end
end
