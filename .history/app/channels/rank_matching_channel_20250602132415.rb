class RankMatchingChannel < ApplicationCable::Channel
  class << self
    attr_accessor :matching_range, :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end

    # ロックの状態をチェックするヘルパーメソッド
    def check_lock_exists(key)
      begin
        # Redisに直接問い合わせてロック情報を取得
        lock_key = "redlock:#{key}"
        value = redis.get(lock_key)
        if value
          DEBUG("RankMatchingChannel", "Lock exists for #{key}: #{value}")
          true
        else
          false
        end
      rescue => e
        ERROR("RankMatchingChannel", "Error checking lock: #{e.message}")
        false
      end
    end

    def find_matching_player(clients, start_index, step, target_player, matched_players = {})
      distance = Float::INFINITY
      matching_player = nil

      return [ nil, Float::INFINITY ] if start_index < 0 || start_index >= clients.size

      range = step > 0 ? (start_index...clients.size) : (start_index.downto(0))

      range.each do |i|
        candidate_id, candidate_player = clients[i]

        # すでにマッチ済みのプレイヤーをスキップ
        if matched_players.key?(candidate_id)
          self.DEBUG("MatchProcess", "Skipping already matched player: #{candidate_id}")
          next
        end

        self.DEBUG("MatchProcess", "Evaluating candidate: #{candidate_id}, rank: #{candidate_player[:rank]} vs #{target_player[:rank]}")

        # BOT同士のマッチングを早期にチェック
        if both_players_are_bots?(target_player, candidate_player)
          self.DEBUG("MatchProcess", "Skipping bot vs bot potential match: #{candidate_id} and target #{target_player[:id]}")
          next
        else
          self.DEBUG("MatchProcess", "BOT check passed for #{candidate_id} and target #{target_player[:id]}")
        end

        temp_distance = (candidate_player[:rank] - target_player[:rank]).abs
        if temp_distance > target_player[:timecount] * self.matching_range
          self.DEBUG("MatchProcess", "Candidate outside match range: #{temp_distance} > #{target_player[:timecount] * self.matching_range}")
          break
        else
          distance = temp_distance
          matching_player = candidate_player
          self.DEBUG("MatchProcess", "Found potential match: #{candidate_id}, distance: #{distance}")
          break
        end
      end

      [ matching_player, distance ]
    end

    # BOT同士のマッチングを防ぐヘルパーメソッド
    def both_players_are_bots?(player1, player2)
      # ISBOTキーが存在し、かつtrueの場合のみBOTとして扱う
      player1_is_bot = player1.key?(:isbot) && (player1[:isbot] == true || player1[:isbot] == "true")
      player2_is_bot = player2.key?(:isbot) && (player2[:isbot] == true || player2[:isbot] == "true")

      # デバッグログ：詳細なBOTチェック情報
      self.DEBUG("MatchProcess", "BOT check - Player1 #{player1[:id]}: has_key=#{player1.key?(:isbot)}, value=#{player1[:isbot]}, is_bot=#{player1_is_bot}")
      self.DEBUG("MatchProcess", "BOT check - Player2 #{player2[:id]}: has_key=#{player2.key?(:isbot)}, value=#{player2[:isbot]}, is_bot=#{player2_is_bot}")
      self.DEBUG("MatchProcess", "Both are bots? #{player1_is_bot && player2_is_bot}")

      player1_is_bot && player2_is_bot
    end

    def create_battle_room(player1_id, player2_id, p1_timecount, p2_timecount)
      # Redisのアトミックカウンターを使用してユニークなroom_idを生成
      counter = self.redis.incr("room_id_counter")
      room_id = "rank_#{counter}"

      self.DEBUG("RoomCreation", "Creating battle room for players #{player1_id} and #{player2_id} with unique room_id: #{room_id}")

      # UserStatusService.update_status(player1_id, "matched", {
      #   opponent_id:    player2_id,
      #   room_id:        room_id,
      #   matched_at:     Time.now,
      #   index_in_room:  0,
      #   skip_broadcast: true, # マッチング時はブロードキャストをスキップ
      # })

      # UserStatusService.update_status(player2_id, "matched", {
      #   opponent_id:    player1_id,
      #   room_id:        room_id,
      #   matched_at:     Time.now,
      #   index_in_room:  1,
      #   skip_broadcast: true, # マッチング時はブロードキャストをスキップ
      # })

      # より詳細なブロードキャスト情報のログ
      player1_message = {
        command:       "RankMatchingSuccess",
        room_id:       room_id,
        index_in_room: 0,
        timecount:     p1_timecount,
      }

      player2_message = {
        command:       "RankMatchingSuccess",
        room_id:       room_id,
        index_in_room: 1,
        timecount:     p2_timecount,
      }

      self.DEBUG("Broadcast", "To player1: #{player1_message}", player1_id)
      self.DEBUG("Broadcast", "To player2: #{player2_message}", player2_id)

      # 正しい実装例
      ActionCable.server.broadcast("rank_matching:#{player1_id}", player1_message)
      ActionCable.server.broadcast("rank_matching:#{player2_id}", player2_message)

      self.DEBUG("RoomCreation", "Battle room created successfully with unique room_id: #{room_id} (counter: #{counter})", player1_id)
    end
  end

  self.matching_range = ENV.fetch("MATCHING_RANGE", 100).to_i
  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")


  # より安全なwith_lockヘルパー - TTLを短くして接続解放を確実に行う
  def with_lock(lock_key, ttl = nil)
    settings = self.class.get_env_settings
    start_time = Time.now
    success = false
    retry_count = 0
    max_retries = settings[:retry_count]

    # ロックが存在しているか確認（デバッグ用）
    self.class.check_lock_exists(lock_key)

    # TTLを適切に設定（指定がない場合はデフォルト値を使用）
    ttl = ttl || settings[:default_ttl]
    # 短いTTLに強制的に調整（長すぎるTTLを防止）
    ttl = [ ttl, settings[:max_lock_ttl] ].min

    # 処理全体のタイムアウト（TTLの3倍を目安）
    timeout = ttl * 3
    timeout_time = start_time + (timeout / 1000.0)

    while !success && retry_count < max_retries && Time.now < timeout_time
      begin
        # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
        remaining_time = (timeout_time - Time.now) * 1000
        if remaining_time < ttl
          adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
          self.class.WARN("RankMatchingChannel", "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
          ttl = adjusted_ttl
        end

        self.class.redlock.lock(lock_key, ttl) do |locked|
          if locked
            begin
              self.class.DEBUG("RankMatchingChannel", "Lock acquired for #{lock_key}")
              success = true
              yield
            rescue => e
              self.class.ERROR("RankMatchingChannel", "Error in lock block for #{lock_key}: #{e.message}")
              self.class.ERROR("RankMatchingChannel", e.backtrace.join("\n"))
              success = false
            ensure
              # ロック処理が長すぎる場合は警告
              duration = ((Time.now - start_time) * 1000).to_i
              if duration > ttl / 2
                self.class.WARN("RankMatchingChannel", "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
              end
            end
          else
            retry_count += 1
            # 環境に応じたリトライ間隔を使用
            sleep_time = settings[:retry_interval]
            self.class.WARN("RankMatchingChannel", "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
            sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
          end
        end
      rescue => e
        retry_count += 1
        self.class.ERROR("RankMatchingChannel", "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
        sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
      end
    end

    # タイムアウトした場合の処理
    if Time.now >= timeout_time && !success
      self.class.ERROR("RankMatchingChannel", "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
    end

    if !success && retry_count >= max_retries
      self.class.ERROR("RankMatchingChannel", "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
    end

    success
  end

  # Redisヘルパーメソッド
  def redis_get(key)
    self.class.redis.get(key)
  rescue Redis::BaseError => e
    self.class.ERROR("RankMatchingChannel", "Redis error when getting #{key}: #{e.message}")
    nil
  end

  def redis_set(key, value, expire = nil)
    self.class.redis.set(key, value)
    if expire
      self.class.redis.expire(key, expire)
    end
    true
  rescue Redis::BaseError => e
    self.class.ERROR("RankMatchingChannel", "Redis error when setting #{key}: #{e.message}")
    false
  end

  def redis_del(key)
    self.class.redis.del(key)
    true
  rescue Redis::BaseError => e
    self.class.ERROR("RankMatchingChannel", "Redis error when deleting #{key}: #{e.message}")
    false
  end

  def subscribed
    begin
      player_id = connection.get_player_id
      match_key = "matching_data:#{player_id}"

      # REDISコネクションプールの代わりに専用のRedis接続を使用
      exists = false
      exists = redis_get(match_key) != nil

      if exists
        self.class.WARN("Matching", "Player #{player_id} already in queue")
        reject_with_error("ESBC-0001", "Player already in queue")
        return
      end

      # ストリームの設定
      stream_from "rank_matching:#{player_id}"

      # プレイヤー情報の取得 - OUTSIDE lock to avoid lock contention
      player = User.find_by(open_id: player_id)
      if player.nil?
        self.class.ERROR("Matching", "Player #{player_id} not found")
        reject_with_error("ESBC-0002", "Player not found")
        return
      end

      player_rate = player.rate
      # params
      self.class.DEBUG("Matching", "Player #{player_id} params: #{params}")
      # ISBOTパラメータが存在し、かつtrueの場合のみBOTとして扱う
      is_bot = params.key?(:is_bot) && params[:is_bot].to_s == "true"

      # デバッグログ：プレイヤーのBOT状態を記録
      self.class.DEBUG("Matching", "Player #{player_id} BOT status: params has isbot=#{params.key?(:isbot)}, value=#{params[:isbot]}, is_bot=#{is_bot}")

      match_data = { id: player_id, rank: player_rate, timecount: 0, isbot: is_bot, start_time: Time.now.to_i }

      # キューサイズの取得とマッチングデータの保存
      queue_size = 0
      with_lock("rank_matching:#{player_id}:subscribe", 1000) do
        redis_set(match_key, match_data.to_json)
        # プレイヤーIDをSetに追加（パフォーマンス改善）
        self.class.redis.sadd("rank_matching_players", player_id)
        # Setのサイズを取得（KEYSより高速）
        queue_size = self.class.redis.scard("rank_matching_players")
      end

      # ユーザーステータスの更新 - OUTSIDE lock to avoid lock contention
      UserStatusService.update_status(player_id, "matching", {
        rank:         player_rate,
        timecount:    0,
        queue_size:   queue_size,
        started_at:   Time.now,
        channel_type: "rank_matching",
      })

      self.class.DEBUG("Matching", "Player #{player_id} entered queue with rank #{player_rate}")
    rescue Redis::BaseError => e
      self.class.ERROR("Redis", "Error in subscribed: #{e.message}", player_id)
      reject_with_error("ESBC-0003", "Database error")
    rescue => e
      self.class.ERROR("Subscription", "Error in subscribed: #{e.message}", player_id)
      self.class.ERROR("Subscription", e.backtrace.join("\n"), player_id)
      reject_with_error("ESBC-9999", "Internal server error")
    end
  end

  def unsubscribed
    begin
      player_id = connection.get_player_id
      match_key = "matching_data:#{player_id}"

      self.class.DEBUG("Subscription", "Player unsubscribed", player_id)

      # REDISコネクションプールの代わりに専用のRedis接続を使用し、ロックで保護
      with_lock("rank_matching:#{player_id}:unsubscribe", 1000) do
        exists = redis_get(match_key) != nil
        if exists
          redis_del(match_key)
          # プレイヤーIDをSetから削除（パフォーマンス改善）
          self.class.redis.srem("rank_matching_players", player_id)
          self.class.DEBUG("Matching", "Player removed from matching", player_id)
        else
          self.class.DEBUG("Matching", "Player not in matching", player_id)
        end
      end

      # 接続が生きている場合のみステータスを更新
      # if connection_alive?
      #   UserStatusService.update_status(player_id, "connected", {
      #     left_at: Time.now,
      #     reason:  "unsubscribed",
      #   })
      # end
    rescue Redis::BaseError => e
      self.class.ERROR("Redis", "Error in unsubscribed: #{e.message}", player_id)
    rescue => e
      self.class.ERROR("Subscription", "Error in unsubscribed: #{e.message}", player_id)
      self.class.ERROR("Subscription", e.backtrace.join("\n"), player_id)
    end
  end
end
