window.UserStatusWebSocket = (function() {
  let adminStatusSubscription = null;
  let rankMatchSubscription = null;
  let freeMatchSubscription = null;
  let roomMatchSubscription = null;
  let isConnected = false;
  let currentView = null;
  let lastReceivedData = null;

  // Callback functions cho từng view
  let callbacks = {
    onAdminData: null,
    onRankData: null,
    onFreeData: null,
    onRoomData: null,
    onConnectionChange: null
  };

  function connect(viewName, viewCallbacks = {}) {
    console.log(`UserStatusWebSocket: Connecting for view ${viewName}`);
    
    // Cập nhật current view
    currentView = viewName;
    
    // Cập nhật callbacks
    Object.assign(callbacks, viewCallbacks);

    // Nếu đã connected, chỉ cần gọi callbacks với data hiện tại
    if (isConnected && adminStatusSubscription) {
      console.log('UserStatusWebSocket: Already connected, using existing connection');
      
      // Gọi callback với data cuối cùng nếu có
      if (lastReceivedData && callbacks.onAdminData) {
        callbacks.onAdminData(lastReceivedData);
      }
      
      // Cập nhật connection status
      if (callbacks.onConnectionChange) {
        callbacks.onConnectionChange('connected');
      }
      
      return;
    }

    // Tạo connection mới
    createConnections();
  }

  function createConnections() {
    console.log('UserStatusWebSocket: Creating new connections');

    // Tạo cable consumer
    const adminId = window.currentMasterUserId || null;
    let wsUrl = '/cable?client_type=admin';
    if (adminId) {
      wsUrl += '&token=admin-' + adminId;
    }

    console.log("Connecting to WebSocket at: " + wsUrl);
    const cable = ActionCable.createConsumer(wsUrl);

    // Main admin channel
    adminStatusSubscription = cable.subscriptions.create("Admin::UserStatusChannel", {
      connected() {
        console.log("Connected to Admin::UserStatusChannel!");
        isConnected = true;
        
        if (callbacks.onConnectionChange) {
          callbacks.onConnectionChange('connected');
        }
      },

      disconnected() {
        console.log("Disconnected from Admin::UserStatusChannel");
        isConnected = false;
        
        if (callbacks.onConnectionChange) {
          callbacks.onConnectionChange('disconnected');
        }
      },

      received(data) {
        console.log("Received admin data:", data);
        lastReceivedData = data;
        
        if (callbacks.onAdminData) {
          callbacks.onAdminData(data);
        }
      }
    });

    // Rank match channel
    rankMatchSubscription = cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "rank" },
      {
        received(data) {
          console.log("Received rank match data:", data);
          if (callbacks.onRankData) {
            callbacks.onRankData(data);
          }
        }
      }
    );

    // Free match channel
    freeMatchSubscription = cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "free" },
      {
        received(data) {
          console.log("Received free match data:", data);
          if (callbacks.onFreeData) {
            callbacks.onFreeData(data);
          }
        }
      }
    );

    // Room match channel
    roomMatchSubscription = cable.subscriptions.create(
      { channel: "Admin::MatchStatusChannel", match_type: "room" },
      {
        received(data) {
          console.log("Received room match data:", data);
          if (callbacks.onRoomData) {
            callbacks.onRoomData(data);
          }
        }
      }
    );
  }

  function disconnect() {
    console.log('UserStatusWebSocket: Disconnecting all connections');
    
    if (adminStatusSubscription) {
      adminStatusSubscription.unsubscribe();
      adminStatusSubscription = null;
    }
    
    if (rankMatchSubscription) {
      rankMatchSubscription.unsubscribe();
      rankMatchSubscription = null;
    }
    
    if (freeMatchSubscription) {
      freeMatchSubscription.unsubscribe();
      freeMatchSubscription = null;
    }
    
    if (roomMatchSubscription) {
      roomMatchSubscription.unsubscribe();
      roomMatchSubscription = null;
    }
    
    isConnected = false;
    currentView = null;
    lastReceivedData = null;
    
    // Reset callbacks
    callbacks = {
      onAdminData: null,
      onRankData: null,
      onFreeData: null,
      onRoomData: null,
      onConnectionChange: null
    };
  }

  function updateCallbacks(newCallbacks) {
    Object.assign(callbacks, newCallbacks);
  }

  function getCurrentView() {
    return currentView;
  }

  function isWebSocketConnected() {
    return isConnected;
  }

  function getLastData() {
    return lastReceivedData;
  }

  // Public API
  return {
    connect: connect,
    disconnect: disconnect,
    updateCallbacks: updateCallbacks,
    getCurrentView: getCurrentView,
    isConnected: isWebSocketConnected,
    getLastData: getLastData
  };
})();

// Auto disconnect khi rời khỏi user_status pages
document.addEventListener('DOMContentLoaded', function() {
  // Kiểm tra nếu không phải user_status page thì disconnect
  const currentPath = window.location.pathname;
  const isUserStatusPage = currentPath.includes('/user_statuses') || currentPath.includes('/matching_test');

  if (!isUserStatusPage && window.UserStatusWebSocket) {
    console.log('Not on user_status page, disconnecting WebSocket');
    window.UserStatusWebSocket.disconnect();
  }
});

// Handle navigation events
let lastPath = window.location.pathname;

// Check for navigation changes
function checkNavigation() {
  const currentPath = window.location.pathname;
  const isUserStatusPage = currentPath.includes('/user_statuses') || currentPath.includes('/matching_test');
  const wasUserStatusPage = lastPath.includes('/user_statuses') || lastPath.includes('/matching_test');

  // If navigating away from user_status pages to non-user_status page
  if (wasUserStatusPage && !isUserStatusPage && window.UserStatusWebSocket) {
    console.log('Navigating away from user_status pages, disconnecting WebSocket');
    window.UserStatusWebSocket.disconnect();
  }

  lastPath = currentPath;
}

// Listen for navigation events
window.addEventListener('popstate', checkNavigation);

// For modern navigation (if using Turbo/Turbolinks)
document.addEventListener('turbo:visit', checkNavigation);
document.addEventListener('turbolinks:visit', checkNavigation);

// Disconnect khi navigate away từ user_status pages
window.addEventListener('beforeunload', function() {
  const currentPath = window.location.pathname;
  const isUserStatusPage = currentPath.includes('/user_statuses') || currentPath.includes('/matching_test');

  // Only disconnect if we're leaving user_status pages
  if (!isUserStatusPage && window.UserStatusWebSocket) {
    console.log('Page unload from non-user_status page, disconnecting WebSocket');
    window.UserStatusWebSocket.disconnect();
  }
});

// Export cho ES6 modules
export default window.UserStatusWebSocket;
