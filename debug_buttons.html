<!DOCTYPE html>
<html>
<head>
    <title>Debug Buttons Test</title>
    <meta name="csrf-token" content="test-token">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Debug Buttons Test</h1>
        
        <!-- Test Users Selection -->
        <div class="mb-3">
            <h3>Select Test Users:</h3>
            <input type="checkbox" name="test_users" value="test_user_1" id="user1"> 
            <label for="user1">Test User 1</label><br>
            <input type="checkbox" name="test_users" value="test_user_2" id="user2"> 
            <label for="user2">Test User 2</label><br>
            <input type="checkbox" name="test_users" value="test_user_3" id="user3"> 
            <label for="user3">Test User 3</label><br>
        </div>
        
        <!-- Test Buttons -->
        <div class="mb-3">
            <button onclick="testSimulateRankMatching()">🏆 Test Rank Matching</button>
            <button onclick="testSimulateFreeMatching()">🎯 Test Free Matching</button>
            <button onclick="testSimulateRoomMatching()">🏠 Test Room Matching</button>
            <button onclick="testSimulateMatchSuccess()">⚔️ Test Create Match</button>
            <button onclick="testClearAllStatus()">🗑️ Test Clear All Status</button>
        </div>
        
        <!-- Results -->
        <div id="results"></div>
    </div>

    <script>
        function getSelectedUsers() {
            const checkboxes = document.querySelectorAll('input[name="test_users"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function showResult(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                    <h4>${data.success ? '✅ Success' : '❌ Error'}</h4>
                    <p><strong>Message:</strong> ${data.message}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }

        function testSimulateRankMatching() {
            console.log('Testing Rank Matching...');
            const userIds = getSelectedUsers();
            console.log('Selected users:', userIds);
            
            if (userIds.length === 0) {
                alert('少なくとも1人のユーザーを選択してください');
                return;
            }

            fetch('/matching_test/simulate_rank_matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                },
                body: JSON.stringify({ user_ids: userIds })
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                showResult(data);
            })
            .catch(error => {
                console.error('Error:', error);
                showResult({ success: false, message: `Error: ${error.message}` });
            });
        }

        function testSimulateFreeMatching() {
            console.log('Testing Free Matching...');
            const userIds = getSelectedUsers();
            
            if (userIds.length === 0) {
                alert('少なくとも1人のユーザーを選択してください');
                return;
            }

            fetch('/matching_test/simulate_free_matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                },
                body: JSON.stringify({ user_ids: userIds })
            })
            .then(response => response.json())
            .then(data => showResult(data))
            .catch(error => {
                console.error('Error:', error);
                showResult({ success: false, message: `Error: ${error.message}` });
            });
        }

        function testSimulateRoomMatching() {
            console.log('Testing Room Matching...');
            const userIds = getSelectedUsers();
            
            if (userIds.length === 0) {
                alert('少なくとも1人のユーザーを選択してください');
                return;
            }

            fetch('/matching_test/simulate_room_matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                },
                body: JSON.stringify({ user_ids: userIds })
            })
            .then(response => response.json())
            .then(data => showResult(data))
            .catch(error => {
                console.error('Error:', error);
                showResult({ success: false, message: `Error: ${error.message}` });
            });
        }

        function testSimulateMatchSuccess() {
            console.log('Testing Match Success...');
            
            fetch('/matching_test/simulate_match_success', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => showResult(data))
            .catch(error => {
                console.error('Error:', error);
                showResult({ success: false, message: `Error: ${error.message}` });
            });
        }

        function testClearAllStatus() {
            console.log('Testing Clear All Status...');
            
            if (confirm('すべてのユーザーステータスをクリアしますか？')) {
                fetch('/matching_test/clear_all_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => showResult(data))
                .catch(error => {
                    console.error('Error:', error);
                    showResult({ success: false, message: `Error: ${error.message}` });
                });
            }
        }

        // Test CSRF token
        console.log('CSRF Token:', document.querySelector('[name="csrf-token"]').content);
        
        // Test if we can reach the server
        fetch('/matching_test', {
            method: 'GET'
        })
        .then(response => {
            console.log('Server reachable, status:', response.status);
        })
        .catch(error => {
            console.error('Cannot reach server:', error);
        });
    </script>
</body>
</html>
