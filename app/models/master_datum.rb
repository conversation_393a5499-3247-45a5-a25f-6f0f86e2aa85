class MasterDatum < ApplicationRecord
    include AutoUidGenerator
    # レコードの保存後にキャッシュをクリア
    after_save :clear_cache
    # レコードの削除後にキャッシュをクリア
    after_destroy :clear_cache
    
    def self.get_hash_key()
        hash_key = Digest::SHA256.hexdigest(get_all().to_json)
        return hash_key
    end

    def self.get_all()
        Rails.cache.fetch("master_datum_content", expires_in: 1.hour) do
            md = MasterDatum.order(created_at: :desc).first
            if md.present?
                content = md.content
                
                # カード情報に修正を適用
                if content && content["_cards"].present?
                    cards = content["_cards"].deep_dup
                    card_modifiers = CardModifier.where(use: 1)
                    
                    if card_modifiers.present?
                        cards.map! do |card|
                            # カードIDと一致するCardModifierを探す
                            modifier = card_modifiers.find { |m| m.card_id == card["ID"] }
                            
                            # 修正情報とnerf情報を追加
                            card["IsModified"] = modifier.present?
                            card["IsNerf"] = modifier.present? && modifier.is_nerf == 1
                            
                            if modifier.present?
                                # カード情報を修正する
                                card["Name"] = modifier.name if modifier.name.present?
                                card["Group"] = modifier.group if modifier.group.present? && modifier.group >= 0
                                card["Role"] = modifier.role if modifier.role.present?
                                card["Cost"] = modifier.cost if modifier.cost.present?
                                card["Power"] = modifier.power if modifier.power.present?
                                card["Rarity"] = modifier.rarity if modifier.rarity.present? && modifier.rarity >= 0
                                
                                # テキストとスクリプトの修正
                                (0..2).each do |i|
                                    
                                    # タイミングの修正
                                    use_timing = modifier.send("use_timing#{i}")
                                    if use_timing == 0
                                        card["Timing#{i}"] = modifier.send("timing#{i}")
                                    elsif use_timing == 1
                                        card["Timing#{i}"] = ""
                                    end

                                    # テキストの修正
                                    use_text = modifier.send("use_text#{i}")
                                    if use_text == 0
                                        card["Text#{i}"] = modifier.send("text#{i}")
                                    elsif use_text == 1
                                        card["Text#{i}"] = ""
                                    end
                                    
                                    # スクリプトの修正
                                    use_script = modifier.send("use_script#{i}")
                                    if use_script == 0
                                        card["Script#{i}"] = modifier.send("script#{i}")
                                    elsif use_script == 1
                                        card["Script#{i}"] = ""
                                    end
                                end
                            end
                            
                            card
                        end
                    end
                    
                    # 修正したカード情報を元のコンテンツに戻す
                    content["_cards"] = cards
                end
                
                content
            else
                nil
            end
        end
    end
    
    def self.get_all_default()
        Rails.cache.fetch("master_datum_content", expires_in: 1.hour) do
            md = MasterDatum.order(created_at: :desc).first
            if md.present?
                md.content
            else
                nil
            end
        end
    end

    def self.get_card_rarity(id)
        card = get_card(id)
        return card.dig("Rarity")
    end

    def self.get_cards()
        # 既にキャッシュされたデータを取得する
        get_all()&.dig("_cards") || []
    end

    def self.get_card(id)
        cards = get_cards()
        return cards.find { |card| card["ID"] == id }
    end

    def self.get_default_cards()
        # 既にキャッシュされたデータを取得する
        all_cards = get_all_default()&.dig("_cards") || []
        card_modifiers = CardModifier.where(use: 1)
        
        # ディープコピーして元のデータに影響を与えないようにする
        all_cards = all_cards.deep_dup
        
        return all_cards
    end

    def self.get_default_card(id)
        all_cards = get_default_cards()
        return all_cards&.find { |card| card["ID"] == id }
    end

    def self.get_groups()
        # 既にキャッシュされたデータを取得する
        get_all()&.dig("_groups") || []
    end

    def self.get_group(id)
        groups = get_groups()
        return groups&.find { |group| group["ID"] == id }
    end

    def self.get_groups_for_select()
        groups = get_groups()
        return groups&.map { |group| [group["Name"], group["ID"]] }
    end

    
    def self.get_categories()
        # 既にキャッシュされたデータを取得する
        get_all()&.dig("_categories") || []
    end

    def self.get_category(id)
        categories = get_categories()
        return categories&.find { |category| category["ID"] == id }
    end

    def self.get_categories_for_select()
        categories = get_categories()
        return categories&.map { |category| [category["Name"], category["Key"]] }
    end

    def self.get_item_name(item_type, item_id)
        data = get_all()
        if data.present?
            sheet = data['_' + item_type]
            if sheet.present?
                item = sheet.find { |item| item["ID"] == item_id }
                if item.present?
                    return item['Name__jp']
                else
                    return ""
                end
            else
                return ""
            end
        else
            return ""
        end
    end

    def self.get_skins()
        # 既にキャッシュされたデータを取得する
        get_all()&.dig("_leader_skins") || []
    end

    def self.get_skin(id)
        skins = get_skins()
        puts "id: #{id.class}"
        puts "id_skins_id: #{skins[1]["ID"]}"
        return skins&.find { |skin| skin["ID"] == id }
    end

    def self.skin_groups(id)
        # 既にキャッシュされたデータを取得する
        skin = get_skin(id)
        return skin&.dig("GroupID")
    end

    def self.get_rank_rate_tables()
        get_all()&.dig("_rank_rate_table") || []
    end

    def self.get_rank_rate_table(rate)
        tables = get_rank_rate_tables()
        # 後ろからfindする
        return tables.reverse.find { |table| table["NeedExp"] <= rate }
    end
    

    # キャッシュをクリアするメソッド（マスターデータが更新された際に使用）
    def self.clear_cache
        Rails.cache.delete("master_datum_content")
    end
    
    # インスタンスメソッドとしてのキャッシュクリア（コールバック用）
    def clear_cache
        self.class.clear_cache
    end
end
