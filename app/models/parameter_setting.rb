class ParameterSetting < ApplicationRecord
  
  # 実際の取得にはConstantServiceを使用する!!

  # キャッシュキーの定義
  CURRENT_CACHE_KEY = 'parameter_setting_settings'
  
  # 最新の設定ハッシュを取得する（キャッシュ対応）
  def self.settings
    # Railsキャッシュから最新設定ハッシュを取得
    Rails.cache.fetch(CURRENT_CACHE_KEY, expires_in: 1.hour) do
      current_record = order(updated_at: :desc).first
      current_record ? current_record.settings : {}
    end
  end
  
  # 特定の設定キーの値を取得する（キャッシュ対応）
  def self.get(key, default = nil)
    # キャッシュした現在の設定ハッシュから値を取得
    settings_hash = settings
    
    if settings_hash.has_key?(key)
      settings_hash[key]
    else
      default
    end
  end
  
  # キャッシュをクリアする
  def self.clear_cache
    Rails.cache.delete(CURRENT_CACHE_KEY)
  end

  # レコード保存後にキャッシュをクリア
  after_save :clear_class_cache
  after_destroy :clear_class_cache
  
  def clear_class_cache
    self.class.clear_cache
  end

  # settingsのデフォルト値
  def settings
    settings_value = super
    return {} if settings_value.nil?
    
    # 文字列の場合はJSONとしてパース
    if settings_value.is_a?(String)
      begin
        JSON.parse(settings_value)
      rescue JSON::ParserError
        {}
      end
    else
      # 既にハッシュの場合はそのまま返す
      settings_value
    end
  end

  # settingsを設定する際にJSON文字列に変換
  def settings=(value)
    if value.is_a?(Hash)
      super(value.to_json)
    else
      super(value)
    end
  end

  # 設定を見やすく表示するためのメソッド
  def display_name
    "設定 ##{id || 'New'} (#{updated_at&.strftime('%Y-%m-%d %H:%M:%S') || '未保存'})"
  end
  
  # 現在使用中の設定かどうか
  def current?
    # 自分が最新のレコードかどうかを確認
    return false if id.nil? # 新規レコードの場合はfalse
    self.class.order(updated_at: :desc).first&.id == id
  end

  # responds_to?をオーバーライド
  def self.respond_to_missing?(method_name, include_private = false)
    method_name.to_s.upcase == method_name.to_s || super
  end
end
