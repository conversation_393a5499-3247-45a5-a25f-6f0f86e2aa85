class CardModifier < ApplicationRecord
    include AutoUidGenerator
    validates :card_id, uniqueness: true

    # カード修正が変更された時にMasterDatumのキャッシュをクリア
    after_save :clear_master_datum_cache
    after_destroy :clear_master_datum_cache
    after_update :clear_master_datum_cache

    def isUse()
        return !(self.use === 1)
    end
    
    def modifyKindToStr(useColor)
        str = "その他"
        case self.kind
        when 0
        str = "修正検討"
        when 1
        str = "追加検討"
        when 2
        str = "修正"
        when 3
        str = "追加"
        when 4
        str = "お試し"
        end
        if useColor && (self.kind == 2)
        str = "<font color=\"green\"><b>" + str + "</b></font>"
        elsif useColor && (self.kind == 3)
        str = "<font color=\"blue\"><b>" + str + "</b></font>"
        end
        return str.html_safe
    end
    
    def getTiming(num)
        case num
        when 0
        self.timing0
        when 1
        self.timing1
        when 2
        self.timing2
        end
    end
    
    
    def getText(num)
        case num
        when 0
        self.text0
        when 1
        self.text1
        when 2
        self.text2
        end
    end

    
    def getScript(num)
        case num
        when 0
        self.script0
        when 1
        self.script1
        when 2
        self.script2
        end
    end

    
    def isChangeText(num)
        temp = getText(num)
        if temp == nil
        return -1 # 変更なし
        elsif temp == ""
        return 1 # 削除
        else
        return 0 # 変更あり
        end
    end

    
    def isChangeScript(num)
        temp = getScript(num)
        if temp == nil
        return -1 # 変更なし
        elsif temp == ""
        return 1 # 削除
        else
        return 0 # 変更あり
        end
    end

    def isChangeTextStr(num)
        temp = getText(num)
        if temp == nil
          return "変更なし"
        elsif temp == ""
          return "削除"
        else
          return "変更あり"
        end
    end

    def isChangeScriptStr(num)
        temp = getScript(num)
        if temp == nil
          return "変更なし"
        elsif temp == ""
          return "削除"
        else
          return "変更あり"
        end
    end
    
    
    def getScriptOrStatus(num)
        status = isChangeScript(num)
        if status == 0
        return getScript(num)
        else
        return isChangeScriptStr(num)
        end
    end

    def isAnyChangeScript
        return isChangeScript(1) != -1 || isChangeScript(2) != -1 || isChangeScript(3) != -1
    end

    def isAnyChangeText
        return isChangeText(1) != -1 || isChangeText(2) != -1 || isChangeText(3) != -1
    end
    
    # MasterDatumのキャッシュをクリアするメソッド
    def clear_master_datum_cache
        MasterDatum.clear_cache
    end
end
