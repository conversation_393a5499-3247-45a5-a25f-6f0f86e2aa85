class User < ApplicationRecord
  has_secure_password

  # 名前のバリデーション
  # 正規表現 /\A[\p{L}\p{N}_]+\z/
  # \p{L}: Unicodeで「文字」（例: 日本語、アルファベット、その他の言語の文字）を指定。
  # \p{N}: Unicodeで「数字」（例: アラビア数字、その他の数字）を指定。
  # _ : アンダースコアを許可。
  # \A と \z で文字列全体を制約。
  # 空白や改行、タブ文字は許可されない。
  validates :name,
  presence: true,
  length: { in: 2..12 },
  format: {
    with: /\A[\p{L}\p{N}_]+\z/,
    message: "は日本語、英数字、アンダースコア(_)のみ使用できます。"
  }

  has_many :user_logs, foreign_key: :user_id, primary_key: :open_id, dependent: :destroy
  has_many :user_scores, foreign_key: :user_id, primary_key: :open_id, dependent: :destroy

  has_many :gifts, foreign_key: :user_id, primary_key: :open_id, dependent: :destroy

  has_many :iap_ios, foreign_key: :open_id, primary_key: :open_id, dependent: :destroy
  has_many :iap_and, foreign_key: :open_id, primary_key: :open_id, dependent: :destroy

  validates :open_id, presence: true, uniqueness: true

  scope :by_rate, -> { order(rate: :desc) }
  scope :by_wins, -> { order(wins: :desc) }

  def create_log(action, data = {})
    UserLog.create(uuid: create_uuid, user_id: open_id, action: action, data: data)
  end

  def create_uuid
    last_log = UserLog.order(created_at: :desc).first
    if last_log.present?
      return last_log.uuid + 1
    else
      return 1
    end
  end
  
  # ユーザーのカードコレクションをJSON形式で取得（BoxServiceに移行）
  def cards_json
    BoxService.get_box_json(self)
  end

  # ユーザーのスキンコレクションをJSON形式で取得
  def skins_json
    SkinService.get_skins_json(self)
  end

  # TODO ユーザーのレベルを取得
  def level
    exp = self.exp || 0
    level = 1
    # TODO レベルの計算式を変更する
    while exp >= 100
      exp -= 100
      level += 1
    end
    return level
  end

  #TODO 次のレベルまでの経験値を取得
  def next_level_exp
    return 100 * (level + 1)
  end

end