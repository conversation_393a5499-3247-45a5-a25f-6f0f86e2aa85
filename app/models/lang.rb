class Lang < ApplicationRecord
  include AutoUidGenerator
  # ISO 639-1 の全言語コードと名称
  AVAILABLE_LANGS = [
    { name: 'アファル語', locale: 'aa' },
    { name: 'アブハジアン語', locale: 'ab' },
    { name: 'アフリカンス語', locale: 'af' },
    { name: 'アカン語', locale: 'ak' },
    { name: 'アムハリア語', locale: 'am' },
    { name: 'アラビア語', locale: 'ar' },
    { name: 'アラゴネシア語', locale: 'an' },
    { name: 'アッサム語', locale: 'as' },
    { name: 'アバリコ語', locale: 'av' },
    { name: 'アイマラ語', locale: 'ay' },
    { name: 'アザルバイジャン語', locale: 'az' },
    { name: 'バシク語', locale: 'ba' },
    { name: 'ベラルーシ語', locale: 'be' },
    { name: 'ブルガリア語', locale: 'bg' },
    { name: 'ビハリ語', locale: 'bh' },
    { name: 'ビスラマ語', locale: 'bi' },
    { name: 'バンバラ語', locale: 'bm' },
    { name: 'ベンガル語', locale: 'bn' },
    { name: 'チベツト語', locale: 'bo' },
    { name: 'ブレトン語', locale: 'br' },
    { name: 'ボスニア語', locale: 'bs' },
    { name: 'カタロニア語', locale: 'ca' },
    { name: 'チェチェン語', locale: 'ce' },
    { name: 'チャモロ語', locale: 'ch' },
    { name: 'コルシカ語', locale: 'co' },
    { name: 'クリ語', locale: 'cr' },
    { name: 'チェコ語', locale: 'cs' },
    { name: 'キリルスラヴィア語', locale: 'cu' },
    { name: 'チュヴァシ語', locale: 'cv' },
    { name: 'ウェールズ語', locale: 'cy' },
    { name: 'デンマーク語', locale: 'da' },
    { name: 'ドイツ語', locale: 'de' },
    { name: 'ディベヒ語', locale: 'dv' },
    { name: 'デゾンガ語', locale: 'dz' },
    { name: 'エウェ語', locale: 'ee' },
    { name: 'グリーンランド語', locale: 'gl' },
    { name: 'グジャラト語', locale: 'gn' },
    { name: 'グジャラティ語', locale: 'gu' },
    { name: 'マン語', locale: 'gv' },
    { name: 'ハウサ語', locale: 'ha' },
    { name: 'ヘブライ語', locale: 'he' },
    { name: 'ヒンディー語', locale: 'hi' },
    { name: 'ヒリモトゥ語', locale: 'ho' },
    { name: 'クロアチア語', locale: 'hr' },
    { name: 'ハイチ語', locale: 'ht' },
    { name: 'ハンガリー語', locale: 'hu' },
    { name: 'アルメニア語', locale: 'hy' },
    { name: 'ヒロシビア語', locale: 'hz' },
    { name: 'インタリングア語', locale: 'ia' },
    { name: 'インドネシア語', locale: 'id' },
    { name: 'インタリング語', locale: 'ie' },
    { name: 'イグボ語', locale: 'ig' },
    { name: 'シチュアン語', locale: 'ii' },
    { name: 'イヌピアク語', locale: 'ik' },
    { name: 'イド語', locale: 'io' },
    { name: 'アイスランド語', locale: 'is' },
    { name: 'イタリア語', locale: 'it' },
    { name: 'インド語', locale: 'iu' },
    { name: '日本語', locale: 'ja' },
    { name: 'ジャワ語', locale: 'jv' },
    { name: 'グジア語', locale: 'ka' },
    { name: 'コンゴ語', locale: 'kg' },
    { name: 'キクユ語', locale: 'ki' },
    { name: 'クワンヤマ語', locale: 'kj' },
    { name: 'カザフ語', locale: 'kk' },
    { name: 'カラアリ語', locale: 'kl' },
    { name: 'セントラルカンマ語', locale: 'km' },
    { name: 'カンナダ語', locale: 'kn' },
    { name: '韓国語', locale: 'ko' },
    { name: 'カヌリ語', locale: 'kr' },
    { name: 'カシュミル語', locale: 'ks' },
    { name: 'クルド語', locale: 'ku' },
    { name: 'コミ語', locale: 'kv' },
    { name: 'コルニア語', locale: 'kw' },
    { name: 'キルギス語', locale: 'ky' },
    { name: 'ラテン語', locale: 'la' },
    { name: 'ルクセンブルク語', locale: 'lb' },
    { name: 'ガンダ語', locale: 'lg' },
    { name: 'リンバグァン語', locale: 'li' },
    { name: 'リンガラ語', locale: 'ln' },
    { name: 'ラオ語', locale: 'lo' },
    { name: 'リトアニア語', locale: 'lt' },
    { name: 'ルバカタンガ語', locale: 'lu' },
    { name: 'ラトビア語', locale: 'lv' },
    { name: 'マラガシ語', locale: 'mg' },
    { name: 'マーシャル語', locale: 'mh' },
    { name: 'マオリ語', locale: 'mi' },
    { name: 'マケドニア語', locale: 'mk' },
    { name: 'マラーヤラム語', locale: 'ml' },
    { name: 'モンゴル語', locale: 'mn' },
    { name: 'マラーティ語', locale: 'mr' },
    { name: 'マレー語', locale: 'ms' },
    { name: 'モルタ語', locale: 'mt' },
    { name: 'ビルマ語', locale: 'my' },
    { name: 'ナウル語', locale: 'na' },
    { name: 'ノルウェー語', locale: 'nb' },
    { name: 'ノートゥナンデベレ語', locale: 'nd' },
    { name: 'ネパル語', locale: 'ne' },
    { name: 'ンドンガ語', locale: 'ng' },
    { name: 'オランダ語', locale: 'nl' },
    { name: 'ノルウェー語', locale: 'nn' },
    { name: 'ノルウェー語', locale: 'no' },
    { name: 'ナンデベレ語', locale: 'nr' },
    { name: 'ナバジ語', locale: 'nv' },
    { name: 'チェワ語', locale: 'ny' },
    { name: 'オシュツカン語', locale: 'oc' },
    { name: 'オジバ語', locale: 'oj' },
    { name: 'オロモ語', locale: 'om' },
    { name: 'オリヤ語', locale: 'or' },
    { name: 'オセチアン語', locale: 'os' },
    { name: 'パンジャビ語', locale: 'pa' },
    { name: 'パリ語', locale: 'pi' },
    { name: 'ポーランド語', locale: 'pl' },
    { name: 'プシュトゥ語', locale: 'ps' },
    { name: 'ポルトガル語', locale: 'pt' },
    { name: 'ケチュア語', locale: 'qu' },
    { name: 'ロマンシュ語', locale: 'rm' },
    { name: 'ルンディ語', locale: 'rn' },
    { name: 'ルーマニア語', locale: 'ro' },
    { name: 'ロシア語', locale: 'ru' },
    { name: 'キニアルワンダ語', locale: 'rw' },
    { name: 'サンスクリット語', locale: 'sa' },
    { name: 'サルデーニャ語', locale: 'sc' },
    { name: 'シンド語', locale: 'sd' },
    { name: '北サーミ語', locale: 'se' },
    { name: 'サンゴ語', locale: 'sg' },
    { name: 'シンハラ語', locale: 'si' },
    { name: 'スロバキア語', locale: 'sk' },
    { name: 'スロベニア語', locale: 'sl' },
    { name: 'サモア語', locale: 'sm' },
    { name: 'ショナ語', locale: 'sn' },
    { name: 'ソマリ語', locale: 'so' },
    { name: 'アルバニア語', locale: 'sq' },
    { name: 'セルビア語', locale: 'sr' },
    { name: 'スワティ語', locale: 'ss' },
    { name: '南ソト語', locale: 'st' },
    { name: 'スンダ語', locale: 'su' },
    { name: 'スウェーデン語', locale: 'sv' },
    { name: 'スワヒリ語', locale: 'sw' },
    { name: 'タミル語', locale: 'ta' },
    { name: 'テルグ語', locale: 'te' },
    { name: 'タジク語', locale: 'tg' },
    { name: 'タイ語', locale: 'th' },
    { name: 'ティグリニャ語', locale: 'ti' },
    { name: 'トルクメン語', locale: 'tk' },
    { name: 'タガログ語', locale: 'tl' },
    { name: 'ツワナ語', locale: 'tn' },
    { name: 'トンガ語', locale: 'to' },
    { name: 'トルコ語', locale: 'tr' },
    { name: 'ツォンガ語', locale: 'ts' },
    { name: 'タタール語', locale: 'tt' },
    { name: 'トウィ語', locale: 'tw' },
    { name: 'タヒチ語', locale: 'ty' },
    { name: 'ウイグル語', locale: 'ug' },
    { name: 'ウクライナ語', locale: 'uk' },
    { name: 'ウルドゥー語', locale: 'ur' },
    { name: 'ウズベク語', locale: 'uz' },
    { name: 'ヴェンダ語', locale: 've' },
    { name: 'ベトナム語', locale: 'vi' },
    { name: 'ヴォラピュク語', locale: 'vo' },
    { name: 'ワロン語', locale: 'wa' },
    { name: 'ウォロフ語', locale: 'wo' },
    { name: 'コーサ語', locale: 'xh' },
    { name: 'イディッシュ語', locale: 'yi' },
    { name: 'ヨルバ語', locale: 'yo' },
    { name: 'チワン語', locale: 'za' },
    { name: '中国語', locale: 'zh' },
    { name: 'ズールー語', locale: 'zu' },
    { name: '繁体字', locale: 'zh-Hant' },
    { name: '簡体字', locale: 'zh-Hans' },
    { name: '英語', locale: 'en' },
  ].freeze

  validates :name, presence: true
  validates :locale, presence: true, uniqueness: true, inclusion: { in: AVAILABLE_LANGS.map { |lang| lang[:locale] } }
  validates :position, presence: true, uniqueness: true

  before_validation :ensure_unique_position
  default_scope { order(position: :asc) }

  def self.main_lang
    # 位置が最小のレコードを取得
    # キャッシュする
    Rails.cache.fetch('langs_main_lang', expires_in: 1.minute) do
      Lang.order(:position).first
    end
  end

  def self.order_by_position
    # キャッシュする
    Rails.cache.fetch('langs_order_by_position', expires_in: 1.minute) do
      Lang.order(:position).pluck(:locale)
    end
  end

  def self.options_for_select
    AVAILABLE_LANGS.sort_by { |lang| lang[:name] }.map { |lang| ["#{lang[:name]} (#{lang[:locale]})", lang[:locale]] }
  end

  def self.available_locales
    AVAILABLE_LANGS.map { |lang| lang[:locale] }
  end

  def self.find_name_by_locale(locale)
    lang = AVAILABLE_LANGS.find { |l| l[:locale] == locale }
    lang ? lang[:name] : nil
  end

  def self.locales
    # キャッシュする
    Rails.cache.fetch('langs_locales', expires_in: 1.minute) do
      Lang.pluck(:locale)
    end
  end

  private

  def ensure_unique_position
    return if position.present? && Lang.where(position: position).where.not(id: id).empty?
    
    # 指定されたpositionが既に使用されている場合、
    # そのposition以降のレコードを1つずつずらして空きを作る
    if position.present?
      Lang.where('position >= ?', position)
          .order(position: :desc)
          .each do |lang|
        lang.update_column(:position, lang.position + 1)
      end
    else
      # positionが指定されていない場合は最後に追加
      self.position = (Lang.maximum(:position) || 0) + 1
    end
  end
end
