class Pack < ApplicationRecord
  include AutoUidGenerator
  include PeriodManagement
  attr_accessor :selected_cards
  belongs_to :period, optional: true

  # デフォルトのソート順
  default_scope { order(sort_order: :asc) }

  # Validations
  validate :validate_pack_probabilities
  validate :validate_skin_probabilities
  validate :validate_skin_probabilities_sum

  # Default values for the new jsonb columns
  after_initialize :set_default_values, if: :new_record?

  def set_default_values
    self.rarity_prob ||= {"legend4" => 2, "gold3" => 8, "silver2" => 35, "bronze1" => 55}
    self.edition_prob ||= {"normal" => 94, "shine" => 5, "premium" => 1}
    self.change_pts ||= {"legend4" => 400, "gold3" => 300, "silver2" => 200, "bronze1" => 100}
    self.skin_data ||= []
    self.sort_order ||= 0
  end
  
  def set_card_data(card_data, rarity_prob)
    card_data_with_probability = []
    card_data.each do |card|
      rarity_key = rarity_to_key(card["rarity"])
      probability = rarity_prob[rarity_key]
      card_data_with_probability << {card_id: card["id"], rarity: card["rarity"], probability: probability}
    end
    return card_data_with_probability
  end

  # Validate that probabilities sum up to 100%
  def validate_pack_probabilities
    rarity_sum = rarity_prob.values.sum
    edition_sum = edition_prob.values.sum
    
    unless (rarity_sum - 100).abs < 0.000001
      errors.add(:base, "レアリティ確率の合計が100%ではありません (合計: #{rarity_sum}%)")
    end
    
    unless (edition_sum - 100).abs < 0.000001
      errors.add(:base, "エディション確率の合計が100%ではありません (合計: #{edition_sum}%)")
    end
  end
  
  # Validate skin data format
  def validate_skin_probabilities
    return if skin_data.blank? # スキンデータが空の場合はチェックをスキップ
    total_probability = skin_data.sum { |skin| skin["probability"].to_f }
    if total_probability != 100.0
      errors.add(:skin_data, "の確率の合計は100%である必要があります")
    end
  end

  private

  def validate_skin_probabilities_sum
    return if skin_data.blank? # スキンデータが空の場合はチェックをスキップ
    total_probability = skin_data.sum { |skin| skin["probability"].to_f }
    if total_probability != 100.0
      errors.add(:skin_data, "の確率の合計は100%である必要があります")
    end
  end
end
