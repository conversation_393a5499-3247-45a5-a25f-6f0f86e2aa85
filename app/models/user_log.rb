class UserLog < ApplicationRecord
    

    LOG_TYPE_HASH = {
        login: {name:"ログイン", icon: "fas fa-sign-in-alt"},
        register: {name:"初回登録", icon: "fas fa-user-plus"},
        infos: {name:"お知らせ取得", icon: "fas fa-newspaper"},
        rankings: {name:"ランキング取得", icon: "fas fa-ranking-star"},
        update_decks: {name:"デッキ更新", icon: "fa-solid fa-deck"},
        update_settings: {name:"設定更新", icon: "fa-solid fa-gear"},
        update_tutorial: {name:"チュートリアル更新", icon: "fa-solid fa-book"},
        start_matching: {name:"マッチング開始", icon: "fa-solid fa-gamepad"},
        create_room: {name:"ルーム作成", icon: "fa-solid fa-room"},
        join_room: {name:"ルーム参加", icon: "fa-solid fa-room"},
        spectate_room: {name:"観戦開始", icon: "fa-solid fa-room"},
        solo_progress: {name:"ソロバトル進捗更新", icon: "fa-solid fa-battle-axe"},
        open_pack: {name:"パック引く", icon: "fas fa-dice"},
        normal_purchase: {name:"通常課金", icon: "fas fa-coins"}
    }.freeze

    KEY_TO_NAME = {
        amount: "金額",
        change_dia: "ダイヤ変動量",
        item_id: "アイテムID",
        pull_count: "パック引いた回数",
        # results: "パック引いた結果",
        pack_id: "パックID",
        register: "初回登録",
        infos: "お知らせ取得",
        rankings: "ランキング取得",
        update_decks: "デッキ更新",
        device: "デバイス",
        ip_address: "IPアドレス"
    }

    def value
        if(action.present? && LOG_TYPE_HASH[action.to_sym].present?)
            LOG_TYPE_HASH[action.to_sym]
        else
            {name: "不明", icon: "fa-solid fa-question"}
        end
    end

    def self.log_type_name(log_type)
        LOG_TYPE_HASH[log_type]
    end


end
