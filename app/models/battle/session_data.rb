module Battle
  class SessionData
    attr_accessor :id, :status, :rank, :room_id, :created_at, :room_type

    def initialize(id:, status:, rank:, room_id:, created_at:, room_type:)
      @id = id
      @status = status
      @rank = rank
      @room_id = room_id
      @created_at = created_at
      @room_type = room_type
    end

    def to_s
      "BattleSessionData(ID: #{@id}, Status: #{@status}, Rank: #{@rank}, RoomID: #{@room_id}, CreatedAt: #{@created_at}, RoomType: #{@room_type})"
    end
  end
end 