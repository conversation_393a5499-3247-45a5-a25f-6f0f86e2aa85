module Battle
  class PlayerMatchPack
    attr_accessor :player_index_in_room, :random_seed, :deck_data

    def initialize(player_index_in_room, random_seed, deck_data)
      @player_index_in_room = player_index_in_room
      @random_seed = random_seed
      @deck_data = deck_data
    end

    def to_json
      {
        PlayerIndexInRoom: @player_index_in_room,
        RandomSeed:        @random_seed,
        DeckDataJson:      @deck_data,
      }.to_json
    end
  end
end
