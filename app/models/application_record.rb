class ApplicationRecord < ActiveRecord::Base
  primary_abstract_class
  scope :within_valid_period, -> {
    # デフォルトの条件（直接start_atとend_atを使用）
    base_condition = where("start_at <= ? AND end_at >= ?", Time.current, Time.current)
    
    # period_idカラムが存在するモデルの場合
    if column_names.include?('period_id')
      # period_idが設定されている場合とそうでない場合で分ける
      condition_with_period = joins("LEFT JOIN periods ON periods.uid = #{table_name}.period_id")
        .where("periods.start_at <= ? AND periods.end_at >= ? AND #{table_name}.period_id IS NOT NULL", 
              Time.current, Time.current)
      
      # period_idがnullまたは-1の場合は直接日付で判定
      condition_without_period = where("(#{table_name}.period_id IS NULL OR #{table_name}.period_id = -1) 
                                      AND #{table_name}.start_at <= ? 
                                      AND #{table_name}.end_at >= ?", 
                                    Time.current, Time.current)
      
      # 両方の条件を結合
      condition_with_period.or(condition_without_period)
    else
      # period_idカラムを持たないモデルは単純に日付のみで判定
      base_condition
    end
  }

  # period_start_atの値によるソートを行うスコープ
  scope :order_by_period_start_at, -> (direction = :desc) {
    # period_start_atのロジックをSQLで実装
    order_clause = "CASE WHEN period_id IS NOT NULL AND period_id != -1 THEN " +
                    "(SELECT start_at FROM periods WHERE periods.uid = #{table_name}.period_id) " +
                    "ELSE #{table_name}.start_at END"

    order(Arel.sql("#{order_clause} #{direction.to_s.upcase}"))
  }

  def image_url(host:, port:)
    if self.image.attached?
      return Rails.application.routes.url_helpers.rails_blob_url(self.image, host:host, port:port)
    else
      return nil
    end
  end
end
