class MasterUser < ApplicationRecord
  # Deviseモジュール
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable, :trackable,
         :omniauthable, omniauth_providers: [:google_oauth2]  # OmniAuth対応

  # 許可されたメールアドレスのリスト
  ALLOWED_EMAILS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ].freeze

  # メールアドレスのバリデーション
  validate :validate_email_domain

  def self.from_omniauth(auth)
    where(provider: auth.provider, uid: auth.uid).first_or_create do |user|
      user.provider = auth.provider
      user.uid = auth.uid
      user.email = auth.info.email
      user.password = Devise.friendly_token[0, 20]
    end
  end

  private

  def validate_email_domain
    unless ALLOWED_EMAILS.include?(email)
      errors.add(:email, "は許可されていません。管理者にお問い合わせください。")
    end
  end
end
