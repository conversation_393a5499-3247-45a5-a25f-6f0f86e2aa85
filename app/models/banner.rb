class Banner < ApplicationRecord
  has_one_attached :banner_image
  belongs_to :period, optional: true
  include PeriodManagement

  validates :uid, presence: true, uniqueness: true
  validates :title, presence: true
  validates :view_type, presence: true
  validates :view_uid, presence: true
  validates :start_at, presence: true, if: -> { period_id.blank? }
  validates :end_at, presence: true, if: -> { period_id.blank? }
  validate :end_at_after_start_at

  def banner_image_url(host:, port:)
    banner_image.attached? ? Rails.application.routes.url_helpers.rails_blob_url(banner_image, host: host, port: port) : nil
  end

  private

  def end_at_after_start_at
    return if end_at.blank? || start_at.blank?
    if end_at <= start_at
      errors.add(:end_at, "は開始日時より後の日時を指定してください")
    end
  end
end
