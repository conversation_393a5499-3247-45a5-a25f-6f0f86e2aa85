# ActionCableのメッセージモデル
module ActionCable
  class ProtocolModel
    attr_accessor :command, :identifier, :data

    def initialize(command, identifier, data)
      @command = command
      @identifier = identifier
      @data = data
    end

    # JSONシリアライズのためのメソッド
    def to_json(options = nil)
      {
        command: @command,
        identifier: @identifier,
        data: @data
      }.to_json(options)
    end
  end
end 