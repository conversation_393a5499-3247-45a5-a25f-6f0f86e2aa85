class Period < ApplicationRecord
  include AutoUidGenerator
  belongs_to :parent, class_name: "Period", optional: true

  def self.get_period_str(model)
    if model.period_id.nil? || model.period_id == -1
      return format_date_range(model.start_at, model.end_at)
    else
      period = Period.find_by(uid: model.period_id)
      period&.name || format_date_range(model.start_at, model.end_at)
    end
  end

  private

  def self.format_date_range(start_at, end_at)
    return "期間未定" if start_at.nil? && end_at.nil?
    return "開始日未定" if start_at.nil?
    return "終了日未定" if end_at.nil?
    
    "#{start_at.strftime("%Y/%m/%d %H:%M")}~#{end_at.strftime("%Y/%m/%d %H:%M")}"
  end
end
