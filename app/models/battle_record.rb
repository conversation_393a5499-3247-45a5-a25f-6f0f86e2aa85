class BattleRecord < ApplicationRecord
  include AutoUidGenerator
  belongs_to :user1, class_name: 'User', foreign_key: 'user_id_1', optional: true
  belongs_to :user2, class_name: 'User', foreign_key: 'user_id_2', optional: true
  belongs_to :winner, class_name: 'User', foreign_key: 'winner_id', optional: true

  validates :uid, presence: true, uniqueness: true

  private

  def validate_users
    if user_id_1.blank? && user_id_2.blank?
      errors.add(:base, "At least one user must be present")
    end
  end
end
