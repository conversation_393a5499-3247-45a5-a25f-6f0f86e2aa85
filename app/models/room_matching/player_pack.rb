module RoomMatching
  class PlayerPack
    attr_accessor :player_id, :name, :level, :banner_id, :leader_icon, :win_count

    def initialize(player_id, name, level, banner_id, leader_icon)
      @player_id = player_id
      @name = name
      @level = level
      @banner_id = banner_id
      @leader_icon = leader_icon
      @win_count = 0
    end

    def as_json(options = nil)
      {
          id:   @player_id,
          name: @name,
          lv:   @level,
          banr: @banner_id,
          icon: @leader_icon,
          winc: @win_count,
      }
    end
  end
end
