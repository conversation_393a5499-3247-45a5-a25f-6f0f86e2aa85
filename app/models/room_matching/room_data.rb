module RoomMatching
  class RoomData
    attr_accessor :room_id, :owner_id, :player0_pack, :player1_pack, :player0_ready, :player1_ready, :player_count

    def initialize(room_id, owner_id)
      @room_id = room_id
      @owner_id = owner_id
      @player0_pack = nil
      @player1_pack = nil

      @player0_ready = false
      @player1_ready = false
      @player_count = 0 # ルーム作成時は0人（座った時にプラス）
    end

    # ルームが満員かどうか
    def is_full_players?
      @player0_pack && @player1_pack
    end

    # 人数を増やす
    def increment_player_count
      @player_count += 1
    end

    # 人数を減らす
    def decrement_player_count
      @player_count -= 1
    end

    def to_json
      {
          room_id:      @room_id,
          owner_id:     @owner_id,
          p0_p:         @player0_pack&.as_json,
          p1_p:         @player1_pack&.as_json,
          p0_ready:     @player0_ready,
          p1_ready:     @player1_ready,
          player_count: @player_count,
      }.to_json
    end
  end
end
