class ServerSetting < ApplicationRecord
    include AutoUidGenerator
    
    def self.current
        current = ServerSetting.order(created_at: :desc).first
        if current.nil?
            # uidの最大値を取得し、なければ0を使用
            max_uid = ServerSetting.maximum(:uid)
            next_uid = max_uid.nil? ? 0 : max_uid + 1
            current = ServerSetting.create(version_id: -1, uid: next_uid)
        end
        return current
    end

    def self.version_id
        current.version_id
    end

    def self.version
        ServerVersion.find_by(uid: self.version_id)
    end
    
    
end
