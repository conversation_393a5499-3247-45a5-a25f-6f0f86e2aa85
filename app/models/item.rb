class Item < ApplicationRecord
  include AutoUidGenerator
  validates :item_type, presence: true
  validates :uid, presence: true, uniqueness: true
  validates :name, presence: true
  validates :desc, presence: true

  ITEM_TYPES = [
    { name: 'アイコン', value: 'icon' },
    { name: 'アイコンフレーム', value: 'icon_frame' },
    { name: 'カードスリーブ', value: 'card_sleeve' },
    { name: 'プレイマット', value: 'playmat' },
    { name: 'BGM', value: 'bgm' },
    { name: '称号', value: 'title' },
    { name: '宝箱', value: 'chest' },
    { name: 'パックチケット', value: 'pack_ticket' },
    { name: '構築済みデッキ', value: 'prebuilt_deck' },
  ].freeze

  ALL_ITEM_TYPES = ITEM_TYPES + [
    { name: 'ダイヤ', value: 'dia' },
    { name: '有償ダイヤ', value: 'paid_dia' },
    { name: '報酬無し', value: 'none' },
    { name: 'メインパックチケット', value: 'main_pack_ticket' },
    { name: 'スペシャルパック', value: 'special_pack' },
    { name: 'カードの砂', value: 'card_sand' },
    { name: '通常カード', value: 'nr_card' },
    { name: 'シャイン加工カード', value: 'sh_card' },
    { name: 'プレミアム加工カード', value: 'pr_card' },
    { name: 'パックポイント', value: 'pack_point' },
  ].freeze

  ITEM_ICONS = 
  {
    'icon' => 'fas fa-user-circle',
    'icon_frame' => 'far fa-circle',
    'card_sleeve' => 'fas fa-window-restore',
    'playmat' => 'fas fa-chess-board',
    'bgm' => 'fas fa-compact-disc',
    'title' => 'fas fa-crown',
    
    'chest' => 'fas fa-box',
    'pack_ticket' => 'fas fa-ticket-alt',


    'main_pack_ticket' => 'fas fa-ticket-alt',
    'special_pack' => 'fas fa-ticket-alt',
    'prebuilt_deck' => 'fas fa-layer-group',
    'none' => 'fas fa-ban',
    'dia' => 'fas fa-gem',
    'card_sand' => 'fa-solid fa-snowflake',
    'nr_card' => 'fal fa-square',
    'sh_card' => 'far fa-square',
    'pr_card' => 'fas fa-square',
    'pack_point' => 'fas fa-gem',
  }.freeze

  RARITIES = [
    { name: 'ブロンズ', value: 1 },
    { name: 'シルバー', value: 2 },
    { name: 'ゴールド', value: 3 },
    { name: 'レジェンド', value: 4 },
  ].freeze

  CHESTS = [
    { name: 'ブロンズ', value: 1 },
    { name: 'シルバー', value: 2 },
    { name: 'ゴールド', value: 3 },
    { name: 'レジェンド', value: 4 },
  ].freeze

  ITEM_RULES = {
    'icon' => {'name' => 'アイコン', 'icon' => 'fas fa-user-circle', 'count' => false, 'range' => false, 'item_id' => true},
    'icon_frame' => {'name' => 'アイコンフレーム', 'icon' => 'far fa-circle', 'count' => false, 'range' => false, 'item_id' => true},
    'card_sleeve' => {'name' => 'カードスリーブ', 'icon' => 'fas fa-window-restore', 'count' => false, 'range' => false, 'item_id' => true},
    'playmat' => {'name' => 'プレイマット', 'icon' => 'fas fa-chess-board', 'count' => false, 'range' => false, 'item_id' => true},
    'bgm' => {'name' => 'BGM', 'icon' => 'fas fa-compact-disc', 'count' => false, 'range' => false, 'item_id' => true},
    'title' => {'name' => '称号', 'icon' => 'fas fa-crown', 'count' => false, 'range' => false, 'item_id' => true},
    'chest' => {'name' => '宝箱', 'icon' => 'fas fa-box', 'count' => true, 'range' => true, 'item_id' => true},
    'pack_ticket' => {'name' => 'パックチケット', 'icon' => 'fas fa-ticket-alt', 'count' => true, 'range' => true, 'item_id' => true},
    'prebuilt_deck' => {'name' => '構築済みデッキ', 'icon' => 'fas fa-layer-group', 'count' => false, 'range' => false, 'item_id' => true},

    'dia' => {'name' => 'ダイヤ', 'icon' => 'fas fa-gem', 'count' => true, 'range' => true, 'item_id' => false},
    'paid_dia' => {'name' => '有償ダイヤ', 'icon' => 'fas fa-gem', 'count' => true, 'range' => true, 'item_id' => false},
    'none' => {'name' => '報酬無し', 'icon' => 'fas fa-ban', 'count' => false, 'range' => false, 'item_id' => false},
    'main_pack_ticket' => {'name' => 'メインパックチケット', 'icon' => 'fas fa-ticket-alt', 'count' => true, 'range' => true, 'item_id' => false},
    'special_pack' => {'name' => 'スペシャルパック', 'icon' => 'fas fa-ticket-alt', 'count' => true, 'range' => true, 'item_id' => false},
    'card_sand' => {'name' => 'カードの砂', 'icon' => 'fa-solid fa-snowflake', 'count' => true, 'range' => true, 'item_id' => true},
    'nr_card' => {'name' => '通常カード', 'icon' => 'fal fa-square', 'count' => true, 'range' => false, 'item_id' => true},
    'sh_card' => {'name' => 'シャイン加工カード', 'icon' => 'far fa-square', 'count' => true, 'range' => false, 'item_id' => true},
    'pr_card' => {'name' => 'プレミアム加工カード', 'icon' => 'fas fa-square', 'count' => true, 'range' => false, 'item_id' => true},
    'pack_point' => {'name' => 'パックポイント', 'icon' => 'fas fa-gem', 'count' => true, 'range' => true, 'item_id' => true},
  }.freeze

  def self.get_all_item_rules
    ITEM_RULES
  end

  def self.rewards_to_s(rewards)
    rewards.map do |reward|
      item_id = reward['item_id']
      item_type = reward['item_type']
      count = reward['count']
      ext = reward['ext']
      icon = Item.item_icon(item_type)
      
      case item_type
      when 'none'
        "報酬無し"
      when 'dia'
        "<i class='#{icon}'></i>×#{count}"
      else
        "<i class='#{icon}'></i> #{MasterDatum.get_item_name(item_type, item_id.to_i)}"
      end
    end.join(', ')
  end

  def self.get_rule(item_type)
    rule = ITEM_RULES[item_type]
    if rule.present?
      return rule
    else
      return {'count' => false, 'range' => false}
    end
  end

  def self.get_item_name(item_type, item_id)

    data = MasterDatum.get_all()
    if data.present?
        sheet = data['_' + item_type]
        if sheet.present?
            return sheet[item_id]['Name__jp']
        else
            return ""
        end
    else
        return ""
    end
  end

  def self.options_for_select
    ITEM_TYPES.map { |type| [type[:name], type[:value]] }
  end

  def self.all_options_for_select
    ALL_ITEM_TYPES.map { |type| [type[:name], type[:value]] }
  end

  def item_type_name
    ALL_ITEM_TYPES.find { |type| type[:value] == item_type }[:name]
  end

  def self.item_type_name(item_type)
    data = ALL_ITEM_TYPES.find { |type| type[:value] == item_type }
    data[:name] if data
  end

  def self.item_icon(item_type)
    ITEM_ICONS[item_type]
  end

  def self.rarity_options
    RARITIES.map { |rarity| [rarity[:name], rarity[:value]] }
  end

  def self.chest_options
    CHESTS.map { |chest| [chest[:name], chest[:value]] }
  end

  validates :item_type, inclusion: { in: ITEM_TYPES.map { |type| type[:value] } }

  
  def self.get_data(item_key)
    data = MasterDatum.get_all()
    if data.present?
        return data['_' + item_key]
    else
        return nil
    end
  end

  # マスターデータから選択用のデータを取得
  def self.md_options_for_select(item_key)
    data = self.get_data(item_key)
    if data.present?
        return data.map { |item| ["#{item["Name__jp"]} (#{item["ID"]})", item["ID"]] }
    else
        return []
    end
  end

  def self.md_options_cards_for_select()
    data = MasterDatum.get_cards()
    if data.present?
        return data.map { |item| ["#{item["ID"]}  #{item["Name"]} (#{item["Role"]}/#{item["Cost"]}/#{item["Power"]})", item["ID"]] }
    else
        return []
    end
  end

  def self.card_sand_options
    data = self
  end
end
