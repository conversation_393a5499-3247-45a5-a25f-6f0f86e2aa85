class LoginBonus < ApplicationRecord
  include AutoUidGenerator
  include PeriodManagement
  belongs_to :period, optional: true

  validates :uid, presence: true, uniqueness: true
  validates :title, presence: true
  validates :desc, presence: true
  validate :validate_dates

  private
  def validate_dates
    if start_at && end_at && start_at >= end_at
      errors.add(:end_at, "must be after start_at")
    end
  end
end
