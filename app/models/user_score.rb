class UserScore < ApplicationRecord
  belongs_to :user, foreign_key: :user_id, primary_key: :open_id

  validates :category, presence: true
  validates :score, presence: true
  validates :user_id, presence: true
  validates :category, uniqueness: { scope: :user_id }

  CATEGORY_TYPES = [
    { name: "レート", value: "rate" },
    { name: "攻撃数", value: "atk" },
    { name: "防御数", value: "def" },
    { name: "勝利数", value: "win" },
    { name: "ダメージ数", value: "damage" },
  ].freeze

  def self.options_for_select
    CATEGORY_TYPES.map { |type| [type[:name], type[:value]] }
  end

  def user_score_name
    # カテゴリータイプに含まれていればnameを返す
    if CATEGORY_TYPES.find { |type| type[:value] == category }
      CATEGORY_TYPES.find { |type| type[:value] == category }[:name]
    else
      category
    end
  end

  # カテゴリー別ランキング取得
  scope :ranking, ->(category) { where(category: category).order(score: :desc) }

  

end
