module ProcessFormatRewards
    extend ActiveSupport::Concern
    # main_pack_ticketを変換するメソッド
    def process_format_rewards(rewards_data)
        # 元のrewardsデータを取得
        original_rewards = rewards_data
        return [] if original_rewards.blank?

        # メインパックの情報を取得
        main_pack = PackService.fetch_current_main_pack
        main_pack_uid = main_pack&.uid || 0

        # main_pack_ticketを変換
        original_rewards.map do |reward|
        if reward['item_type'] == 'main_pack_ticket'
            reward = reward.dup # 元のデータを変更しないようにコピーを作成
            reward['item_type'] = 'pack_ticket'
            reward['item_id'] = main_pack_uid
        end
        reward
        end
    end
end