module PeriodManagement
  extend ActiveSupport::Concern

  # モデルに追加されるインスタンスメソッド
  def period_start_at
    if period_id.present? && period_id != -1
      period = Period.find_by(uid: period_id)
      period&.start_at || self[:start_at]
    else
      self[:start_at]
    end
  end

  def period_end_at
    if period_id.present? && period_id != -1
      period = Period.find_by(uid: period_id)
      period&.end_at || self[:end_at]
    else
      self[:end_at]
    end
  end
end 