# app/models/concerns/markdown_converter.rb
require 'reverse_markdown'
require 'nokogiri'

module MarkdownConverter
    extend ActiveSupport::Concern

    class_methods do
        def rich_text_to_markdown(rich_text,host:,port:)
            return "" unless rich_text

            html = rich_text.body.to_s
            doc = Nokogiri::HTML::DocumentFragment.parse(html)

            # ActionTextの画像タグをMarkdown形式に変換
            doc.css("action-text-attachment").each_with_index do |node, index|
                # TODO 可能ならsgidを取得して、attachableを取得する
                attachment = rich_text.body.attachments[index]
                next unless attachment
                url = Rails.application.routes.url_helpers.rails_blob_url(attachment, host: host, port: port)
                markdown_img = "![image](#{url})"
                node.replace(markdown_img)
            end
            markdown = ReverseMarkdown.convert(doc.to_html)
            markdown.gsub!('\\_', '_') # マークダウンは自動で_を避けるので、それを防ぐ
        end
    end
end
