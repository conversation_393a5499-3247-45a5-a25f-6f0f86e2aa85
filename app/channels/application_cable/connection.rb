module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :player_id
    identified_by :admin_user

    # def connect
    #   self.player_id = find_verified_player
    # end


    def connect
      if request.params[:client_type] == "admin"
        Rails.logger.info "Admin connection attempt with params: #{request.params.inspect}"

        admin_from_cookie = find_admin_from_cookie
        if admin_from_cookie
          self.admin_user = admin_from_cookie
          Rails.logger.info "Admin接続成功 (cookie): #{admin_user.id}"
          return
        end

        if request.params[:token].present?
          admin_from_token = find_admin_from_token(request.params[:token])
          if admin_from_token
            self.admin_user = admin_from_token
            Rails.logger.info "Admin接続成功 (token): #{admin_user.id}"
            return
          end
        end

        Rails.logger.info "Admin authentication failed"
        reject_unauthorized_connection
        return
      end

      INFO("Connection", "ActionCable接続試行: params=#{request.params.inspect}")
      self.player_id = find_verified_player

      # if @@connections[player_id].present?
      #   # 古い接続を切断し、新しい接続を許可
      #   old_connection = @@connections[player_id]
      #   INFO("Connection", "既存の接続を切断: #{player_id}")
      #   old_connection.close_with_error("ESACC-0003", "新しい接続が確立されました")
      # end

      # @@connections[player_id] = self
      # INFO("Connection", "接続成功: #{player_id}")
    end

    def disconnect
      # return if admin_user.present?

      # logger.info "Connection disconnected: #{player_id}"
      # UserStatusService.handle_disconnect(player_id)
    end


    # 構造化ログメソッド
    def INFO(category, message)
      Rails.logger.info(level: "info", channel: "ApplicationCable::Connection", id: request.params[:ID], category: category, message: message)
    end

    def DEBUG(category, message)
      Rails.logger.debug(level: "debug", channel: "ApplicationCable::Connection", id: request.params[:ID], category: category, message: message)
    end

    def ERROR(category, message)
      Rails.logger.error(level: "error", channel: "ApplicationCable::Connection", id: request.params[:ID], category: category, message: message)
    end

    def get_player_id
      if player_id.nil?
        close_with_error("ESACC-0004", "Player ID is nil")
      end
      player_id
    end

    # カスタム切断メソッド
    def close_with_error(error_code, reason)
      close(reason: { error_code: "#{error_code}", reason: "#{reason}" }, reconnect: false)
      INFO("Connection", "切断: #{error_code} #{reason}")
    end

    # 接続確認用
    def alive?
      websocket.present? && websocket.alive?
    end

    def find_admin_from_cookie
      admin_id = cookies.signed[:admin_id]
      Rails.logger.info "Admin ID from cookie: #{admin_id.inspect}"

      if admin_id.present?
        admin = MasterUser.find_by(id: admin_id)
        return admin if admin
      end

      nil
    end

    def find_admin_from_token(token)
      if token.start_with?("admin-")
        admin_id = token.split("-")[1]
        admin = MasterUser.find_by(id: admin_id)
        return admin if admin
      end

      nil
    end

    def find_verified_player
      INFO("Connection", "ActionCable接続試行: params=#{request.params.inspect}")
      id = request.params[:ID].to_s # ID はJWTトークン
      INFO("Connection", "JWT token: #{id}")
      if id.blank?
        INFO("Connection", "認証失敗: ID不在")
        close_with_error("ESACC-0001", "接続エラー")
      end

      INFO("Connection", "JWT token: #{id}")
      player = ApplicationController.authenticate_with_jwt(id)

      if player
        # テストのためにエラーを表示したい場合は以下のコメントを外す
        # close_with_error("ESACC-0000", "接続成功TestError")
        # 認証成功の場合は単純にIDを返す
        INFO("Connection", "認証成功: #{player.open_id}")
        player.open_id.to_s
      else
        # JWT認証に失敗した場合
        INFO("Connection", "認証失敗: JWT無効")
        close_with_error("ESACC-0002", "接続エラー")
      end
    end
  end
end
