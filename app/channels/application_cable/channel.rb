module ApplicationCable
  class Channel < ActionCable::Channel::Base
    protected
      # 構造化ログメソッド
      def self.INFO(category, message, id = nil)
        Rails.logger.info(
          level:    "info",
          worker:   Process.pid,
          channel:  self.name,
          id:       id,
          category: category,
          message:  message
        )
      end

      def self.DEBUG(category, message, id = nil)
        Rails.logger.debug(
          level:    "debug",
          worker:   Process.pid,
          channel:  self.name,
          id:       id,
          category: category,
          message:  message
        )
      end

      def self.ERROR(category, message, id = nil)
        Rails.logger.error(
          level:    "error",
          worker:   Process.pid,
          channel:  self.name,
          id:       id,
          category: category,
          message:  message
        )
      end

      def self.WARN(category, message, id = nil)
        Rails.logger.warn(
          level:    "warn",
          worker:   Process.pid,
          channel:  self.name,
          id:       id,
          category: category,
          message:  message
        )
      end

      # カスタム切断メソッド
      def reject_with_error(error_code, reason)
        transmit({
          command:    "reject",
          error_code: error_code,
          reason:     reason,
          reconnect:  false,
        })
        reject
        self.class.INFO("Connection", "reject: #{error_code} #{reason}")
      end

      def kick_player(error_code, reason)
        transmit({
          command:    "kick",
          error_code: error_code,
          reason:     reason,
          reconnect:  false,
        })
        self.class.INFO("Connection", "kick: #{error_code} #{reason}")
      end

      def connection_alive?
        connection&.alive?
      end
  end
end
