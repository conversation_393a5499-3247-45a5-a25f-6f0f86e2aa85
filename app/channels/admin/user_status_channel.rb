module Admin
  class UserStatusChannel < ApplicationCable::Channel
    def subscribed
      Rails.logger.info "Admin::UserStatusChannel#subscribed attempt by: #{admin_user.inspect}"
      
      if admin_user.present?
        stream_from "admin_user_status_updates"
        Rails.logger.info "Admin subscribed to user status updates: #{admin_user.id}"
        
        transmit({
          matching: UserStatusService.get_users_by_status('matching'),
          matched: UserStatusService.get_users_by_status('matched'),
          in_room: UserStatusService.get_users_by_status('in_room'),
          stats: UserStatusService.get_stats
        })
      else
        Rails.logger.info "Non-admin attempted to subscribe to admin channel"
        reject
      end
    end

    def unsubscribed
      Rails.logger.info "Admin::UserStatusChannel#unsubscribed"
    end
  end
end

