module Admin
  class MatchStatusChannel < ApplicationCable::Channel
    def subscribed
      Rails.logger.info "Admin::MatchStatusChannel#subscribed attempt by: #{admin_user.inspect}"
      
      if admin_user.present?
        match_type = params[:match_type]
        
        if match_type.in?(['rank', 'free', 'room'])
          stream_from "admin_#{match_type}_match_status"
          Rails.logger.info "Admin subscribed to #{match_type} match status updates: #{admin_user.id}"
          
          # Gửi dữ liệu ban đầu cho client
          case match_type
          when 'rank'
            transmit_rank_match_data
          when 'free'
            transmit_free_match_data
          when 'room'
            transmit_room_match_data
          end
        else
          Rails.logger.info "Invalid match type: #{match_type}"
          reject
        end
      else
        Rails.logger.info "Non-admin attempted to subscribe to admin match status channel"
        reject
      end
    end

    def unsubscribed
      Rails.logger.info "Admin::MatchStatusChannel#unsubscribed"
    end
    
    private
    
    def transmit_rank_match_data
      total_users = UserStatusService.count_by_channel_type('rank_matching')
      matching_users = UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching')
      matched_users = UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched')
      
      stats = {
        total: total_users,
        matching: matching_users.size,
        matched: matched_users.size
      }
      
      transmit({
        matching_users: matching_users,
        matched_users: matched_users,
        stats: stats,
        timestamp: Time.now.to_i
      })
    end
    
    def transmit_free_match_data
      total_users = UserStatusService.count_by_channel_type('free_matching')
      matching_users = UserStatusService.get_users_by_channel_and_status('free_matching', 'matching')
      matched_users = UserStatusService.get_users_by_channel_and_status('free_matching', 'matched')
      
      stats = {
        total: total_users,
        matching: matching_users.size,
        matched: matched_users.size
      }
      
      transmit({
        matching_users: matching_users,
        matched_users: matched_users,
        stats: stats,
        timestamp: Time.now.to_i
      })
    end
    
    def transmit_room_match_data
      total_users = UserStatusService.count_by_channel_type('room_matching')
      room_count = UserStatusService.get_room_count
      rooms = UserStatusService.get_rooms_data
      
      stats = {
        total: total_users,
        rooms: room_count
      }
      
      transmit({
        rooms: rooms,
        stats: stats,
        timestamp: Time.now.to_i
      })
    end
  end
end