class RoomMatchingChannel < ApplicationCable::Channel
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end

    # ロックの状態をチェックするヘルパーメソッド
    def check_lock_exists(key)
      begin
        # Redisに直接問い合わせてロック情報を取得
        lock_key = "redlock:#{key}"
        value = redis.get(lock_key)
        if value
          DEBUG("RoomMatchingChannel", "Lock exists for #{key}: #{value}")
          true
        else
          false
        end
      rescue => e
        ERROR("RoomMatchingChannel", "Error checking lock: #{e.message}")
        false
      end
    end
  end

  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  # Redisヘルパーメソッド
  def redis_get(key)
    self.class.redis.get(key)
  rescue Redis::BaseError => e
    self.class.ERROR("RoomMatchingChannel", "Redis error when getting #{key}: #{e.message}")
    nil
  end

  def redis_set(key, value, expire = nil)
    self.class.redis.set(key, value)
    if expire
      self.class.redis.expire(key, expire)
    end
    true
  rescue Redis::BaseError => e
    self.class.ERROR("RoomMatchingChannel", "Redis error when setting #{key}: #{e.message}")
    false
  end

  def redis_del(key)
    self.class.redis.del(key)
    true
  rescue Redis::BaseError => e
    self.class.ERROR("RoomMatchingChannel", "Redis error when deleting #{key}: #{e.message}")
    false
  end

  # より安全なwith_lockヘルパー - TTLを短くして接続解放を確実に行う
  def with_lock(lock_key, ttl = nil)
    settings = self.class.get_env_settings
    start_time = Time.now
    success = false
    retry_count = 0
    max_retries = settings[:retry_count]

    # ロックが存在しているか確認（デバッグ用）
    self.class.check_lock_exists(lock_key)

    # TTLを適切に設定（指定がない場合はデフォルト値を使用）
    ttl = ttl || settings[:default_ttl]
    # 短いTTLに強制的に調整（長すぎるTTLを防止）
    ttl = [ ttl, settings[:max_lock_ttl] ].min

    # 処理全体のタイムアウト（TTLの3倍を目安）
    timeout = ttl * 3
    timeout_time = start_time + (timeout / 1000.0)

    while !success && retry_count < max_retries && Time.now < timeout_time
      begin
        # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
        remaining_time = (timeout_time - Time.now) * 1000
        if remaining_time < ttl
          adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
          self.class.WARN("RoomMatchingChannel", "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
          ttl = adjusted_ttl
        end

        self.class.redlock.lock(lock_key, ttl) do |locked|
          if locked
            begin
              self.class.DEBUG("RoomMatchingChannel", "Lock acquired for #{lock_key}")
              success = true
              yield
            rescue => e
              self.class.ERROR("RoomMatchingChannel", "Error in lock block for #{lock_key}: #{e.message}")
              self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
              success = false
            ensure
              # ロック処理が長すぎる場合は警告
              duration = ((Time.now - start_time) * 1000).to_i
              if duration > ttl / 2
                self.class.WARN("RoomMatchingChannel", "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
              end
            end
          else
            retry_count += 1
            # 環境に応じたリトライ間隔を使用
            sleep_time = settings[:retry_interval]
            self.class.WARN("RoomMatchingChannel", "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
            sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
          end
        end
      rescue => e
        retry_count += 1
        self.class.ERROR("RoomMatchingChannel", "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
        sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
      end
    end

    # タイムアウトした場合の処理
    if Time.now >= timeout_time && !success
      self.class.ERROR("RoomMatchingChannel", "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
    end

    if !success && retry_count >= max_retries
      self.class.ERROR("RoomMatchingChannel", "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
    end

    success
  end

  def subscribed
  end

  def unsubscribed
    player_id = params[:player_id]
    begin
      self.class.DEBUG("RoomMatchingChannel", "unsubscribed player #{params} #{player_id}")

      # 先にroom_idを取得してから削除
      player_key = "room_matching_channel:player#{player_id}"
      room_id = redis_get(player_key)
      redis_del(player_key)

      if room_id.nil?
        self.class.ERROR("RoomMatchingChannel", "No room found for player #{player_id}")
        self.kick_player("CR0000", "No room found for player #{player_id}")
        return
      end

      # 安全に部屋情報を更新（エラーが発生しても処理を継続）
      begin
        # player_countとルーム情報の更新を一つのロックで効率化
        room_key = "room_matching_channel:#{room_id}"
        lock_success = with_lock("room_matching_channel:#{room_id}:unsubscribe", 1500) do
          # プレイヤーカウント更新
          begin
            player_count_key = "room_matching_channel_player_count:#{room_id}"
            player_count_json = redis_get(player_count_key)

            if player_count_json
              begin
                player_count = JSON.parse(player_count_json)
                player_count["player_count"] = player_count["player_count"].to_i - 1
                redis_set(player_count_key, player_count.to_json)
              rescue JSON::ParserError => e
                self.class.ERROR("RoomMatchingChannel", "JSON parse error for player count: #{e.message}")
              end
            end
          rescue => e
            self.class.ERROR("RoomMatchingChannel", "Error updating player count: #{e.message}")
          end

          # ルーム情報更新
          begin
            room_json = redis_get(room_key)
            if room_json
              begin
                room = JSON.parse(room_json, symbolize_names: true)
                self.class.DEBUG("RoomMatchingChannel", "Show Room #{room}")
                updated = false

                if room[:player_0_pack] && room[:player_0_pack][:player_id] == player_id
                  room[:player_0_pack] = nil
                  updated = true
                elsif room[:player_1_pack] && room[:player_1_pack][:player_id] == player_id
                  room[:player_1_pack] = nil
                  updated = true
                end

                if updated
                  redis_set(room_key, room.to_json)
                end
              rescue JSON::ParserError => e
                self.class.ERROR("RoomMatchingChannel", "JSON parse error for room data: #{e.message}")
              end
            end
          rescue => e
            self.class.ERROR("RoomMatchingChannel", "Error updating room: #{e.message}")
          end

          # ロックの取得に成功してから更新が終わるまでtrueを返す
          updated = true
          self.class.DEBUG("RoomMatchingChannel", "All updates completed successfully in lock")
          true
        end

        # ロックの取得に成功した場合だけブロードキャストを実行
        if lock_success
          begin
            broadcast_room_update(room_id)
            broadcast_room_update_player_count(room_id)
          rescue => e
            self.class.ERROR("RoomMatchingChannel", "Error broadcasting updates: #{e.message}")
          end
        else
          # ロック取得に失敗しても、クライアントが切断されるだけなので、エラーログだけ残して続行
          self.class.WARN("RoomMatchingChannel", "Failed to update room/player count on unsubscribe, but continuing")
        end
      rescue => e
        # 何かあっても処理を継続させる
        self.class.ERROR("RoomMatchingChannel", "Error in room update process: #{e.message}")
        self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
      end
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Unexpected error in unsubscribed: #{e.message}")
      self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
    ensure
      # 何があってもストリーミングは停止する
      begin
        if room_id
          self.class.DEBUG("RoomMatchingChannel", "Ensuring stream is stopped for room_id: #{room_id}")
        end
      rescue => e
        self.class.ERROR("RoomMatchingChannel", "Error in final cleanup: #{e.message}")
      end
    end
  end

  def broadcast_room_update(room_id)
    room_json = redis_get("room_matching_channel:#{room_id}")

    if room_json
      room = JSON.parse(room_json)
      ActionCable.server.broadcast("room_matching_channel:#{room_id}", {
        command: "RoomDataUpdate",
        body:    room,
      })
    else
      self.class.ERROR("RoomMatchingChannel", "Failed to broadcast room update - room not found: #{room_id}")
    end
  rescue => e
    self.class.ERROR("RoomMatchingChannel", "Error in broadcast_room_update: #{e.message}")
  end

  def transmit_room_update(room_id)
    room_json = redis_get("room_matching_channel:#{room_id}")

    if room_json
      room = JSON.parse(room_json)
      transmit({ command: "RoomDataUpdate", body: room })
    else
      self.class.ERROR("RoomMatchingChannel", "Failed to transmit room update - room not found: #{room_id}")
    end
  rescue => e
    self.class.ERROR("RoomMatchingChannel", "Error in transmit_room_update: #{e.message}")
  end


  def broadcast_room_update_player_count(room_id)
    player_count_json = redis_get("room_matching_channel_player_count:#{room_id}")

    if player_count_json
      player_count = JSON.parse(player_count_json)
      ActionCable.server.broadcast("room_matching_channel:#{room_id}", {
        command: "RoomPlayerCountUpdate",
        body:    player_count,
      })
    else
      self.class.ERROR("RoomMatchingChannel", "Failed to broadcast player count - data not found: #{room_id}")
    end
  rescue => e
    self.class.ERROR("RoomMatchingChannel", "Error in broadcast_room_update_player_count: #{e.message}")
  end

  def generate_room_id
    charset = ("0".."9").to_a
    Array.new(6) { charset.sample }.join
  end

  def create_room(data)
    begin
      self.class.DEBUG("RoomMatchingChannel", "create_room #{data}")
      room_id = nil

      # ロックの取得試行時間を短縮（2000ms→1000ms）
      with_lock("room_matching_channel:create_room", 1000) do
        # 重複しないIDが出るまで繰り返す
        loop_count = 0
        loop do
          loop_count += 1
          if loop_count > 10
            self.class.WARN("RoomMatchingChannel", "Room ID generation retry limit reached")
            break
          end

          candidate_id = generate_room_id
          exists = false

          exists = self.class.redis.exists?("room_matching_channel:#{candidate_id}")

          unless exists
            room_id = candidate_id
            break
          end
        end

        # ループから抜けた後、room_idがnilの場合はエラー
        if room_id.nil?
          self.class.ERROR("RoomMatchingChannel", "Failed to generate unique room ID")
          self.kick_player("CR0000", "Failed to generate unique room ID") # 生成に失敗した場合はプレイヤーを強制的に切断
          return
        end

        pack = {
          "room_id"   => room_id,
          "player_id" => data["text"]["player_id"],
          "name"      => data["text"]["name"],
          "level"     => data["text"]["level"],
          "icon_id"   => data["text"]["icon_id"],
          "frame_id"  => data["text"]["frame_id"],
          "title_id"  => data["text"]["title_id"],
        }

        room_data = {
          "room_id"        => room_id,
          "owner_id"       => data["text"]["player_id"],
          "player_0_pack"  => pack,
          "player_1_pack"  => nil,
          "player_0_ready" => false,
          "player_1_ready" => false,
        }.to_json

        player_count_data = { "player_count" => 1 }.to_json

        # Redisへの書き込み
        self.class.redis.set("room_matching_channel:#{room_id}", room_data)
        self.class.redis.expire("room_matching_channel:#{room_id}", 36000) # 10時間後に削除（秒単位）

        self.class.redis.set("room_matching_channel_player_count:#{room_id}", player_count_data)
        self.class.redis.expire("room_matching_channel_player_count:#{room_id}", 36000) # 10時間後に削除（秒単位）

        # player_idとroom_idのマッピングを設定
        self.class.redis.set("room_matching_channel:player#{data["text"]["player_id"]}", room_id.to_s)
        self.class.redis.expire("room_matching_channel:player#{data["text"]["player_id"]}", 36000)

        self.class.DEBUG("RoomMatchingChannel", "Room created: #{room_id}")
      end

      if room_id.nil?
        self.class.ERROR("RoomMatchingChannel", "Failed to create room: room_id is nil")
        self.kick_player("CR0001", "Failed to create room: room_id is nil") # 作成に失敗した場合はプレイヤーを強制的に切断
        return
      end

      # ユーザーステータスの更新
      UserStatusService.update_status(data["text"]["player_id"], "in_room", {
        room_id:      room_id,
        joined_at:    Time.now,
        ready:        false,
        channel_type: "room_matching",
      })

      stream_from("room_matching_channel:#{room_id}")
      transmit({ command: "MoveToRoom" })

      broadcast_room_update(room_id)
      broadcast_room_update_player_count(room_id)
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Failed to create room: #{e.message}")
      self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
      self.kick_player("CR0002", "Failed to create room") # 作成に失敗した場合はプレイヤーを強制的に切断
    end
  end

  def join_room(data)
    begin
      join_start_time = Time.now
      self.class.DEBUG("RoomMatchingChannel", "join_room #{data}")
      unless data["text"] && data["text"]["room_id"]
        transmit({ command: "InvalidData", error: "Invalid room data" })
        return
      end

      room_id = data["text"]["room_id"]
      key = "room_matching_channel:#{room_id}"
      room_json = nil

      # Redisからルーム情報を取得
      begin
        self.class.DEBUG("RoomMatchingChannel", "Getting room  from Redis: #{key}")
        room_json = self.class.redis.get(key)

        unless room_json
          self.class.ERROR("RoomMatchingChannel", "Room not found in Redis: #{key}")
          self.kick_player("JR0001", "Room not found in Redis: #{key}") # 取得に失敗した場合はプレイヤーを強制的に切断
          return
        end

        room = JSON.parse(room_json)
        self.class.DEBUG("RoomMatchingChannel", "Room  retrieved successfully")
      rescue Redis::BaseError => e
        self.class.ERROR("RoomMatchingChannel", "Redis error when getting room: #{e.message}")
        self.kick_player("JR0001", "Redis error when getting room: #{e.message}") # 取得に失敗した場合はプレイヤーを強制的に切断
        return
      rescue JSON::ParserError => e
        self.class.ERROR("RoomMatchingChannel", "JSON parse error for room: #{e.message}")
        self.kick_player("JR0002", "JSON parse error for room: #{e.message}") # パースに失敗した場合はプレイヤーを強制的に切断
        return
      end

      # プレイヤー情報パックの作成
      begin
        pack = {
          "room_id"   => room_id,
          "player_id" => data["text"]["player_id"],
          "name"      => data["text"]["name"],
          "level"     => data["text"]["level"],
          "icon_id"   => data["text"]["icon_id"],
          "frame_id"  => data["text"]["frame_id"],
          "title_id"  => data["text"]["title_id"],
        }
        self.class.DEBUG("RoomMatchingChannel", "Player pack created")
      rescue => e
        self.class.ERROR("RoomMatchingChannel", "Error creating player pack: #{e.message}")
        self.kick_player("JR0003", "Error creating player pack: #{e.message}") # パースに失敗した場合はプレイヤーを強制的に切断
        return
      end

      self.class.DEBUG("RoomMatchingChannel", "join_room V1")

      # 空いている席が存在するかを先に確認
      position = nil
      if room["player_0_pack"].nil?
        self.class.DEBUG("RoomMatchingChannel", "join_room0 #{data} #{room}")
        position = 0
      elsif room["player_1_pack"].nil?
        self.class.DEBUG("RoomMatchingChannel", "join_room1 #{data} #{room}")
        position = 1
      else
        self.class.DEBUG("RoomMatchingChannel", "join_room Spectator #{data} #{room}")
      end

      # 席が空いている場合のみロックを取得して処理
      updated = false
      if position != nil
        self.class.DEBUG("RoomMatchingChannel", "Trying to acquire lock for room #{room_id}")
        # 一つの大きなロックで全ての処理を行う
        begin
          Timeout.timeout(5) do # 5秒のタイムアウトを設定
            lock_success = with_lock("room_matching_channel:#{room_id}:join", 2000) do
              # ロック内の処理結果
              lock_result = false

              self.class.DEBUG("RoomMatchingChannel", "Processing in lock")
              # 最新のルーム情報を再度取得（ロック取得中に変更されている可能性がある）
              fresh_room_json = redis_get(key)

              if fresh_room_json.nil?
                self.class.ERROR("RoomMatchingChannel", "Room no longer exists")
                self.kick_player("JR0004", "Room no longer exists") # ルームが存在しない場合はプレイヤーを強制的に切断
              else
                begin
                  fresh_room = JSON.parse(fresh_room_json)
                  self.class.DEBUG("RoomMatchingChannel", "Fresh room data parsed")

                  # 再チェック - 他のプレイヤーが既に座っていないか
                  if fresh_room["player_#{position}_pack"].nil?
                    fresh_room["player_#{position}_pack"] = pack
                    self.class.DEBUG("RoomMatchingChannel", "Player added to position #{position}")

                    # ルーム情報を更新
                    redis_result = redis_set(key, fresh_room.to_json)
                    self.class.DEBUG("RoomMatchingChannel", "Room data update result: #{redis_result}")

                    if redis_result
                      updated = true
                      lock_result = true

                      # ロックの外でステータス更新用にマークする
                      @status_update_needed = {
                        player_id: data["text"]["player_id"],
                        room_id: room_id
                      }

                      self.class.DEBUG("RoomMatchingChannel", "All updates completed successfully in lock")
                    else
                      self.class.ERROR("RoomMatchingChannel", "Failed to update room data")
                      self.kick_player("JR0006", "Failed to update room data") # 更新に失敗した場合はプレイヤーを強制的に切断
                    end
                  else
                    self.class.DEBUG("RoomMatchingChannel", "Position #{position} was taken during lock acquisition")
                  end
                rescue JSON::ParserError => e
                  self.class.ERROR("RoomMatchingChannel", "JSON parse error after lock: #{e.message}")
                  self.kick_player("JR0007", "JSON parse error after lock: #{e.message}") # パースに失敗した場合はプレイヤーを強制的に切断
                rescue => e
                  self.class.ERROR("RoomMatchingChannel", "Unexpected error in lock block: #{e.message}")
                  self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
                  self.kick_player("JR0008", "Unexpected error in lock block: #{e.message}") # 予期せぬエラーが発生した場合はプレイヤーを強制的に切断
                end
              end

              # ロックブロックの結果を返す
              lock_result
            end

            # ロックの結果を確認
            self.class.DEBUG("RoomMatchingChannel", "Lock processed, result: #{lock_success}")
          end
        rescue Timeout::Error => e
          self.class.ERROR("RoomMatchingChannel", "Timeout occurred during join_room lock processing: #{e.message}")
          self.kick_player("JR0009", "Timeout occurred during join_room lock processing: #{e.message}") # タイムアウト時でも処理を継続
        rescue => e
          self.class.ERROR("RoomMatchingChannel", "Unexpected error during lock processing: #{e.message}")
          self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
          self.kick_player("JR0010", "Unexpected error during lock processing: #{e.message}") # 予期せぬエラーが発生した場合はプレイヤーを強制的に切断
        end

        # ロック取得に失敗した場合
        if !updated
          self.class.WARN("RoomMatchingChannel", "Failed to update room data during join, proceeding as spectator")
        end
      end

      # プレイヤーカウントを更新（全ての参加者に対して実行）
      begin
        # ロックを使ってプレイヤーカウントを更新
        with_lock("room_matching_channel:#{room_id}:player_count", 1000) do
          player_count_key = "room_matching_channel_player_count:#{room_id}"
          player_count_json = redis_get(player_count_key)
          self.class.DEBUG("RoomMatchingChannel", "Retrieved player count data")

          if player_count_json.nil?
            self.class.DEBUG("RoomMatchingChannel", "Player count not found, creating new")
          else
            begin
              self.class.DEBUG("RoomMatchingChannel", "Updating existing player count")
              player_count = JSON.parse(player_count_json)
              player_count["player_count"] = player_count["player_count"].to_i + 1
              redis_set(player_count_key, player_count.to_json)
            rescue JSON::ParserError => e
              self.class.ERROR("RoomMatchingChannel", "JSON parse error for player count: #{e.message}")
            end
          end
        end

        # プレイヤーとルームの紐付け
        player_id = params[:player_id]
        player_key = "room_matching_channel:player#{player_id}"
        self.class.DEBUG("RoomMatchingChannel", "Linking player #{player_id} to room #{room_id}")
        redis_set(player_key, room_id.to_s, 36000)
      rescue => e
        self.class.ERROR("RoomMatchingChannel", "Error updating player count: #{e.message}")
        self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
      end

      # ユーザーステータスの更新
      if @status_update_needed
        UserStatusService.update_status(@status_update_needed[:player_id], "in_room", {
          room_id:      @status_update_needed[:room_id],
          joined_at:    Time.now,
          ready:        false,
          channel_type: "room_matching",
        })
      end

      # 接続とブロードキャスト（これらは必ず実行する）
      self.class.DEBUG("RoomMatchingChannel", "Setting up streaming for room #{room_id}")
      stream_from("room_matching_channel:#{room_id}")

      self.class.DEBUG("RoomMatchingChannel", "Sending MoveToRoom command")
      transmit({ command: "MoveToRoom" })

      # 更新があった場合はブロードキャスト、なければ自分だけに送信
      self.class.DEBUG("RoomMatchingChannel", "Sending updates, updated=#{updated}")
      if updated
        broadcast_room_update(room_id)
      else
        transmit_room_update(room_id)
      end

      broadcast_room_update_player_count(room_id)
      self.class.DEBUG("RoomMatchingChannel", "Join room process completed")

      # 処理時間が長すぎる場合は警告
      join_duration = (Time.now - join_start_time) * 1000
      if join_duration > 500
        self.class.WARN("RoomMatchingChannel", "join_room operation took #{join_duration.to_i}ms, which is longer than expected")
      end
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Failed to join room: #{e.message}")
      self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
      self.kick_player("JR0011", "Failed to join room: #{e.message}") # 参加に失敗した場合はプレイヤーを強制的に切断
    end
  end

  def ready0(data)
    self.class.DEBUG("RoomMatchingChannel", "Ready0 #{data}")
    ready(num: 0, room_id: data["text"]["room_id"])
  end

  def ready1(data)
    self.class.DEBUG("RoomMatchingChannel", "ready1 #{data}")
    ready(num: 1, room_id: data["text"]["room_id"])
  end

  def ready(num:, room_id:)
    begin
      self.class.DEBUG("RoomMatchingChannel", "ready OK  #{num} #{room_id}")
      room_key = "room_matching_channel:#{room_id}"
      room_json = redis_get(room_key)

      if room_json.nil?
        self.class.ERROR("RoomMatchingChannel", "Room not found for ready: #{room_id}")
        return
      end

      room = JSON.parse(room_json)

      # ロックの外で使用するステータス更新用の変数
      status_player_id = nil
      new_ready_state = nil

      with_lock("#{room_key}:ready", 2000) do
        # 現在の状態を取得し、反転させる
        current_state = room["player_#{num}_ready"]
        room["player_#{num}_ready"] = !current_state
        new_ready_state = !current_state

        redis_set(room_key, room.to_json)

        # ステータス更新のために使用する player_id をロックの外で保存
        status_player_id = room["player_#{num}_pack"]["player_id"] if room["player_#{num}_pack"]
      end

      # ユーザーステータスの更新
      if status_player_id
        UserStatusService.update_status(status_player_id, "in_room", {
          room_id:      room_id,
          ready:        new_ready_state,
          channel_type: "room_matching",
        })
      end

      self.class.DEBUG("RoomMatchingChannel", "ready OK  #{num} #{room_id}")
      broadcast_room_update(room_id)
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Error in ready: #{e.message}")
    end
  end

  def update_deck(data)
    begin
      room_id = data["text"]["room_id"]
      player_id = data["text"]["player_id"]
      deck_data = data["text"]["deck_data"]

      # シンボルキーを含むデータを受け取った場合の例
      # deck_data = {cards: [{id: 1, name: "カード1"}, {id: 2, name: "カード2"}], deck_name: "デッキ名"}

      self.class.DEBUG("RoomMatchingChannel", "Original deck_data: #{deck_data.inspect}")

      key = "room_matching_channel:#{room_id}"

      with_lock("room_matching_channel:#{room_id}:update_deck", 2000) do
        room_json = self.class.redis.get(key)

        unless room_json
          transmit({ command: "RoomNotFound", error: "Room not found" })
          return
        end

        room = JSON.parse(room_json)

        # プレイヤーIDに基づいてデッキ情報を更新
        if room["player_0_pack"] && room["player_0_pack"]["player_id"] == player_id
          room["deck_0"] = deck_data
        elsif room["player_1_pack"] && room["player_1_pack"]["player_id"] == player_id
          room["deck_1"] = deck_data
        else
          transmit({ command: "PlayerNotFound", error: "Player not found in room" })
          return
        end

        # 更新したデータをRedisに保存
        self.class.redis.set(key, room.to_json)

        self.class.DEBUG("RoomMatchingChannel", "Deck updated for player: #{player_id}")
      end

      # ルーム情報を更新してブロードキャスト
      broadcast_room_update(room_id)
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Error in update_deck: #{e.message}")
      self.class.ERROR("RoomMatchingChannel", e.backtrace.join("\n"))
    end
  end

  def reset_ready(data)
    begin
      room_id = data["text"]["room_id"]

      with_lock("room_matching_channel:#{room_id}:reset_ready", 2000) do
        room_json = self.class.redis.get("room_matching_channel:#{room_id}")

        if room_json.nil?
          self.class.ERROR("RoomMatchingChannel", "Room not found for reset_ready: #{room_id}")
          return
        end

        room = JSON.parse(room_json)
        room["player_0_ready"] = false
        room["player_1_ready"] = false

        self.class.redis.set("room_matching_channel:#{room_id}", room.to_json)
      end

    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Error in reset_ready: #{e.message}")
    end
  end

  def switch_position(data)
    begin
      self.class.DEBUG("RoomMatchingChannel", "switch_position #{data}")
      room_id = data["text"]["room_id"]
      player_id = data["text"]["player_id"]

      pack = {
        "room_id"   => room_id,
        "player_id" => data["text"]["player_id"],
        "name"      => data["text"]["name"],
        "level"     => data["text"]["level"],
        "icon_id"   => data["text"]["icon_id"],
        "frame_id"  => data["text"]["frame_id"],
        "title_id"  => data["text"]["title_id"],
      }

      room_json = self.class.redis.get("room_matching_channel:#{room_id}")

      if room_json.nil?
        self.class.ERROR("RoomMatchingChannel", "Room not found for switch_position: #{room_id}")
        return
      end

      room = JSON.parse(room_json, symbolize_names: true)
      room_updated = false

      with_lock("room_matching_channel:#{room_id}:switch_position", 2000) do
        # 部屋の現在の状態を保存（変更検出用）
        previous_state = room.deep_dup

        # プレイヤーポジションをチェックする前に、nilチェックを追加
        if room[:player_0_pack] && room[:player_0_pack][:player_id] == player_id
          room[:player_0_pack] = nil
          room[:player_0_ready] = false
        elsif room[:player_1_pack] && room[:player_1_pack][:player_id] == player_id
          room[:player_1_pack] = nil
          room[:player_1_ready] = false
        else
          # 空席にプレイヤーを配置
          if room[:player_0_pack].nil?
            room[:player_0_pack] = pack
          elsif room[:player_1_pack].nil?
            room[:player_1_pack] = pack
          end
        end

        # 変更があったか確認
        room_updated = (previous_state != room)

        if room_updated
          self.class.DEBUG("RoomMatchingChannel", "Room state changed, updating in Redis")
          self.class.redis.set("room_matching_channel:#{room_id}", room.to_json)
        else
          self.class.DEBUG("RoomMatchingChannel", "No change in room state, skipping update")
        end
      end

      # 変更があった場合のみブロードキャスト
      if room_updated
        self.class.DEBUG("RoomMatchingChannel", "Broadcasting room update")
        broadcast_room_update(room_id)
      end
    rescue => e
      self.class.ERROR("RoomMatchingChannel", "Error in switch_position: #{e.message}")
    end
  end
end
