module GiftsHelper

  # def gift_check
  #   gift_master_check()
  #   page = 0
  #   count = 50
  #   data = Gift.where(user_id:current_user.id).where("limited_at > ?",DateTime.now).where.not(state: 1).offset(page*count).limit(count).reverse
  #   return data.to_ary
  # end

  # def gift_master_check
  #     gifts = Gift.where(user_id: current_user.id)
    
  #     master = GiftMaster
  #               .where("limited_at > ?" ,Time.now)
  #               .where("start_at < ?",Time.now)
  #               .where.not(id: gifts.select(:master_id))
  #     master.each do |data|
  #       gift = Gift.new(user_id: current_user.id,item_kind: data.item_kind,item_count: data.item_count,desc: data.desc,state: 0,limited_at: data.limited_at,master_id: data.id,master_limit: data.limited_at) 
  #       gift.save!
  #     end
  # end

  # def create_gift(user_id,item_kind,item_count,desc,limited_at)
  #   gift = Gift.new
  #   gift.user_id = user_id
  #   gift.item_kind = item_kind
  #   gift.item_count = item_count
  #   gift.desc = desc
  #   gift.limited_at = limited_at
  #   gift.state = 0
  #   gift.valid = 0
  #   gift.master_id = -1
  #   gift.master_limit = Time.now.since(14.days)
  #   gift.save!
  # end
end