module <PERSON>Helper
  def display_game_mode(mode)
    case mode
    when 'free'
      'フリーマッチ(free)'
    when 'rank'
      'ランク(rank)'
    when 'room'
      'ルーム(room)'
    when 'event'
      'イベント(event)'
    else
      mode
    end
  end
  
  # プレイヤー詳細リンクを生成するヘルパーメソッド
  def get_player_link(player_id)
    return "不明" if player_id.blank?
    
    begin
      user = get_player_info(player_id)
      if user
        link_to "#{user.name} (#{player_id})", user_page_show_path(player_id), class: "text-primary text-decoration-none"
      else
        content_tag :span, player_id, class: "text-muted"
      end
    rescue => e
      Rails.logger.error("Error creating player link for #{player_id}: #{e.message}")
      content_tag :span, player_id, class: "text-muted"
    end
  end
end
