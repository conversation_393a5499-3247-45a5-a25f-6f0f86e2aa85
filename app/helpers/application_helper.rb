module ApplicationHelper
  
  # 一行表示用の言語ヘルパー
  def json_lang(hash)
    sorted_langs = Lang.order_by_position
    hash.sort_by { |lang, _| sorted_langs.index(lang) || Float::INFINITY }
        .map do |lang, text|
      next if text.blank?
      tag.span class: 'd-inline-block me-3' do
        tag.span(lang.upcase, class: 'badge bg-primary me-1') + " " + text
      end
    end.compact.join(" ").html_safe
  end

  # 複数行表示用の言語ヘルパー
  def json_lang_multi(hash)
    sorted_langs = Lang.order_by_position
    hash.sort_by { |lang, _| sorted_langs.index(lang) || Float::INFINITY }
        .map do |lang, text|
      next if text.blank?
      tag.div class: 'mb-3' do
        tag.span(lang.upcase, class: 'badge bg-secondary') +
        tag.p(text, class: 'text-muted mt-1')
      end
    end.compact.join.html_safe
  end

  # 多言語JSONフィールド用のフォームヘルパー
  # @param form [FormBuilder] フォームビルダーオブジェクト
  # @param field [Symbol] JSONカラム名
  # @param options [Hash] オプション（:label, :as, :rows）
  def json_lang_form(form, field, options = {})
    # フォームグループのコンテナ
    content_tag :div, class: "mb-4" do
      concat(content_tag(:label, options[:label], class: "form-label"))
      # 言語入力フィールドのグリッドレイアウト
      concat(content_tag(:div, class: "row g-3") do
        Lang.all.each do |lang|
          # メイン言語かどうかを判定
          is_main_lang = (lang.locale == Lang.main_lang.locale)
          
          # 各言語用の4カラム幅のコンテナ
          concat(content_tag(:div, class: "col-md-4") do
            content_tag(:div, class: "input-group") do
              # 言語コードのプレフィックス
              concat(content_tag(:span, lang.locale, class: "input-group-text"))
              # テキストエリアまたはテキストフィールドを生成
              concat(
                if options[:as] == :text_area
                  form.text_area "[#{field}][#{lang.locale}]",
                               value: form.object.send(field)[lang.locale],
                               class: "form-control",
                               rows: options[:rows] || 3,
                               placeholder: "#{lang.name}の#{options[:label]}",
                               name: "#{form.object_name}[#{field}][#{lang.locale}]"
                elsif options[:as] == :image
                  form.file_field "[#{field}][#{lang.locale}]",
                               class: "form-control",
                               placeholder: "#{lang.name}の#{options[:label]}",
                               name: "#{form.object_name}[#{field}][#{lang.locale}]"
                else
                  form.text_field "[#{field}][#{lang.locale}]",
                                value: form.object.send(field)[lang.locale],
                                class: "form-control",
                                placeholder: "#{lang.name}の#{options[:label]}",
                                name: "#{form.object_name}[#{field}][#{lang.locale}]"
                end
              )
              # チェックボックスを生成（メイン言語の場合は無効化）
              concat(content_tag(:div, class: "form-check") do
                concat(form.check_box "[#{field}][#{lang.locale}][gpt]", 
                                   class: "form-check-input", 
                                   id: "#{form.object_name}_#{field}_#{lang.locale}_gpt",
                                   name: "#{form.object_name}[gpt][#{field}][#{lang.locale}]",
                                   checked: !is_main_lang && (form.object.send(field).blank? || form.object.send(field)[lang.locale.to_s].blank?),
                                   disabled: is_main_lang)
                concat(content_tag(:label, "GPT", class: "form-check-label", for: "#{form.object_name}_#{field}_#{lang.locale}_gpt"))
              end)
            end
          end)
        end
      end)
    end
  end

  def bool_icon(bool)
    bool ? "<i class='fas fa-check'></i>".html_safe : "<i class='fas fa-times'></i>".html_safe
  end
end
