module PacksHelper
     # Helper method to convert rarity number to key
  def rarity_to_key(rarity)
    case rarity
    when 1 then "bronze1"
    when 2 then "silver2"
    when 3 then "gold3"
    when 4 then "legend4"
    else "bronze1"
    end
  end

  # Helper method to convert edition number to key
  def edition_to_key(edition)
    case edition
    when 1 then "normal"
    when 2 then "shine"
    when 3 then "premium"
    else "normal"
    end
  end
  
  # Convert rarity_key to rarity number
  def key_to_rarity(key)
    case key
    when "bronze1" then 1
    when "silver2" then 2
    when "gold3" then 3
    when "legend4" then 4
    else 1
    end
  end
  
  # Convert edition_key to edition number
  def key_to_edition(key)
    case key
    when "normal" then 1
    when "shine" then 2
    when "premium" then 3
    else 1
    end
  end
end
