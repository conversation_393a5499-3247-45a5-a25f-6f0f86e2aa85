module User<PERSON>age<PERSON>elper
    def show_log_data(log)
        if log.data.present?
            format_log_data2(log)
        else
            {}
        end
    end

    private

    def format_log_data(data)
        data.map do |key, value|
            if value.is_a?(Hash)
                "#{UserLog::KEY_TO_NAME[key.to_sym]}: #{format_log_data(value)}"
            else
                "#{UserLog::KEY_TO_NAME[key.to_sym]}: #{value}"
            end
        end.join("<br />").html_safe
    end

    def format_log_data2(log)
        str = ""
        data = log.data

        data.map do |key, value|
            if UserLog::KEY_TO_NAME[key.to_sym].present?
                str += "#{UserLog::KEY_TO_NAME[key.to_sym]}: #{value}<br />"
            end
        end

        case log.action
        when "normal_purchase"
            str += "アイテムの種類: #{data["item_id"]}<br />ダイヤ変動数: #{data["amount"]}円"
        when "open_pack"
            # ここに処理を追加することができます
        end

        str.html_safe
    end
end
