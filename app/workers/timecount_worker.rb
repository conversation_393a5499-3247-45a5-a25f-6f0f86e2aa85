# app/workers/timecount_worker.rb
class TimecountWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    5, # 本番環境では多めにリトライ
          retry_interval: 0.2, # リトライ間隔を長めに設定
          lock_ttl:       3000, # ロックのTTLを長めに設定
          max_lock_ttl:   5000, # 最大TTL
          default_ttl:    1500, # デフォルトTTL
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end
  end

  # Redis接続初期化
  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  def perform
    matching_keys = []
    free_matching_keys = []

    # REDISコネクションプールを使用
    begin
      self.class.redis.with do |conn|
        matching_keys = conn.keys("matching_data:*")
        free_matching_keys = conn.keys("free_matching_data:*")
      end
    rescue Redis::BaseError => e
      Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Redis error: #{e.message}")
      return
    end

    keys = matching_keys + free_matching_keys
    return if keys.empty?

    # 各プレイヤーのtimecountを更新
    processed_keys = 0
    keys.each do |key|
      process_key(key) && processed_keys += 1
    end

    if processed_keys > 0
      Rails.logger.info(level: "info", worker: "TimecountWorker", message: "Updated timecount for #{processed_keys} players")
    end
  rescue => e
    Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Unexpected error: #{e.message}")
    Rails.logger.error(level: "error", worker: "TimecountWorker", message: e.backtrace.join("\n"))
  end

  private

    # 改善されたロック取得メソッド
    def process_key(key)
      settings = self.class.get_env_settings
      start_time = Time.now
      success = false
      retry_count = 0
      max_retries = settings[:retry_count]
      lock_key = "lock:#{key}"
      ttl = settings[:default_ttl]

      # 処理全体のタイムアウト（TTLの3倍を目安）
      timeout = ttl * 3
      timeout_time = start_time + (timeout / 1000.0)

      while !success && retry_count < max_retries && Time.now < timeout_time
        begin
          # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
          remaining_time = (timeout_time - Time.now) * 1000
          if remaining_time < ttl
            adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
            Rails.logger.warn(level: "warn", worker: "TimecountWorker", message: "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
            ttl = adjusted_ttl
          end

          self.class.redlock.lock(lock_key, ttl) do |locked|
            if locked
              begin
                Rails.logger.debug(level: "debug", worker: "TimecountWorker", message: "Lock acquired for #{lock_key}")
                data_json = self.class.redis.get(key)

                if data_json.nil?
                  Rails.logger.warn(level: "warn", worker: "TimecountWorker", message: "Key #{key} no longer exists")
                  return false
                end

                begin
                  data = JSON.parse(data_json, symbolize_names: true)
                  data[:timecount] += 5

                  self.class.redis.set(key, data.to_json)
                  success = true
                rescue JSON::ParserError => e
                  Rails.logger.error(level: "error", worker: "TimecountWorker", message: "JSON parse error for #{key}: #{e.message}")
                  # 壊れたデータは削除する
                  self.class.redis.del(key)
                  return false
                end
              rescue => e
                Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Error in lock block for #{key}: #{e.message}")
                Rails.logger.error(level: "error", worker: "TimecountWorker", message: e.backtrace.join("\n"))
                success = false
              ensure
                # ロック処理が長すぎる場合は警告
                duration = ((Time.now - start_time) * 1000).to_i
                if duration > ttl / 2
                  Rails.logger.warn(level: "warn", worker: "TimecountWorker", message: "Lock operation took #{duration}ms for #{key}, which is longer than #{ttl/2}ms (half of TTL)")
                end
              end
            else
              retry_count += 1
              # 環境に応じたリトライ間隔を使用
              sleep_time = settings[:retry_interval]
              Rails.logger.warn(level: "warn", worker: "TimecountWorker", message: "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{key}, retrying in #{sleep_time}s")
              sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
            end
          end
        rescue => e
          retry_count += 1
          Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Lock error for #{key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
          sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
        end
      end

      # タイムアウトした場合の処理
      if Time.now >= timeout_time && !success
        Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{key}")
      end

      if !success && retry_count >= max_retries
        Rails.logger.error(level: "error", worker: "TimecountWorker", message: "Failed to acquire lock after #{retry_count} attempts: #{key}")
      end

      success
    end
end
