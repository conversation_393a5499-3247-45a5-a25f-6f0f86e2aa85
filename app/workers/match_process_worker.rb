class MatchProcessWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redis, :initialized

    # 初回起動時のクリーンアップ
    def ensure_initialized
      return if @initialized

      @initialized = true
      # プレイヤーSetをクリア（サーバー再起動時の整合性保持）
      redis.del("rank_matching_players")
      Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Cleared rank_matching_players set on initialization")
    end
  end

  # Redis接続初期化（Redlockは削除）
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  def perform
    # 初回実行時のクリーンアップ
    self.class.ensure_initialized

    Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Starting continuous processing loop")

    # 継続的に処理を実行
    loop do
      start_time = Time.now

      begin
        process_matches
      rescue => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Error in processing loop: #{e.message}")
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
      end

      # 最低5秒のインターバルを保つ
      elapsed_time = Time.now - start_time
      if elapsed_time < 5
        sleep_time = 5 - elapsed_time
        sleep(sleep_time)
      end

      # Sidekiqのシャットダウン要求をチェック
      if Thread.current[:should_stop]
        Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Stopping processing loop")
        break
      end
    end
  end

  private

    def process_matches
      begin
        # メイン処理
        player_ids = self.class.redis.smembers("rank_matching_players") || []
        return if player_ids.empty?

        # プレイヤーデータを取得し、同時にtimecountを更新
        players = player_ids.map do |player_id|
          key = "matching_data:#{player_id}"
          json = self.class.redis.get(key)

          # データが存在しない場合はSetからも削除
          unless json
            self.class.redis.srem("rank_matching_players", player_id)
            next
          end

          begin
            data = JSON.parse(json, symbolize_names: true)
            # 開始時刻からの経過時間を計算（秒単位）
            start_time = data[:start_time] || Time.now.to_i
            data[:timecount] = Time.now.to_i - start_time
            Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Player #{player_id}: start_time=#{start_time}, current_time=#{Time.now.to_i}, timecount=#{data[:timecount]}")
            # 更新されたデータをRedisに保存
            self.class.redis.set(key, data.to_json)
            [ data[:id], data ]
          rescue JSON::ParserError => e
            Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "JSON parse error for #{key}: #{e.message}")
            # 壊れたデータは削除
            self.class.redis.del(key)
            self.class.redis.srem("rank_matching_players", player_id)
            nil
          end
        end.compact # nilを除外

        return if players.empty?

        Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Found #{players.size} players")

        # ランクでソート（ランク情報は維持）
        sorted = players.sort_by { |_, data| data[:rank] }

        # シンプルなペア形成（フリーマッチと同様）
        pairs = []
        i = 0
        while i < sorted.size - 1
          pair = [ sorted[i], sorted[i + 1] ]
          Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Found pair: #{pair[0][0]} (rank: #{pair[0][1][:rank]}) and #{pair[1][0]} (rank: #{pair[1][1][:rank]})")
          pairs << pair
          i += 2
        end

        # 各ペアに対してマッチング処理
        matched_players = {}

        pairs.each do |pair|
          player1_id, player1_data = pair[0]
          player2_id, player2_data = pair[1]

          # 両方のキーの存在を確認
          key1 = "matching_data:#{player1_id}"
          key2 = "matching_data:#{player2_id}"

          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Attempting to match players: #{player1_id} (rank: #{player1_data[:rank]}) and #{player2_id} (rank: #{player2_data[:rank]})")

          # マッチング処理
          match_successful = process_matching_pair(key1, key2, player1_id, player2_id, player1_data, player2_data)

          if match_successful
            matched_players[player1_id] = player2_id
            matched_players[player2_id] = player1_id
          end
        end

        if matched_players.any?
          Rails.logger.info(level: "info", worker: "MatchProcessWorker",
            message: "Matched #{matched_players.size / 2} pairs (#{matched_players.size} players).")
        else
          Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "No matches were made this cycle.")
        end
      rescue Redis::BaseError => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Redis error: #{e.message}")
      rescue => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Unexpected error: #{e.message}")
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
      end
    end

    # マッチング処理を行うヘルパーメソッド
    def process_matching_pair(key1, key2, player1_id, player2_id, player1_data, player2_data)
      # 両キーの存在を確認
      exist1 = self.class.redis.exists?(key1)
      exist2 = self.class.redis.exists?(key2)

      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Keys exist? #{key1}: #{exist1}, #{key2}: #{exist2}")

      # 両方のキーが存在する場合のみマッチング処理を実行
      if exist1 && exist2
        # バトルルーム作成
        Rails.logger.info(level: "info", worker: "MatchProcessWorker",
          message: "Creating battle room for players: #{player1_id} (rank: #{player1_data[:rank]}) and #{player2_id} (rank: #{player2_data[:rank]})")

        RankMatchingChannel.create_battle_room(
          player1_id,
          player2_id,
          player1_data[:timecount] || 0,
          player2_data[:timecount] || 0
        )

        # キューからプレイヤーを削除
        self.class.redis.del(key1)
        self.class.redis.del(key2)
        # Setからも削除
        self.class.redis.srem("rank_matching_players", player1_id)
        self.class.redis.srem("rank_matching_players", player2_id)

        Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
          message: "Match successful, deleted keys: #{key1}, #{key2}")

        true
      else
        Rails.logger.warn(level: "warn", worker: "MatchProcessWorker",
          message: "Match failed, keys no longer exist: #{key1}:#{exist1}, #{key2}:#{exist2}")

        false
      end
    rescue => e
      Rails.logger.error(level: "error", worker: "MatchProcessWorker",
        message: "Error matching players #{player1_id} and #{player2_id}: #{e.message}")
      false
    end
end
