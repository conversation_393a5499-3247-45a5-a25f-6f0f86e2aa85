class MatchProcessWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redis, :initialized, :matching_range

    # 初回起動時のクリーンアップ
    def ensure_initialized
      return if @initialized

      @initialized = true
      # プレイヤーSetをクリア（サーバー再起動時の整合性保持）
      redis.del("rank_matching_players")
      Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Cleared rank_matching_players set on initialization")
    end
  end

  # Redis接続初期化（Redlockは削除）
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")
  self.matching_range = ENV.fetch("MATCHING_RANGE", 100).to_i

  def perform
    # 初回実行時のクリーンアップ
    self.class.ensure_initialized

    Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Starting continuous processing loop")

    # 継続的に処理を実行
    loop do
      start_time = Time.now

      begin
        process_matches
      rescue => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Error in processing loop: #{e.message}")
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
      end

      # 最低5秒のインターバルを保つ
      elapsed_time = Time.now - start_time
      if elapsed_time < 5
        sleep_time = 5 - elapsed_time
        sleep(sleep_time)
      end

      # Sidekiqのシャットダウン要求をチェック
      if Thread.current[:should_stop]
        Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Stopping processing loop")
        break
      end
    end
  end

  private

    def process_matches
      begin
        # メイン処理
        player_ids = self.class.redis.smembers("rank_matching_players") || []
        return if player_ids.empty?

        # プレイヤーデータを取得し、同時にtimecountを更新
        players = player_ids.map do |player_id|
          key = "matching_data:#{player_id}"
          json = self.class.redis.get(key)

          # データが存在しない場合はSetからも削除
          unless json
            self.class.redis.srem("rank_matching_players", player_id)
            next
          end

          begin
            data = JSON.parse(json, symbolize_names: true)
            # 開始時刻からの経過時間を計算（秒単位）
            start_time = data[:start_time] || Time.now.to_i
            data[:timecount] = Time.now.to_i - start_time
            Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Player #{player_id}: start_time=#{start_time}, current_time=#{Time.now.to_i}, timecount=#{data[:timecount]}")
            # 更新されたデータをRedisに保存
            self.class.redis.set(key, data.to_json)
            [ data[:id], data ]
          rescue JSON::ParserError => e
            Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "JSON parse error for #{key}: #{e.message}")
            # 壊れたデータは削除
            self.class.redis.del(key)
            self.class.redis.srem("rank_matching_players", player_id)
            nil
          end
        end.compact # nilを除外

        return if players.empty?

        Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Found #{players.size} players")

        # ランクでソート（ランク情報は維持）
        sorted = players.sort_by { |_, data| data[:rank] }

        # 本来のランクマッチングロジック（待機時間とランク差を考慮）
        matched_players = {}

        sorted.each_with_index do |(player_id, player_data), index|
          # すでにマッチ済みのプレイヤーをスキップ
          next if matched_players.key?(player_id)

          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
            message: "Looking for match for player #{player_id} (rank: #{player_data[:rank]}, timecount: #{player_data[:timecount]})")

          # 前後のプレイヤーから最適なマッチを探す
          best_match = find_best_match(sorted, index, player_data, matched_players)

          if best_match
            opponent_id, opponent_data = best_match
            Rails.logger.info(level: "info", worker: "MatchProcessWorker",
              message: "Found match: #{player_id} (rank: #{player_data[:rank]}) vs #{opponent_id} (rank: #{opponent_data[:rank]}), rank_diff: #{(player_data[:rank] - opponent_data[:rank]).abs}")

            # マッチング処理を実行
            key1 = "matching_data:#{player_id}"
            key2 = "matching_data:#{opponent_id}"

            match_successful = process_matching_pair(key1, key2, player_id, opponent_id, player_data, opponent_data)

            if match_successful
              matched_players[player_id] = opponent_id
              matched_players[opponent_id] = player_id
            end
          end
        end

        if matched_players.any?
          Rails.logger.info(level: "info", worker: "MatchProcessWorker",
            message: "Matched #{matched_players.size / 2} pairs (#{matched_players.size} players).")
        else
          Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "No matches were made this cycle.")
        end
      rescue Redis::BaseError => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Redis error: #{e.message}")
      rescue => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Unexpected error: #{e.message}")
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
      end
    end

    # 最適なマッチを見つけるメソッド（元のRankMatchingChannelのロジックを移植）
    def find_best_match(sorted_players, target_index, target_player, matched_players)
      max_range = target_player[:timecount] * self.class.matching_range
      best_match = nil
      best_distance = Float::INFINITY

      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "Max matching range for #{target_player[:id]}: #{max_range} (timecount: #{target_player[:timecount]} * range: #{self.class.matching_range})")

      # 前方向への検索
      (target_index + 1).upto(sorted_players.size - 1) do |i|
        candidate_id, candidate_data = sorted_players[i]

        # すでにマッチ済みをスキップ
        next if matched_players.key?(candidate_id)

        # BOT同士のマッチングを回避
        next if both_players_are_bots?(target_player, candidate_data)

        rank_diff = (candidate_data[:rank] - target_player[:rank]).abs

        # 範囲外の場合は後続も範囲外なので終了
        break if rank_diff > max_range

        if rank_diff < best_distance
          best_distance = rank_diff
          best_match = [ candidate_id, candidate_data ]
          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
            message: "Forward search: better match found - #{candidate_id} with distance #{rank_diff}")
        end
      end

      # 後方向への検索
      (target_index - 1).downto(0) do |i|
        candidate_id, candidate_data = sorted_players[i]

        # すでにマッチ済みをスキップ
        next if matched_players.key?(candidate_id)

        # BOT同士のマッチングを回避
        next if both_players_are_bots?(target_player, candidate_data)

        rank_diff = (candidate_data[:rank] - target_player[:rank]).abs

        # 範囲外の場合は後続も範囲外なので終了
        break if rank_diff > max_range

        if rank_diff < best_distance
          best_distance = rank_diff
          best_match = [ candidate_id, candidate_data ]
          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
            message: "Backward search: better match found - #{candidate_id} with distance #{rank_diff}")
        end
      end

      if best_match.nil?
        Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
          message: "No suitable match found for #{target_player[:id]} within range #{max_range}")
      end

      best_match
    end

    # BOT同士のマッチングを防ぐヘルパーメソッド（元のRankMatchingChannelから移植）
    def both_players_are_bots?(player1, player2)
      # ISBOTキーが存在し、かつtrueの場合のみBOTとして扱う
      player1_is_bot = player1.key?(:isbot) && (player1[:isbot] == true || player1[:isbot] == "true")
      player2_is_bot = player2.key?(:isbot) && (player2[:isbot] == true || player2[:isbot] == "true")

      # デバッグログ：詳細なBOTチェック情報
      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "BOT check - Player1 #{player1[:id]}: has_key=#{player1.key?(:isbot)}, value=#{player1[:isbot]}, is_bot=#{player1_is_bot}")
      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "BOT check - Player2 #{player2[:id]}: has_key=#{player2.key?(:isbot)}, value=#{player2[:isbot]}, is_bot=#{player2_is_bot}")
      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "Both are bots? #{player1_is_bot && player2_is_bot}")

      player1_is_bot && player2_is_bot
    end

    # バトルルーム作成メソッド（RankMatchingChannelから移植）
    def create_battle_room(player1_id, player2_id, p1_timecount, p2_timecount)
      # Redisのアトミックカウンターを使用してユニークなroom_idを生成
      counter = self.class.redis.incr("room_id_counter")
      room_id = "rank_#{counter}"

      Rails.logger.info(level: "info", worker: "MatchProcessWorker",
        message: "Creating battle room for players #{player1_id} and #{player2_id} with unique room_id: #{room_id}")

      # より詳細なブロードキャスト情報のログ
      player1_message = {
        command:       "RankMatchingSuccess",
        room_id:       room_id,
        index_in_room: 0,
        timecount:     p1_timecount,
      }

      player2_message = {
        command:       "RankMatchingSuccess",
        room_id:       room_id,
        index_in_room: 1,
        timecount:     p2_timecount,
      }

      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "To player1: #{player1_message}")
      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
        message: "To player2: #{player2_message}")

      # ActionCableでブロードキャスト
      ActionCable.server.broadcast("rank_matching:#{player1_id}", player1_message)
      ActionCable.server.broadcast("rank_matching:#{player2_id}", player2_message)

      Rails.logger.info(level: "info", worker: "MatchProcessWorker",
        message: "Battle room created successfully with unique room_id: #{room_id} (counter: #{counter})")
    end

    # マッチング処理を行うヘルパーメソッド
    def process_matching_pair(key1, key2, player1_id, player2_id, player1_data, player2_data)
      # 両キーの存在を確認
      exist1 = self.class.redis.exists?(key1)
      exist2 = self.class.redis.exists?(key2)

      Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Keys exist? #{key1}: #{exist1}, #{key2}: #{exist2}")

      # 両方のキーが存在する場合のみマッチング処理を実行
      if exist1 && exist2
        # バトルルーム作成（独自メソッドを使用）
        Rails.logger.info(level: "info", worker: "MatchProcessWorker",
          message: "Creating battle room for players: #{player1_id} (rank: #{player1_data[:rank]}) and #{player2_id} (rank: #{player2_data[:rank]})")

        create_battle_room(
          player1_id,
          player2_id,
          player1_data[:timecount] || 0,
          player2_data[:timecount] || 0
        )

        # キューからプレイヤーを削除
        self.class.redis.del(key1)
        self.class.redis.del(key2)
        # Setからも削除
        self.class.redis.srem("rank_matching_players", player1_id)
        self.class.redis.srem("rank_matching_players", player2_id)

        Rails.logger.debug(level: "debug", worker: "MatchProcessWorker",
          message: "Match successful, deleted keys: #{key1}, #{key2}")

        true
      else
        Rails.logger.warn(level: "warn", worker: "MatchProcessWorker",
          message: "Match failed, keys no longer exist: #{key1}:#{exist1}, #{key2}:#{exist2}")

        false
      end
    rescue => e
      Rails.logger.error(level: "error", worker: "MatchProcessWorker",
        message: "Error matching players #{player1_id} and #{player2_id}: #{e.message}")
      false
    end
end
