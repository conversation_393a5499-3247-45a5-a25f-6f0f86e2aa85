class MatchProcessWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end
  end

  # Redis接続初期化
  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  def perform
    begin
      player_keys = []
      players = []

      # 独自のRedis接続を使用
      begin
        # ロックを使用してプレイヤー情報を安全に取得
        lock_success = with_lock("rank_matching:worker:process", 5000) do
          player_keys = self.class.redis.keys("matching_data:*")
          return if player_keys.empty?

          # プレイヤーデータを取得し、同時にtimecountを更新
          players = player_keys.map do |key|
            json = self.class.redis.get(key)
            next if json.nil?

            begin
              data = JSON.parse(json, symbolize_names: true)
              # timecountを5増加
              data[:timecount] += 5
              # 更新されたデータをRedisに保存
              self.class.redis.set(key, data.to_json)
              [ data[:id], data ]
            rescue JSON::ParserError => e
              Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "JSON parse error for #{key}: #{e.message}")
              # 壊れたデータは削除
              self.class.redis.del(key)
              nil
            end
          end.compact # nilを除外
          true
        end

        # ロックの取得に失敗した場合は処理を中止
        unless lock_success
          Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Failed to acquire lock for processing")
          return
        end
      rescue => e
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Error during data acquisition: #{e.message}")
        return
      end

      return if players.empty?

      matched_players = {}
      sorted = players.sort_by { |_, data| data[:rank] }

      sorted.each_with_index do |(player_id, player), index|
        next if matched_players[player_id]

        match_range = player[:timecount] * RankMatchingChannel.matching_range

        # デバッグログを追加
        Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Evaluating player: #{player_id}, rank: #{player[:rank]}, match range: #{match_range}")

        front, f_dist = RankMatchingChannel.find_matching_player(sorted, index - 1, -1, player, matched_players)
        back, b_dist = RankMatchingChannel.find_matching_player(sorted, index + 1, 1, player, matched_players)

        if front && f_dist <= match_range && (f_dist <= b_dist || back.nil?)
          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Matching players: #{front[:id]} (rank: #{front[:rank]}) and #{player_id} (rank: #{player[:rank]}), distance: #{f_dist}")
          RankMatchingChannel.create_battle_room(front[:id], player_id, front[:timecount], player[:timecount])
          matched_players[player_id] = front[:id]
          matched_players[front[:id]] = player_id
        elsif back && b_dist <= match_range
          Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Matching players: #{player_id} (rank: #{player[:rank]}) and #{back[:id]} (rank: #{back[:rank]}), distance: #{b_dist}")
          RankMatchingChannel.create_battle_room(player_id, back[:id], player[:timecount], back[:timecount])
          matched_players[player_id] = back[:id]
          matched_players[back[:id]] = player_id
        end
      end

      # マッチしたプレイヤーをキューから削除
      if matched_players.any?
        # ロックを使用してプレイヤーデータを安全に削除
        lock_success = with_lock("rank_matching:worker:cleanup", 3000) do
          matched_players.each_key do |id|
            self.class.redis.del("matching_data:#{id}")
          end
          true
        end

        if lock_success
          Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Matched #{matched_players.size/2} pairs (#{matched_players.size} players).")
        else
          Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Failed to acquire lock for cleanup")
        end
      end
    rescue Redis::BaseError => e
      Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Redis error: #{e.message}")
    rescue => e
      Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Unexpected error: #{e.message}")
      Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
    end
  end

  private

    # with_lockヘルパー
    def with_lock(lock_key, ttl = nil)
      settings = self.class.get_env_settings
      start_time = Time.now
      success = false
      retry_count = 0
      max_retries = settings[:retry_count]

      # TTLを適切に設定（指定がない場合はデフォルト値を使用）
      ttl = ttl || settings[:default_ttl]
      # 短いTTLに強制的に調整（長すぎるTTLを防止）
      ttl = [ ttl, settings[:max_lock_ttl] ].min

      # 処理全体のタイムアウト（TTLの3倍を目安）
      timeout = ttl * 3
      timeout_time = start_time + (timeout / 1000.0)

      while !success && retry_count < max_retries && Time.now < timeout_time
        begin
          # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
          remaining_time = (timeout_time - Time.now) * 1000
          if remaining_time < ttl
            adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
            Rails.logger.warn(level: "warn", worker: "MatchProcessWorker", message: "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
            ttl = adjusted_ttl
          end

          self.class.redlock.lock(lock_key, ttl) do |locked|
            if locked
              begin
                Rails.logger.debug(level: "debug", worker: "MatchProcessWorker", message: "Lock acquired for #{lock_key}")
                success = true
                yield
              rescue => e
                Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Error in lock block for #{lock_key}: #{e.message}")
                Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: e.backtrace.join("\n"))
                success = false
              ensure
                # ロック処理が長すぎる場合は警告
                duration = ((Time.now - start_time) * 1000).to_i
                if duration > ttl / 2
                  Rails.logger.warn(level: "warn", worker: "MatchProcessWorker", message: "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
                end
              end
            else
              retry_count += 1
              # 環境に応じたリトライ間隔を使用
              sleep_time = settings[:retry_interval]
              Rails.logger.warn(level: "warn", worker: "MatchProcessWorker", message: "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
              sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
            end
          end
        rescue => e
          retry_count += 1
          Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
          sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
        end
      end

      # タイムアウトした場合の処理
      if Time.now >= timeout_time && !success
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
      end

      if !success && retry_count >= max_retries
        Rails.logger.error(level: "error", worker: "MatchProcessWorker", message: "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
      end

      success
    end
end
