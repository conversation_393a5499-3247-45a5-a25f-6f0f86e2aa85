class FreeMatchWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redlock, :redis

    # 環境に応じた設定値を取得
    def get_env_settings
      if Rails.env.production?
        {
          retry_count:    15, # 10万人規模対応（適度な回数）
          retry_interval: 0.3, # リトライ間隔を適度に設定（応答性とサーバー負荷のバランス）
          lock_ttl:       3000, # ロックのTTLを適度に設定（デッドロック回避）
          max_lock_ttl:   8000, # 最大TTLを適度に設定
          default_ttl:    2000, # デフォルトTTLを適度に設定
        }
      else
        {
          retry_count:    3, # 開発環境では少なめでOK
          retry_interval: 0.1, # リトライ間隔を短めに設定
          lock_ttl:       1000, # ロックのTTLを短めに設定
          max_lock_ttl:   2000, # 最大TTL
          default_ttl:    500, # デフォルトTTL
        }
      end
    end
  end

  # Redis接続初期化
  self.redlock ||= Redlock::Client.new([ ENV["REDIS_URL"] || "redis://localhost:6379/0" ])
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  def perform
    begin
      # FreeMatchingChannelのRedis接続ではなく自身のRedis接続を使用
      player_keys = []
      begin
        # ロックを使用してプレイヤー情報を安全に取得
        lock_success = with_lock("free_matching:worker:process", 5000) do
          player_keys = self.class.redis.keys("free_matching_data:*")
          true
        end

        # ロックの取得に失敗した場合は処理を中止
        unless lock_success
          Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Failed to acquire lock for processing")
          return
        end
      rescue => e
        Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Error during data acquisition: #{e.message}")
        return
      end

      return if player_keys.empty?

      # プレイヤーデータを取得
      players = []
      begin
        lock_success = with_lock("free_matching:worker:data", 3000) do
          players = player_keys.map do |key|
            json = self.class.redis.get(key)

            next unless json

            begin
              data = JSON.parse(json, symbolize_names: true)
              # timecountを5増加
              data[:timecount] += 5
              # 更新されたデータをRedisに保存
              self.class.redis.set(key, data.to_json)
              Rails.logger.debug(level: "debug", worker: "FreeMatchWorker", message: "Parsed data: #{data.inspect}")
              [ data[:id], data ]
            rescue JSON::ParserError => e
              Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "JSON parse error for #{key}: #{e.message}")
              # 壊れたデータは削除
              self.class.redis.del(key)
              nil
            end
          end.compact
          true
        end

        unless lock_success
          Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Failed to acquire lock for data processing")
          return
        end
      rescue => e
        Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Error during data parsing: #{e.message}")
        return
      end

      Rails.logger.info(level: "info", worker: "FreeMatchWorker", message: "Found #{players.size} players")

      # ペアを形成
      pairs = []
      i = 0
      while i < players.size - 1
        pair = [ players[i], players[i + 1] ]
        Rails.logger.info(level: "info", worker: "FreeMatchWorker", message: "Found pair: #{pair[0][0]} and #{pair[1][0]}")
        pairs << pair
        i += 2
      end

      # 各ペアに対してマッチング処理
      matched_players = {}

      pairs.each do |pair|
        player1_id, player1_data = pair[0]
        player2_id, player2_data = pair[1]

        # 両方のキーの存在を確認
        key1 = "free_matching_data:#{player1_id}"
        key2 = "free_matching_data:#{player2_id}"

        Rails.logger.debug(level: "debug", worker: "FreeMatchWorker", message: "Attempting to match players: #{player1_id} and #{player2_id}")

        # 整合的なマッチングのためにロックを取得
        match_successful = process_matching_pair(key1, key2, player1_id, player2_id, player1_data, player2_data)

        if match_successful
          matched_players[player1_id] = player2_id
          matched_players[player2_id] = player1_id
        end
      end

      if matched_players.any?
        Rails.logger.info(level: "info", worker: "FreeMatchWorker",
          message: "Matched #{matched_players.size / 2} pairs (#{matched_players.size} players).")
      else
        Rails.logger.info(level: "info", worker: "FreeMatchWorker", message: "No matches were made this cycle.")
      end
    rescue Redis::BaseError => e
      Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Redis error: #{e.message}")
    rescue => e
      Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Unexpected error: #{e.message}")
      Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: e.backtrace.join("\n"))
    end
  end

  private

    # マッチング処理を行うヘルパーメソッド
    def process_matching_pair(key1, key2, player1_id, player2_id, player1_data, player2_data)
      # 複数のキーを一度にロックして整合性を確保
      lock_key = "free_matching:match:#{player1_id}:#{player2_id}"

      lock_success = with_lock(lock_key, 3000) do
            # ロック取得後に両キーの存在を確認
            exist1 = self.class.redis.exists?(key1)
            exist2 = self.class.redis.exists?(key2)

            Rails.logger.debug(level: "debug", worker: "FreeMatchWorker", message: "Keys exist? #{key1}: #{exist1}, #{key2}: #{exist2}")

            # 両方のキーが存在する場合のみマッチング処理を実行
            if exist1 && exist2
              # バトルルーム作成
              Rails.logger.info(level: "info", worker: "FreeMatchWorker",
                message: "Creating battle room for players: #{player1_id} and #{player2_id}")

              FreeMatchingChannel.create_battle_room(
                player1_id,
                player2_id,
                player1_data[:timecount] || 0,
                player2_data[:timecount] || 0
              )

              # キューからプレイヤーを削除
              self.class.redis.del(key1)
              self.class.redis.del(key2)

              Rails.logger.debug(level: "debug", worker: "FreeMatchWorker",
                message: "Match successful, deleted keys: #{key1}, #{key2}")

              return true
            else
              Rails.logger.warn(level: "warn", worker: "FreeMatchWorker",
                message: "Match failed, keys no longer exist: #{key1}:#{exist1}, #{key2}:#{exist2}")

              return false
            end
          end

      # ロック取得に失敗した場合
      unless lock_success
        Rails.logger.warn(level: "warn", worker: "FreeMatchWorker",
          message: "Failed to acquire lock for players #{player1_id} and #{player2_id}")
      end

      lock_success
    rescue => e
      Rails.logger.error(level: "error", worker: "FreeMatchWorker",
        message: "Error matching players #{player1_id} and #{player2_id}: #{e.message}")
      false
    end

    # with_lockヘルパー
    def with_lock(lock_key, ttl = nil)
      settings = self.class.get_env_settings
      start_time = Time.now
      success = false
      retry_count = 0
      max_retries = settings[:retry_count]

      # TTLを適切に設定（指定がない場合はデフォルト値を使用）
      ttl = ttl || settings[:default_ttl]
      # 短いTTLに強制的に調整（長すぎるTTLを防止）
      ttl = [ ttl, settings[:max_lock_ttl] ].min

      # 処理全体のタイムアウト（TTLの3倍を目安）
      timeout = ttl * 3
      timeout_time = start_time + (timeout / 1000.0)

      while !success && retry_count < max_retries && Time.now < timeout_time
        begin
          # 残り時間を計算して、タイムアウトに近づいている場合はTTLを短くする
          remaining_time = (timeout_time - Time.now) * 1000
          if remaining_time < ttl
            adjusted_ttl = [ remaining_time.to_i, 500 ].max # 最低でも500ms
            Rails.logger.warn(level: "warn", worker: "FreeMatchWorker", message: "Adjusting TTL from #{ttl}ms to #{adjusted_ttl}ms due to timeout approaching")
            ttl = adjusted_ttl
          end

          self.class.redlock.lock(lock_key, ttl) do |locked|
            if locked
              begin
                Rails.logger.debug(level: "debug", worker: "FreeMatchWorker", message: "Lock acquired for #{lock_key}")
                success = true
                yield
              rescue => e
                Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Error in lock block for #{lock_key}: #{e.message}")
                Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: e.backtrace.join("\n"))
                success = false
              ensure
                # ロック処理が長すぎる場合は警告
                duration = ((Time.now - start_time) * 1000).to_i
                if duration > ttl / 2
                  Rails.logger.warn(level: "warn", worker: "FreeMatchWorker", message: "Lock operation took #{duration}ms for #{lock_key}, which is longer than #{ttl/2}ms (half of TTL)")
                end
              end
            else
              retry_count += 1
              # 環境に応じたリトライ間隔を使用
              sleep_time = settings[:retry_interval]
              Rails.logger.warn(level: "warn", worker: "FreeMatchWorker", message: "Failed to acquire lock (attempt #{retry_count}/#{max_retries}): #{lock_key}, retrying in #{sleep_time}s")
              sleep(sleep_time) if retry_count < max_retries && Time.now < timeout_time
            end
          end
        rescue => e
          retry_count += 1
          Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Lock error for #{lock_key} (attempt #{retry_count}/#{max_retries}): #{e.message}")
          sleep(settings[:retry_interval]) if retry_count < max_retries && Time.now < timeout_time
        end
      end

      # タイムアウトした場合の処理
      if Time.now >= timeout_time && !success
        Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Operation timed out after #{((Time.now - start_time) * 1000).to_i}ms for #{lock_key}")
      end

      if !success && retry_count >= max_retries
        Rails.logger.error(level: "error", worker: "FreeMatchWorker", message: "Failed to acquire lock after #{retry_count} attempts: #{lock_key}")
      end

      success
    end
end
