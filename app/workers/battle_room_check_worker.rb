class BattleRoomCheckWorker
  include Sidekiq::Worker

  # クラス変数として設定
  class << self
    attr_accessor :redis, :initialized

    # 初回起動時のクリーンアップ
    def ensure_initialized
      return if @initialized

      @initialized = true
      # バトルルームSetをクリア（サーバー再起動時の整合性保持）
      redis.del("battle_rooms")
      Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker", message: "Cleared battle_rooms set on initialization")
    end
  end

  # Redis接続初期化
  self.redis ||= Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

  def perform
    # 初回実行時のクリーンアップ
    self.class.ensure_initialized

    Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker", message: "Starting continuous room check loop")

    # 継続的に処理を実行
    loop do
      start_time = Time.now

      begin
        check_battle_rooms
      rescue => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: "Error in room check loop: #{e.message}")
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: e.backtrace.join("\n"))
      end

      # 最低10秒のインターバルを保つ
      elapsed_time = Time.now - start_time
      if elapsed_time < 10
        sleep_time = 10 - elapsed_time
        sleep(sleep_time)
      end

      # Sidekiqのシャットダウン要求をチェック
      if Thread.current[:should_stop]
        Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker", message: "Stopping room check loop")
        break
      end
    end
  end

  private

    def check_battle_rooms
      begin
        # 全バトルルームIDを取得（KEYSではなくSetを使用）
        room_ids = self.class.redis.smembers("battle_rooms") || []
        return if room_ids.empty?

        Rails.logger.debug(level: "debug", worker: "BattleRoomCheckWorker", message: "Checking #{room_ids.size} battle rooms")

        room_ids.each do |room_id|
          room_key = "battle_room:#{room_id}"
          check_room(room_key)
        end

      rescue Redis::BaseError => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: "Redis error: #{e.message}")
      rescue => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: "Unexpected error: #{e.message}")
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: e.backtrace.join("\n"))
      end
    end

    def check_room(room_key)
      json = self.class.redis.get(room_key)
      return unless json

      begin
        room = JSON.parse(json, symbolize_names: true)
        room_id = room_key.sub("battle_room:", "")

        room_created_at = room[:played_at]
        return unless room_created_at

        # 作成から15秒経過しているかチェック
        created_time = Time.parse(room_created_at.to_s)
        elapsed_seconds = Time.now - created_time

        Rails.logger.debug(level: "debug", worker: "BattleRoomCheckWorker",
          message: "Room #{room_id}: elapsed #{elapsed_seconds.to_i}s, player_0: #{room[:player_0_id].present? ? 'present' : 'missing'}, player_1: #{room[:player_1_id].present? ? 'present' : 'missing'}")

        if elapsed_seconds >= 15
          # 片方のプレイヤーが参加していない場合
          if room[:player_0_id].present? && room[:player_1_id].blank?
            Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker",
              message: "Room #{room_id}: Player 1 missing after 15 seconds, removing player 0 (#{room[:player_0_id]})")
            force_leave_room(room_id, room[:player_0_id], 0)

          elsif room[:player_1_id].present? && room[:player_0_id].blank?
            Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker",
              message: "Room #{room_id}: Player 0 missing after 15 seconds, removing player 1 (#{room[:player_1_id]})")
            force_leave_room(room_id, room[:player_1_id], 1)

          elsif room[:player_0_id].blank? && room[:player_1_id].blank?
            Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker",
              message: "Room #{room_id}: Both players missing after 15 seconds, deleting room")
            self.class.redis.del(room_key)
            # Setからも削除
            self.class.redis.srem("battle_rooms", room_id)
          end
        end

      rescue JSON::ParserError => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: "JSON parse error for #{room_key}: #{e.message}")
        # 壊れたデータは削除
        self.class.redis.del(room_key)
        # Setからも削除
        self.class.redis.srem("battle_rooms", room_id)
      rescue => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker", message: "Error checking room #{room_key}: #{e.message}")
      end
    end

    def force_leave_room(room_id, player_id, player_index)
      begin
        # kick形式のメッセージを送信
        message = {
          command:    "kick",
          error_code: "KBTO:対戦相手が接続しませんでした",
          reason:     "",
          reconnect:  false,
          message:    "対戦相手が接続しませんでした",
        }

        # バトルルームに直接ブロードキャスト
        ActionCable.server.broadcast("battle_room:#{room_id}", message)

        # ルームを削除
        self.class.redis.del("battle_room:#{room_id}")
        # Setからも削除
        self.class.redis.srem("battle_rooms", room_id)

        Rails.logger.info(level: "info", worker: "BattleRoomCheckWorker",
          message: "Successfully removed player #{player_id} from room #{room_id} due to connection timeout")

      rescue => e
        Rails.logger.error(level: "error", worker: "BattleRoomCheckWorker",
          message: "Error forcing leave for player #{player_id} in room #{room_id}: #{e.message}")
      end
    end
end
