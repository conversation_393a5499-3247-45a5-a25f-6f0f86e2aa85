# Batch UserStatusService to reduce lock contention
class BatchUserStatusService
  class << self
    def batch_update(&block)
      @batch_mode = true
      @batch_updates = []

      begin
        yield

        # Process all updates in a single batch
        if @batch_updates.any?
          process_batch_updates
        end
      ensure
        @batch_mode = false
        @batch_updates = []
      end
    end

    def update_status(user_id, status, metadata = {})
      if @batch_mode
        @batch_updates << { user_id: user_id, status: status, metadata: metadata }
      else
        UserStatusService.update_status(user_id, status, metadata)
      end
    end

    private

    def process_batch_updates
      return if @batch_updates.empty?

      log_info("BatchUserStatusService: Processing #{@batch_updates.size} updates")

      # Group updates by user_id to avoid duplicate updates
      grouped_updates = @batch_updates.group_by { |update| update[:user_id] }

      # Process each user's final status
      grouped_updates.each do |user_id, updates|
        # Use the last update for each user
        final_update = updates.last
        UserStatusService.update_status(final_update[:user_id], final_update[:status], final_update[:metadata])
      end

      log_info("BatchUserStatusService: Completed #{grouped_updates.size} unique user updates")
    end

    def log_info(message)
      if defined?(Rails) && Rails.logger
        Rails.logger.info(message)
      else
        puts message
      end
    end
  end
end
