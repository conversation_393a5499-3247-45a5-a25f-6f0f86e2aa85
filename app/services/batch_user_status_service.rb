require 'json'
require 'securerandom'

class BatchUserStatusService
  REDIS_LOCK_KEY = "batch_user_status_service_lock"
  REDIS_LIST_KEY = "batch_user_status_service:updates"
  LOCK_TIMEOUT = 5000 # ms

  class << self
    def update_status(user_id, status, metadata = {})
      # Push update vào Redis list dưới dạng JSON
      update = { user_id: user_id, status: status, metadata: metadata }.to_json
      Redis.current.rpush(REDIS_LIST_KEY, update)
    end

    def process_batch_updates
      with_redis_lock do
        updates = []
        loop do
          # Lấy từng phần tử ra
          json_update = Redis.current.lpop(REDIS_LIST_KEY)
          break unless json_update
          updates << JSON.parse(json_update, symbolize_names: true)
        end

        return if updates.empty?

        # Gom nhóm theo user_id, lấy update cuối cùng
        grouped = updates.group_by { |u| u[:user_id] }
        grouped.each do |user_id, updates|
          final_update = updates.last
          UserStatusService.update_status(final_update[:user_id], final_update[:status], final_update[:metadata])
        end

        log_info("BatchUserStatusService: Processed #{grouped.size} unique user updates")
      end
    end

    private

    def with_redis_lock(&block)
      lock_acquired = false
      lock_value = SecureRandom.hex(10)

      begin
        lock_acquired = Redis.current.set(REDIS_LOCK_KEY, lock_value, nx: true, px: LOCK_TIMEOUT)
        if lock_acquired
          yield
        else
          log_info("BatchUserStatusService: Failed to acquire Redis lock, skipping batch processing")
        end
      ensure
        if lock_acquired
          lua_script = <<~LUA
            if redis.call('GET', KEYS[1]) == ARGV[1] then
              return redis.call('DEL', KEYS[1])
            else
              return 0
            end
          LUA
          Redis.current.eval(lua_script, keys: [REDIS_LOCK_KEY], argv: [lock_value])
        end
      end
    end

    def log_info(msg)
      if defined?(Rails) && Rails.logger
        Rails.logger.info(msg)
      else
        puts msg
      end
    end
  end
end
