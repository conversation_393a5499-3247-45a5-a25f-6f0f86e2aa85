class SkinService
    # ガチャ結果からユーザーのスキンを直接更新
    def self.update_box_from_results(user, results)
        # 同じskin_idを持つスキンをグループ化
        grouped_skins = {}
        results.each do |skin|
            skin_id = skin[:skin_id].to_s

            grouped_skins[skin_id] ||= {"skin_level" => 1}
        end

        # ユーザーのスキンボックスを取得し、更新する
        skins_box = user.skins_box || {}

        # グループ化されたスキンごとに処理
        grouped_skins.each do |skin_id, skin_data|
            # 既存のスキンは上書きしない（レベルは維持）
            unless skins_box[skin_id].present?
                skins_box[skin_id] = skin_data
            end
        end

        # 更新されたスキンボックスをユーザーに設定
        user.skins_box = skins_box
    end

    # スキンをボックスに追加
    def self.add_skin_to_box(user, skin_id)
        skin_id = skin_id.to_s
        skins_box = user.skins_box || {}

        # スキンが存在しない場合は新しく作成
        unless skins_box[skin_id].present?
            skins_box[skin_id] = {"skin_level" => 1}
        end

        # 更新されたスキンボックスをユーザーに設定
        user.skins_box = skins_box
    end

    def self.update_sands_from_results(user, results)
        results.each do |sand|
            group_id = sand[:group_id].to_s
            has_items = user.has_items || {}
            skin_sands = has_items["skin_sands"] || {}
            if skin_sands[group_id].present?
                skin_sands[group_id]["count"] = (skin_sands[group_id]["count"] || 0) + (sand[:count] || 0)
            else
                skin_sands[group_id] = {"count" => sand[:count]}
            end

            has_items["skin_sands"] = skin_sands
            user.has_items = has_items
        end
    end

    def self.level_up(user, skin_id)
        begin
            group_id = MasterDatum.skin_groups(skin_id)
            skins_box = user.skins_box || {}

            # スキンが存在しない場合はエラー
            unless skins_box[skin_id.to_s].present? # skin_boxはskin_idをキーとしているのでstringにする
                raise StandardError.new("skin_not_found")
            end

            # スキンの上限レベルが上限かどうかを調べる
            current_level = skins_box[skin_id.to_s]["skin_level"] || 1
            if current_level >= ConstantService.get('skin.skin_max_level')
                raise StandardError.new("skin_max_level")
            end

            # スキンに対応した砂を使用
            has_items = user.has_items || {}
            skin_sands = has_items["skin_sands"] || {}
            group_id_str = group_id.to_s
            skin_level_up_cost = ConstantService.get('skin.skin_level_up_cost') || 1 # スキンレベルアップに必要な砂の個数

            # 対応する砂があるか確認
            if !skin_sands[group_id_str].present? || (skin_sands[group_id_str]["count"] || 0) < skin_level_up_cost
                raise StandardError.new("not_enough_sands")
            end

            # 砂を消費
            skin_sands[group_id_str]["count"] -= skin_level_up_cost

            # スキンレベルを上げる
            skins_box[skin_id.to_s]["skin_level"] = current_level + 1
            user.skins_box = skins_box
            has_items["skin_sands"] = skin_sands
            user.has_items = has_items
            user.save!
            
            return {skin: {skin_id: skin_id, skin_level: skins_box[skin_id.to_s]["skin_level"]}, sands: ItemService.skin_sands_to_array(skin_sands),user: UserService.get_res(user)}
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::SkinsController, "level_up")
        end
    end

    # スキン情報をJSON形式で取得
    def self.get_skins_json(user)
        skins_box = user.skins_box || {}
        
        skin_list = []
        skins_box.each do |skin_id, skin_data|
            skin_list << {
                skin_id: skin_id.to_i,
                skin_level: skin_data["skin_level"] || 1
            }
        end
        
        return skin_list
    end

    private
    def self.generate_skin_uid
        # 一番最新のuidに+1したものを返す
        if Skin.order(uid: :desc).first.nil?
            return 1
        else
            return Skin.order(uid: :desc).first.uid + 1
        end
    end
end
