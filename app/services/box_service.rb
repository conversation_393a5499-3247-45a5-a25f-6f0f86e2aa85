class BoxService

  # ガチャ結果からユーザーのカードを直接更新
  def self.update_box_from_results(user, results)
    # 同じcard_idを持つカードをグループ化し、エディションタイプごとにカウントを集計
    grouped_cards = {}
    results.each do |card|
      card_id = card[:card_id].to_s
      edition_type = card[:edition]

      grouped_cards[card_id] ||= {"nr" => 0, "sh" => 0, "pr" => 0}

      case edition_type
      when 1
        grouped_cards[card_id]["nr"] += 1
      when 2
        grouped_cards[card_id]["sh"] += 1
      when 3
        grouped_cards[card_id]["pr"] += 1
      end
    end

    # ユーザーのボックスを取得し、更新する
    box = user.box || {}

    # グループ化されたカードごとに処理
    grouped_cards.each do |card_id, editions|
      if box[card_id].present?
        # 既存のカードを更新
        box[card_id]["nr"] = (box[card_id]["nr"] || 0) + editions["nr"]
        box[card_id]["sh"] = (box[card_id]["sh"] || 0) + editions["sh"]
        box[card_id]["pr"] = (box[card_id]["pr"] || 0) + editions["pr"]
      else
        # 新しいカードを追加
        box[card_id] = editions
      end
    end

    # 更新されたボックスをユーザーに設定
    user.box = box
  end

  # カードをボックスに追加
  def self.add_card_to_box(user, card_id, edition_type, count)
    card_id = card_id.to_s
    box = user.box || {}

    # カードのエントリーを初期化（存在しない場合）
    box[card_id] ||= {"nr" => 0, "sh" => 0, "pr" => 0}

    # エディションタイプに応じて枚数を増やす
    case edition_type
    when 1
      box[card_id]["nr"] += count
    when 2
      box[card_id]["sh"] += count
    when 3
      box[card_id]["pr"] += count
    else
      raise "Invalid edition type"
    end

    # 更新されたボックスをユーザーに設定
    user.box = box
  end

  # カードをボックスから削除
  def self.remove_card_from_box(user, card_id, edition_type, count = 1)
    card_id = card_id.to_s
    box = user.box || {}

    # カードが存在しない場合
    return false unless box[card_id].present?

    # エディションタイプに応じて枚数を減らす
    case edition_type
    when 1
      return false if (box[card_id]["nr"] || 0) < count
      box[card_id]["nr"] -= count
    when 2
      return false if (box[card_id]["sh"] || 0) < count
      box[card_id]["sh"] -= count
    when 3
      return false if (box[card_id]["pr"] || 0) < count
      box[card_id]["pr"] -= count
    end

    # すべてのエディションの枚数が0になった場合、カード自体を削除
    if total_count(box[card_id]) == 0
      box.delete(card_id)
    end

    # 更新されたボックスをユーザーに設定
    user.box = box

    return true
  end

  # カードの総数を取得
  def self.total_count(card_data)
    (card_data["nr"] || 0) + (card_data["sh"] || 0) + (card_data["pr"] || 0)
  end

  # ユーザーのカード所持数を取得
  def self.get_card_count(user, card_id, edition_type = nil)
    card_id = card_id.to_s
    box = user.box || {}

    # カードが存在しない場合
    return 0 unless box[card_id].present?

    # エディションタイプが指定されていない場合は合計を返す
    if edition_type.nil?
      return total_count(box[card_id])
    else
      case edition_type
      when 1
        return box[card_id]["nr"] || 0
      when 2
        return box[card_id]["sh"] || 0
      when 3
        return box[card_id]["pr"] || 0
      else
        return 0
      end
    end
  end

  # ユーザーのカードコレクションをJSON形式で取得
  def self.get_box_json(user)
    box = user.box || {}
    cards = []

    box.each do |card_id, editions|
      cards << {
        card_id: card_id.to_i,
        normal: editions["nr"] || 0,
        shine: editions["sh"] || 0,
        premium: editions["pr"] || 0
      }
    end

    return {
      cards: cards
    }
  end

  # ユーザーのボックスを初期化するメソッド
  def self.init_box
    # 初期カードの設定
    default_card_ids = []

    if ConstantService.get("danger.default_all_box")
      # 全てのカードを初期カードに設定
      MasterDatum.get_cards.each do |card|
        if card["ID"] >= 10000
          default_card_ids << card["ID"]
        end
      end
    else
      default_card_ids = ConstantService.get("deck.default_box")
    end

    # 初期カードの設定
    box = {}
    
    # 各カードIDに対して、ノーマルカードを3枚ずつ追加
    default_card_ids.each do |card_id|
      box[card_id.to_s] = {"nr" => 3, "sh" => 0, "pr" => 0}
    end
    
    return box
  end

  private
  # 該当IDのレコードを探す（互換性のために残しておく）
  def self.find_entry(box, card_id)
    card_id = card_id.to_s
    return box[card_id]
  end
end