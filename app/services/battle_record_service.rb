class BattleRecordService

    def self.get_battle_records(user)
        begin
            # バトルレコードの取得
            open_id = user.open_id
            battle_records = Match.where("player_1 = ? OR player_0 = ?", open_id, open_id).order(created_at: :desc).limit(20)
            if battle_records.empty?
                raise StandardError.new("no_battle_records")
            end

            # リスポンスデータの作成
            response_data = []
            # user情報持ってるのに再度user情報取得してるので無駄なSQL
            battle_records.each do |br|
                user_0 = User.find_by(open_id: br.player_0)
                user_1 = User.find_by(open_id: br.player_1)
                if user_0.nil? || user_1.nil?
                    raise StandardError.new("user_not_found")
                end
                response_data << {
                    player_1: br.player_1,
                    player_0: br.player_0,
                    name_1: user_1.name,
                    name_0: user_0.name,
                    icon_1: user_1.profile_settings["icon"],
                    icon_0: user_0.profile_settings["icon"],
                    icon_frame_1: user_1.profile_settings["icon_frame"],
                    icon_frame_0: user_0.profile_settings["icon_frame"],
                    reason: br.reason,
                    result: br.result,
                    played_at: br.played_at,
                    game_mode: br.game_mode,
                    event_id: br.event_id,
                    matching_time_1: br.matching_time_1,
                    matching_time_0: br.matching_time_0,
                    deck_1: br.deck_1,
                    deck_0: br.deck_0,
                    group_1: br.group_1,
                    group_0: br.group_0,
                    rank: br.rank,
                    before_rate_1: br.before_rate_1,
                    before_rate_0: br.before_rate_0,
                    after_rate_1: br.after_rate_1,
                    after_rate_0: br.after_rate_0,
                    replay_data: br.replay_data,
                    other_data: br.other_data
                }
            end
            return {battle_recs: response_data}
        rescue => e
            ErrorService.handle_exception(e, "BattleRecordService", "get_battle_records")
        end
    end

    # 使わない
    def self.get_replay(params)
        battle_record = Match.find(params[:id])
        return battle_record
    end

    # 使わない
    def self.report(params)
        battle_record = Match.find(params[:id])
        return battle_record
    end
end
