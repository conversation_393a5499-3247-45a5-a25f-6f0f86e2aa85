class RateStopperService
  # ストッパーレート値の配列
  RATE_STOPPERS = [ 10000 ]

  # レートにストッパーを適用するメソッド
  # @param current_rate [Integer] 現在計算されたレート
  # @param previous_rate [Integer] 以前のレート（変更前）
  # @return [Integer] ストッパーを適用した後の最終レート
  def self.apply_stopper(current_rate, previous_rate)
    # 以前のレートが到達していた最高ストッパー値を見つける
    highest_stopper = RATE_STOPPERS.select { |stopper| previous_rate >= stopper }.max

    # ストッパーがない場合または新レートがストッパーより高い場合はそのまま返す
    return current_rate if highest_stopper.nil? || current_rate >= highest_stopper

    # ストッパー値を適用（レートがストッパー値を下回らないようにする）
    highest_stopper
  end
end
