class LoginService
    def self.last_update_login_time
        today_reset = Time.parse(ConstantService::LOGIN_UPDATE_TIME)
        update_time = Time.current < today_reset ? today_reset - 1.day : today_reset
        return update_time
    end
    def self.next_update_login_time
        return last_update_login_time + 1.day
    end

    def self.is_first_login_of_day?(user)
        return false if user.nil?

        # 最終ログイン時刻がない場合は初めてのログイン
        return true if user.last_login_at.nil?

        # 最終ログイン時刻が最後のログイン更新時間より前かどうかをチェック
        last_update_time = last_update_login_time
        user.last_login_at < last_update_time
    end

    def self.process_login(user, request)
        return false if user.nil?

        begin
            ActiveRecord::Base.transaction do
                # 現在の時刻
                current_time = Time.current
                ShopPassService.check_pass_active(user) # 毎回のログイン時にサブスクチェック
                # 今日初めてのログインかどうかを判断
                if is_first_login_of_day?(user)
                    # ログイン日数をインクリメント
                    user.increment!(:login_days)
                    # TODO デイリーでリセットするセーブデータの処理を追加
                    ChestService.on_new_day(user) # user.saveをしている
                    DailyShopService.on_new_day(user) # user.saveをしている
                    ShopPassService.handle_analysis_pass(user) # 分析パスのギフトをチェック
                    ShopPassService.handle_pack_pass(user) # パックパスのギフトをチェック
                end
                # Update last_login_at timestamp
                user.update(last_login_at: current_time)
                # ログイン情報を記録
                login_data = {
                    ip_address: request.remote_ip,
                    device: request.user_agent,
                    first_login_of_day: is_first_login_of_day?(user)
                }
                return {
                    success: true,
                    first_login_of_day: is_first_login_of_day?(user),
                    login_days: user.login_days,
                    last_login_at: user.last_login_at
                }
            end
        rescue => e
            Rails.logger.error("ログイン処理エラー: #{e.message}")
            return {
                success: false,
                error: e.message
            }
        end
    end
end