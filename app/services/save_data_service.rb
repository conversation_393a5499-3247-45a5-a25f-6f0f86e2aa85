class SaveDataService
    # 各キーのテンプレート
    DATA_TEMPLATES = {
        "login_bonuses" => {normal: {:got_count => 0, :last_receive_at => "" },
                            event: [{ :uid => 0, :last_index => 0, :last_receive_at => "" }]}, # ハッシュで保存(eventはarrayで保存)
        "shop" => { :daily_shop => { :bought_uids => [], :items => [],:update_count => 0 },
                    :bonus => { :charged_bonuses => [], :consumed_bonuses =>[], :total_charged_dia => 0, :total_consumed_dia => 0 },
                    :dia_bundles => [{:uid => 1, :bought_count => 0}],
                    :cash_bundles => [{:uid => 1, :bought_count => 0}],
                    :passes => {:pack_pass => {is_active: false, expires_date: "",auto_renew: false}, :analysis_pass => {is_active: false, expires_date: "",auto_renew: false}}}, # hashで保存
        "battle_passes" => { :season => 0, :mission_pts => 0,:is_premium => false, :free => { :last_index => 0 }, :premium => { :last_index => 0 } }, # 配列で保存
        "solo_modes" => { :mode => 1, :score => 0 },
        "profile" => { :last_update_at => "" ,:name_change_count => 0}, # hashで保存
        "chests" => { :category => "", :status =>"",:uid => 0}, # 配列で保存
        "pack" => { :pack_id => 0, :pts => 0, :count => 0 }, # 配列で保存
        "details" => { :daily_win_count => 0 },
        "decks" => { :deck_data => ""}, # hashで保存
        "transfer_code" => { :code => "", :limit_date => DateTime.now} # hashで保存
    }.freeze

    # データのバリデーション(キーの存在とデータの中のキーと型チェック)
    def self.validate_data_format?(key, data)
        return false unless DATA_TEMPLATES[key]
        template_keys = DATA_TEMPLATES[key].keys
        data_keys = data.keys
        return false if(data_keys - template_keys).any?
        data_keys.each do |k|
            # ブール値の場合は、falseとtrueのみ許可
            if DATA_TEMPLATES[key][k].class == FalseClass || DATA_TEMPLATES[key][k].class == TrueClass
                return false unless (data[k] == false || data[k] == true)
            else
                return false unless data[k].is_a?(DATA_TEMPLATES[key][k].class)
            end
        end
        return true
    end

    # user_save_data の特定のキーにデータを追加・更新するメソッド
    def self.add_user_save_data?(user,key, new_data)
        unless validate_data_format?(key, new_data)
            return false
        end
        save_data = user.user_save_data || {}
        current_data = save_data[key] || []
        existing_index = nil
        case key
        when "login_bonuses"
            existing_index = current_data.index { |d| d["uid"] == new_data[:uid] }
        when "battle_passes"
            existing_index = current_data.index { |d| d["season"] == new_data[:season] }
        when "pack"
            existing_index = current_data.index { |d| d["pack_id"] == new_data[:pack_id] }
        end
        if existing_index.present?
            current_data[existing_index] = new_data
        else
            current_data << new_data
        end
        # 更新
        save_data[key] = current_data
        user.user_save_data = save_data
        return true
    end

    # ハッシュのデータを更新するメソッド
    def self.update_user_save_data_hash?(user,key, new_data)
        unless validate_data_format?(key, new_data)
            return false
        end
        user.user_save_data[key] ||= {}
        # TODO ハッシュのマージがうまくいかない
        user.user_save_data[key].merge!(new_data) # deep_mergeがうまく機能しないので"shop"=>{:daily_shop=>{}}までしかmergeされない
        return true
    end

    # 配列のデータを更新するメソッド
    def self.update_user_save_data_array?(user,key, new_data)
        new_data.each do |data|
            unless validate_data_format?(key, data)
                return false
            end
        end
        user.user_save_data[key] = new_data
        return true
    end
end