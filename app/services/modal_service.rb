class ModalService


    def self.get_info_modal(title, desc, cancel_text = "閉じる")
        return {
            show_modal: {
                type: "info",
                title: title,
                desc: desc,
                ok_text: "",
                cancel_text: cancel_text
            }
        }
    end

    def self.get_url_modal(title, desc, ok_text, cancel_text = "閉じる", url = "")
        return {
            show_modal: {
                type: "url",
                title: title,
                desc: desc,
                url: url,
                ok_text: ok_text,
                cancel_text: cancel_text
            }
        }
    end

    def self.get_back_title_modal(title, desc, ok_text = "タイトルに戻る")
        return {
            show_modal: {
                type: "back_title",
                title: title,
                desc: desc,
                ok_text: ok_text,
                cancel_text: ""
            }
        }
    end


end