# app/services/user_service.rb
class UserService
    def self.fetch_user_data(user_id)
      user = User.find_by(open_id: user_id)
      return nil unless user
  
      {
        name: user.name,
        icon: user.icon,
        icon_frame: user.icon_frame,
        title: user.title,
        open_id: user.open_id
      }
    end

    # TODO 使わないかも
    # 他のユーザーのデータを取得
    def self.get_other_user(open_id)
        user = User.find_by(open_id: open_id).select(
          :open_id,
          :name,
          :rate,
          :icon,
          :icon_frame,
          :title,
          :card_collection,
          :badges
        )
        user
    end

    def self.get_other_user_res(other_user)
      other_user_json = {
        open_id: other_user.open_id,
        name: other_user.name,
        score: {
          rate: other_user.rate || 0,
          wins: other_user.wins || 0,
          atk: other_user.user_scores.where(category: "atk").last&.score || 0,
          def: other_user.user_scores.where(category: "def").last&.score || 0,
          damage: other_user.user_scores.where(category: "damage").last&.score || 0,
        },
        profile_settings: ProfileService.get_user_profile(other_user),
        last_login_at: other_user.last_login_at&.to_s,
        exp: other_user.exp || 0,
      }
    end

    def self.create_user_with_log(user_params, request)
        # ユーザー作成
        user = User.new(user_params)

        # 初期データ設定
        self.set_defaults(user)

        # パスワード生成
        row_password = SecureRandom.hex(32)
        user.password = row_password

        # ユーザー保存
        unless user.save
          error_messages = user.errors.messages.values.flatten.join("\n")
          return { success: false, errors: error_messages }
        end
        
        return { success: true, user: user, password: row_password }
    end
    
    # 初期データ設定
    def self.set_defaults(user)
      user.free_dia = 0
      user.ios_dia = 0
      user.android_dia = 0
      user.steam_dia = 0
      user.tutorial_status = "UNCOMPLETED"
      user.login_days = 0
      user.exp = 0
      user.rate = 0
      user.wins = 0
      user.user_settings = ""
      user.open_id = generate_open_id
      user.profile_settings = ProfileService.init_profile() # TODO 初期アイテムの追加があれば追加する
      user.user_save_data = {}
      user.has_items = ItemService.init_item_data() # TODO 初期アイテムの追加があれば追加する
      user.box = BoxService.init_box()
    end

    # ユーザーID生成
    def self.generate_open_id
      # 1000000000000000以上、9999999999999999以下のランダムな数値を生成

      # 30回まで生成を試みる
      for i in 1..30
        open_id = SecureRandom.random_number(9_000_000_000_000_000) + 1_000_000_000_000_000
        open_id_str = open_id.to_s
        return open_id_str if User.find_by(open_id: open_id_str).nil?
      end
      nil
    end

    def self.get_res(user)
      if user.nil?
        return nil
      end

      box_data = BoxService.get_box_json(user)
      pass_status = user.user_save_data.dig("shop", "passes") || {
        "pack_pass"=>{"is_active"=>false, "auto_renew"=>false, "expires_date"=>""},
        "analysis_pass"=>{"is_active"=>false, "auto_renew"=>false, "expires_date"=>""}
      }

      user_json = {
        open_id: user.open_id,
        name: user.name,
        free_dia: user.free_dia,
        ios_dia: user.ios_dia || 0,
        android_dia: user.android_dia || 0,
        steam_dia: user.steam_dia || 0,
        login_days: user.login_days,
        box: box_data,
        tutorial_status: user.tutorial_status || "UNCOMPLETED",
        score: {
          # 直接ユーザーテーブルから取得
          rate: user.rate || 0,
          wins: user.wins || 0,
          # 他のカテゴリはuser_scoresテーブルから取得
          atk: user.user_scores.where(category: "atk").last&.score || 0,
          def: user.user_scores.where(category: "def").last&.score || 0,
          damage: user.user_scores.where(category: "damage").last&.score || 0,
        },
        exp: user.exp || 0,
        guild_id: user.guild_id || -1,
        last_login_at: user.last_login_at&.to_s || Time.now.to_s,
        user_settings: user.user_settings || "", # user_settingsはstringで返す
        profile_settings: ProfileService.get_user_profile(user),
        has_items: ItemService.get_item_data(user),
        pass_status: pass_status,
        current_time: Time.current
      }
    end

    def self.get_transfer_code(user)
      begin
        tc = rand(100000000000..999999999999).to_s;
        tc_limit_date = DateTime.now.since(ConstantService.get('other.takeover_code_limit').days)
        unless SaveDataService.update_user_save_data_hash?(user, "transfer_code", {code: tc, limit_date: tc_limit_date})
          raise StandardError.new("failed_to_update_save_data")
        end
        user.save!
        return {
          code: tc,
          limit_date: tc_limit_date
        }
      rescue => e
        ErrorService.handle_exception(e, Api::UsersController, "get_transfer_code")
      end
    end

    def self.use_transfer_code(user, code, open_id)
      begin
        user = User.find_by(open_id: open_id)
        if user.nil?
          return {error_type: "user_not_found", error: "ユーザーが見つかりません"}
        end
        tc = user.user_save_data.dig("transfer_code", "code") || ""
        tc_limit_date = user.user_save_data.dig("transfer_code", "limit_date") || ""
        unless tc == code.delete("^0-9") # 数字以外を削除
          return {error_type: "invalid_transfer_code", error: "無効なコードです"}
        end
        unless tc_limit_date > DateTime.now
          return {error_type: "expired_transfer_code", error: "期限切れのコードです"}
        end
        pwd = SecureRandom.hex(32)
        user.password = pwd
        user.save!
        return {
          code: tc,
          password: pwd
        }
      rescue => e
        ErrorService.handle_exception(e, Api::UsersController, "use_transfer_code")
      end
    end

    # 引き継ぎコードを強制的に期限切れにする
    def self.delete_transfer_code(user)
      tc = user.user_save_data.dig("transfer_code", "code") || ""
      unless SaveDataService.update_user_save_data_hash?(user, "transfer_code", {code: tc, limit_date: DateTime.now})
        raise StandardError.new("failed_to_update_save_data")
      end
      user.save!
      return {success: true} # TODO 何をリスポンスで返したほうがいい？
    rescue => e
      ErrorService.handle_exception(e, Api::UsersController, "delete_transfer_code")
    end

    # ユーザーのデータを全て更新する
    def self.all_update(user, main_data, exp, battle_pass_pts, rewards_key, reason)
      Rails.logger.info(category: "user_service.all_update", main_data: main_data, exp: exp, battle_pass_pts: battle_pass_pts, rewards_key: rewards_key, reason: reason)
      cur_exp = user.exp || 0
      if exp.present?
        user.exp = cur_exp + exp
      end
      if main_data.present?
        user.user_settings = main_data
      end
      if rewards_key.present?
        rewards = ItemService.create_rewards_from_item_key(rewards_key)
        unless ItemService.add_rewards?(user, rewards)
          raise StandardError.new("failed_to_add_rewards")
        end
      end
      if battle_pass_pts.present?
        unless BattlePassService.add_mission_pts?(user, battle_pass_pts)
          raise StandardError.new("failed_to_add_mission_pts")
        end
      end
      user.save!
      return {user: UserService.get_res(user), rewards: rewards}
    rescue => e
      ErrorService.handle_exception(e, Api::UsersController, "all_update")
    end

    # ユーザーのメインデータを更新する
    def self.update_main_data(user, main_data)
      user.user_settings = main_data
      user.save!
      return {user: UserService.get_res(user)}
    rescue => e
      ErrorService.handle_exception(e, Api::UsersController, "update_main_data")
    end

    def self.bot_change_rate(user, rate, password)
      if password != ENV["BOT_PASSWORD"]
        raise StandardError.new("invalid_bot_password")
      end
      user.rate = rate
      user.wins = 0 # 勝利数も0にする
      user.save!
      return {user: UserService.get_res(user)}
    rescue => e
      ErrorService.handle_exception(e, Api::UsersController, "bot_change_rate")
    end

    def self.bot_change_name(user, name, password)
      if password != ENV["BOT_PASSWORD"]
        raise StandardError.new("invalid_bot_password")
      end
      user.name = name
      user.save!
      return {user: UserService.get_res(user)}
    rescue => e
      ErrorService.handle_exception(e, Api::UsersController, "bot_change_name")
    end

    # 課金履歴とアイテム数、合計金額を取得
    def self.get_purchase_history(user)
      # iOS課金データを取得
      ios_purchases = user.iap_ios.select(:product, :buy_date, :quantity)
      # Android課金データを取得  
      android_purchases = user.iap_and.select(:product, :buy_date)

      # 課金アイテムの集計用ハッシュ
      purchase_summary = Hash.new { |h, k| h[k] = { count: 0, total_price: 0 } }
      # 個別の購入履歴用配列
      purchase_details = []
      total_amount = 0

      # iOS課金データを処理
      ios_purchases.each do |purchase|
        product = purchase.product
        quantity = purchase.quantity || 1
        price = IapService::PRICE_SETTINGS[product] || 0
        
        purchase_summary[product][:count] += quantity
        purchase_summary[product][:total_price] += price * quantity
        total_amount += price * quantity

        # 個別の購入履歴に追加
        purchase_details << {
          product_key: product,
          quantity: quantity,
          unit_price: price,
          total_price: price * quantity,
          buy_date: purchase.buy_date,
          platform: 'iOS'
        }
      end

      # Android課金データを処理
      android_purchases.each do |purchase|
        product = purchase.product
        quantity = 1 # Androidは基本的に1個ずつ
        price = IapService::PRICE_SETTINGS[product] || 0
        
        purchase_summary[product][:count] += quantity
        purchase_summary[product][:total_price] += price * quantity
        total_amount += price * quantity

        # 個別の購入履歴に追加
        purchase_details << {
          product_key: product,
          quantity: quantity,
          unit_price: price,
          total_price: price * quantity,
          buy_date: purchase.buy_date,
          platform: 'Android'
        }
      end

      # 結果をわかりやすい形式で返す
      {
        purchase_items: purchase_summary.map do |product, data|
          {
            product_key: product,
            count: data[:count],
            unit_price: IapService::PRICE_SETTINGS[product] || 0,
            total_price: data[:total_price]
          }
        end,
        purchase_details: purchase_details.sort_by { |item| item[:buy_date] }.reverse, # 新しい順に並び替え
        total_purchase_amount: total_amount
      }
    end

    # 合計課金額のみを取得する簡潔なメソッド
    def self.get_total_purchase_amount(user)
      total_amount = 0

      # iOS課金データを処理
      user.iap_ios.select(:product, :quantity).each do |purchase|
        product = purchase.product
        quantity = purchase.quantity || 1
        price = IapService::PRICE_SETTINGS[product] || 0
        total_amount += price * quantity
      end

      # Android課金データを処理
      user.iap_and.select(:product).each do |purchase|
        product = purchase.product
        price = IapService::PRICE_SETTINGS[product] || 0
        total_amount += price
      end

      total_amount
    end
end
