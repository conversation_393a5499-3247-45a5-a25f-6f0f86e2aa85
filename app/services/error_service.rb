# app/services/error_service.rb
class ErrorService
    # エラーコード定義
        ERROR_CODES = {
            # 認証関連エラー (1xxxx)
            user_not_found: { code: 10001, message: "ユーザーが見つかりません" },
            invalid_credentials: { code: 10002, message: "認証情報が無効です" },
            session_expired: { code: 10003, message: "セッションの有効期限が切れています" },
            unauthorized: { code: 10004, message: "権限がありません" },
            version_not_supported: { code: 10005, message: "バージョンが古いためログインできません" },
            invalid_transfer_code: { code: 10006, message: "引き継ぎコードが無効です" },
            transfer_code_not_found: { code: 10007, message: "引き継ぎコードが見つかりません" },
            expired_transfer_code: { code: 10008, message: "引き継ぎコードが期限切れです" },

            # マスターデータ関連エラー (2xxxx)
            no_master_data: { code: 20001, message: "マスターデータが存在しません" },
            invalid_master_data: { code: 20002, message: "マスターデータが無効です" },

            # ショップ関連エラー (3xxxx)
            daily_shop_cards_not_found: { code: 30001, message: "デイリーショップのカードの取得に失敗しました" },
            failed_to_update_daily_shop_cards: { code: 30002, message: "デイリーショップのカードの更新に失敗しました" },
            failed_to_get_card_change_cards: { code: 30003, message: "カード変更のカードの取得に失敗しました" },
            failed_to_get_dia_charge: { code: 30004, message: "ダイアの購入に失敗しました" },
            failed_to_get_shop_passes: { code: 30005, message: "ショップパスの取得に失敗しました" },
            failed_to_get_supplies: { code: 30006, message: "ショップのアイテムの取得に失敗しました" },
            not_enough_ticket: { code: 30007, message: "チケットが不足しています" },
            already_bought_daily_shop_card: { code: 30008, message: "購入済みのカードです" },
            daily_shop_card_not_found: { code: 30009, message: "デイリーショップのカードが見つかりません" },
            invalid_rewards_type: { code: 30010, message: "サプライのカテゴリーを報酬のタイプと揃えてください" },
            max_daily_shop_update_count: { code: 30011, message: "デイリーショップの更新回数が最大です" },
            failed_to_update_daily_shop_update_count: { code: 30012, message: "デイリーショップの更新回数を更新できませんでした" },
            already_received_charged_rewards: { code: 30013, message: "すでに受け取り済みの報酬です" },
            not_achieved_charged_rewards: { code: 30014, message: "未達成の報酬です" },
            charged_rewards_not_found: { code: 30015, message: "報酬が見つかりません" },
            already_received_consumed_rewards: { code: 30016, message: "すでに受け取り済みの報酬です" },
            not_achieved_consumed_rewards: { code: 30017, message: "未達成の報酬です" },
            consumed_rewards_not_found: { code: 30018, message: "報酬が見つかりません" },
            failed_to_buy_bundle: { code: 30019, message: "バンドルの購入に失敗しました" },
            exceeded_max_count: { code: 30020, message: "購入回数の上限に達しています" },
            shop_bundle_not_found: { code: 30021, message: "バンドルが見つかりません" },
            invalid_bundle_id: { code: 30022, message: "バンドルIDが不正です" },
            invalid_bundle_receipt: { code: 30023, message: "バンドルのレシートが不正です" },
            already_bundle_processed: { code: 30024, message: "すでにバンドルの処理済みです" },
            invalid_bundle_product_id: { code: 30025, message: "バンドルの商品が見つかりません" },
            shop_pass_not_found: { code: 30026, message: "ショップパスが見つかりません" },
            shop_pass_already_bought: { code: 30027, message: "ショップパスはすでに購入済みです" },
            supply_not_found: { code: 30028, message: "サプライが見つかりません" },
            supply_already_owned: { code: 30029, message: "サプライはすでに持っています" },
            card_change_card_not_found: { code: 30030, message: "カード変更のカードが見つかりません" },
            pack_data_not_found: { code: 30031, message: "パックデータが見つかりません" },
            dia_charges_not_found: { code: 30032, message: "ダイヤチャージが見つかりません" },

            # パック関連エラー (4xxxx)
            pack_not_found: { code: 40001, message: "パックが見つかりません" },
            pack_get_failed: { code: 40002, message: "パックの取得に失敗しました" },
            pull_count_invalid: { code: 40003, message: "ガチャ回数は1〜10の範囲で指定してください" },
            dia_purchase_disabled: { code: 40004, message: "このパックはダイヤで購入できません" },
            ticket_purchase_disabled: { code: 40005, message: "このパックはチケットで購入できません" },
            invalid_pack_category: { code: 40006, message: "パックのカテゴリーが無効です" },
            card_buy_failed: { code: 40007, message: "カードの購入に失敗しました" },
            special_pack_not_found: { code: 40008, message: "スペシャルパックが見つかりません" },
            # ギフト関連エラー (5xxxx)
            gift_not_found: { code: 50001, message: "ギフトが見つかりません" },
            gift_get_failed: { code: 50002, message: "ギフトの取得に失敗しました" },
            gift_open_failed: { code: 50003, message: "ギフトの開封に失敗しました" },
            gift_already_opened: { code: 50004, message: "ギフトは既に開封されています" },
            item_add_failed: { code: 50005, message: "アイテムの追加に失敗しました" },
            no_openable_gifts: { code: 50006, message: "開封可能なギフトがありません" },

            # バトルパス関連エラー (6xxxx)
            battle_pass_not_found: { code: 60001, message: "バトルパスが見つかりません" },
            failed_to_get_battle_pass: { code: 60002, message: "バトルパスの取得に失敗しました" },
            failed_to_update_battle_pass: { code: 60003, message: "バトルパスの更新に失敗しました" },
            pts_not_enough: { code: 60004, message: "ポイントが不足しています" },
            reward_not_found: { code: 60005, message: "報酬が見つかりません" },
            premium_battle_pass_not_purchased: { code: 60006, message: "プレミアムバトルパスを購入してください" },
            invalid_battle_pass_category: { code: 60007, message: "バトルパスのカテゴリーが無効です" },
            failed_to_update_premium_battle_pass: { code: 60008, message: "プレミアムバトルパスの更新に失敗しました" },
            invalid_battle_pass_index: { code: 60009, message: "バトルパスのインデックスが無効です" },
            invalid_battle_pass_season: { code: 60010, message: "バトルパスのシーズンが無効です" },
            failed_to_add_mission_pts: { code: 60011, message: "バトルパスのミッションポイントの追加に失敗しました" },

            # 課金関連エラー (7xxxx)
            invalid_receipt: { code: 70001, message: "レシートが不正です" },
            already_processed: { code: 70002, message: "すでに処理済みです" },
            invalid_product_id: { code: 70003, message: "商品が見つかりません" },
            already_processed_purchase: { code: 70004, message: "既に購入済みです" },
            premium_battle_pass_already_purchased: { code: 70005, message: "プレミアムバトルパスはすでに購入済みです" },
            failed_to_verify_notification: { code: 70006, message: "通知の署名検証に失敗しました" },
            failed_to_verify_transaction_info: { code: 70007, message: "取引情報の署名検証に失敗しました" },
            invalid_subscription: { code: 70008, message: "無効なサブスクリプションです" },
            already_processed_subscription: { code: 70009, message: "すでに処理済みのサブスクリプションです" },
            invalid_purchase: { code: 70010, message: "無効な購入です" },
            invalid_transaction: { code: 70011, message: "無効なトランザクションです" },

            # ログインボーナス関連エラー (8xxxx)
            failed_to_update_login_bonus: { code: 80001, message: "ログインボーナスの更新に失敗しました" },
            invalid_login_bonus_category: { code: 80002, message: "無効なログインボーナスのカテゴリーです" },
            invalid_login_bonus_index: { code: 80003, message: "無効なログインボーナスのインデックスです" },
            invalid_login_bonus_season: { code: 80004, message: "無効なログインボーナスのシーズンです" },
            login_bonus_not_found: { code: 80005, message: "ログインボーナスが見つかりません" },
            login_bonus_not_available: { code: 80006, message: "ログインボーナスは深夜4時以降に受け取れます" },
            received_all_login_bonus: { code: 80007, message: "ログインボーナスは最後まで受け取りました" },

            # その他のエラー (9xxxx)
            unknown_error: { code: 90001, message: "不明なエラーが発生しました" },
            failed_to_add_rewards: { code: 90002, message: "報酬の追加に失敗しました" },
            invalid_parameters: { code: 90003, message: "パラメータが無効です" },
            database_error: { code: 90004, message: "データベースエラーが発生しました" },
            failed_to_update_item: { code: 90005, message: "アイテム更新に失敗しました" },
            failed_to_update_save_data: { code: 90006, message: "ログインボーナスの保存に失敗しました" },
            not_enough_dia: { code: 90007, message: "ダイヤが不足しています" },
            failed_to_update_user_save_data: { code: 90008, message: "ユーザーの保存データの更新に失敗しました" },
            failed_to_add_user_save_data: { code: 90009, message: "ユーザーの保存データの追加に失敗しました" },
            unknown_reward_format: { code: 90010, message: "未知の報酬形式です" },
            os_type_is_nil: { code: 90011, message: "OSタイプが指定されていません" },

            # ホーム画面関連エラー (10xxxx)
            failed_to_get_chests: { code: 100001, message: "宝箱の取得に失敗しました" },
            failed_to_get_banners: { code: 100002, message: "バナーの取得に失敗しました" },

            # 宝箱関連エラー (11xxxx)
            failed_to_update_user_chests: { code: 110001, message: "宝箱の更新に失敗しました" },
            invalid_chest_category: { code: 110002, message: "無効な宝箱のカテゴリーです" },
            chest_not_found: { code: 110003, message: "宝箱が見つかりません" },
            chest_already_opened: { code: 110004, message: "宝箱は既に開封されています" },
            chest_not_achieved: { code: 110005, message: "宝箱は未達成です" },
            max_chests: { code: 110006, message: "宝箱の最大個数に達しています" },

            # デッキ関連エラー (12xxxx)
            failed_to_save_deck: { code: 120001, message: "デッキの保存に失敗しました" },
            failed_to_get_deck: { code: 120002, message: "デッキの取得に失敗しました" },
            invalid_deck_data: { code: 120003, message: "デッキのデータが無効です" },
            deck_not_found: { code: 120004, message: "デッキが見つかりません" },

            # プロフィール関連エラー (13xxxx)
            failed_to_update_profile: { code: 130001, message: "プロフィールの更新に失敗しました" },
            not_enough_badge: { code: 130002, message: "指定したバッジはありません" },
            invalid_badge: { code: 130003, message: "バッジが無効です" },
            not_enough_title: { code: 130004, message: "指定したタイトルはありません" },
            invalid_title: { code: 130005, message: "タイトルが無効です" },
            not_enough_icon: { code: 130006, message: "指定したアイコンはありません" },
            invalid_icon: { code: 130007, message: "アイコンが無効です" },
            not_enough_icon_frame: { code: 130008, message: "指定したアイコンフレームはありません" },
            invalid_icon_frame: { code: 130009, message: "アイコンフレームが無効です" },
            not_enough_card_id: { code: 130010, message: "指定したカードIDはありません" },
            not_enough_card_edition: { code: 130011, message: "指定したカードのエディションはありません" },
            invalid_card_collection: { code: 130012, message: "カードコレクションが無効です" },
            invalid_profile_settings: { code: 130013, message: "プロフィールが無効なデータ形式です" },
            invalid_item_type: { code: 130014, message: "アイテムの種類が無効です" },
            not_enough_pts: { code: 130015, message: "ポイントが不足しています" },
            failed_to_find_other_user: {code: 130016, message: "ユーザーが見つかりません"},
            failed_to_update_profile_name: {code: 130017, message: "プロフィールの名前の更新に失敗しました"},

            # スキン関連エラー (14xxxx)
            skin_not_found: { code: 140001, message: "スキンが見つかりません" },
            skin_max_level: { code: 140002, message: "スキンのレベルが最大です" },
            not_enough_sands: { code: 140003, message: "スキンの砂が不足しています" },

            # お知らせ関連エラー (16xxxx)
            info_not_found: { code: 160001, message: "お知らせが見つかりません" },

            # バトルレコード関連エラー (17xxxx)
            no_battle_records: { code: 170001, message: "バトル履歴がありません" },
        }
        # APIレスポンス用のエラー情報を生成
        # @param [Symbol or String] error_type エラータイプ
        # @param [String] custom_message カスタムメッセージ（オプション）
        # @param [Hash] metadata 追加メタデータ（オプション）
        # @return [Hash] エラー情報
        def self.api_error(error_type, metadata = {})
            error_info = ERROR_CODES[error_type.to_sym] || ERROR_CODES[:unknown_error]
            error_code = error_info[:code]
            error_message = error_info[:message]

            if error_code == 90001
                response = {
                    error_type: "unknown_error",
                    error: error_message,
                    error_code: error_code
                }
            else
                response = {
                    error_type: error_type,
                    error: error_message,
                    error_code: error_code
                }
            end

            # 追加メタデータがあれば追加
            response.merge!(metadata) if metadata.present?
            response
        end

        # コントローラ用のログ記録とエラーレスポンス生成
        # @param [Exception] exception 例外オブジェクト
        # @param [ActionController::Base] controller コントローラインスタンス
        # @param [String] action アクション名
        # @return [Hash] エラー情報
        def self.handle_exception(exception, controller, action)
            # エラーログ記録
            Rails.logger.error("#{controller}##{action} エラー: #{exception.message}")
            Rails.logger.error(exception.backtrace.join("\n"))
            # APIエラーレスポンスを生成
            api_error(exception.message)
        end

        # バリデーションエラーハンドリング
        # @param [ActiveRecord::Base] record バリデーションエラーを含むレコード
        # @return [Hash] エラーレスポンス
        def self.handle_validation_errors(record)
            error_messages = record.errors.full_messages.join(", ")
            api_error(:invalid_parameter, error_messages, { validation_errors: record.errors.to_hash })
        end
    end