class GiftService
  extend ProcessFormatRewards
  # ユーザーに対して新しいギフトを作成する
  def self.check_and_create_gifts(user)
    existing = Gift.where(user_id: user.open_id).select(:master_id)
    existing_master_ids = existing.map(&:master_id)

    # 通常のwhereクエリでは動的なperiod_end_atメソッドを使えないため、
    # いったんすべて取得してからフィルタリングする
    all_masters = GiftMaster.within_valid_period
    new_masters = all_masters.select do |master|
      (master.user_id.nil? || master.user_id == '' || master.user_id == user.open_id) &&
      !existing_master_ids.include?(master.uid)
    end

    new_masters.each do |master|
      gift = Gift.create!(
        uid:      generate_gift_uid,
        user_id:  user.open_id,
        rewards:  master.rewards,
        name:     master.name,
        desc:     master.desc,
        state:    0,
        end_at:   master.period_end_at,
        master_id:  master.uid,
        open_at: nil
      )
    end
  end

  def self.get_gifts(user,user_lang)
    begin
      not_received = user.gifts.where(state: 0).order(:uid)
      received = user.gifts.where(state: 1).order(:uid)
      return {gifts: {
        not_received: not_received.map{|gift| format_json(gift,user_lang)},
        received: received.map{|gift| format_json(gift,user_lang)}
      }}
    rescue => e
      ErrorService.handle_exception(e, Api::GiftsController, "get_gifts")
    end
  end

  # ギフトを開封する
  def self.open_gift(user, gift_uid,user_lang)
    begin
      gift = Gift.find_by(uid: gift_uid, user_id: user.open_id)
      if gift.nil?
        raise StandardError.new("gift_not_found")
      end
      if gift.state == 1
        raise StandardError.new("gift_already_opened")
      end
      rewards = gift.rewards
      unless ItemService.add_rewards?(user,rewards)
        raise StandardError.new("item_add_failed")
      end

      gift.state = 1 # ギフトを開封を表す
      gift.open_at = Time.now
      ActiveRecord::Base.transaction do
        gift.save!
        user.save!
      end
      gifts = get_gifts(user,user_lang)
      if gifts[:error].present?
        raise StandardError.new("gift_get_failed")
      end
      return {gifts: gifts[:gifts],rewards:rewards,user: UserService.get_res(user)}
    rescue => e
      ErrorService.handle_exception(e, Api::GiftsController, "open_gift")
    end
  end


  # すべてのギフトを開封する
  def self.open_all_gifts(user,user_lang)
    begin
      available_gifts = Gift.where(user_id: user.open_id,state: 0)
      if available_gifts.empty?
        raise StandardError.new("no_openable_gifts")
      end
      opened_gift_rewards = []
      available_gifts.each do |gift|
        opened_gift_rewards += gift.rewards # 開封したギフトを配列に追加
      end
      unless ItemService.add_rewards?(user,opened_gift_rewards)
          raise StandardError.new("item_add_failed")
        end
      ActiveRecord::Base.transaction do
        available_gifts.update_all(state: 1,open_at: Time.now)
        user.save!
      end
      gifts = get_gifts(user,user_lang)
      if gifts[:error].present?
          raise StandardError.new("gift_get_failed")
        end
        return {gifts: gifts[:gifts],rewards: opened_gift_rewards,user: UserService.get_res(user)}
    rescue => e
      ErrorService.handle_exception(e, Api::GiftsController, "open_all_gifts")
    end
  end

  private
  def self.generate_gift_uid
    if Gift.select(:uid).present?
      Gift.select(:uid).order(:uid).last.uid + 1
    else
      1
    end
  end

  def self.format_json(gift,user_lang)
    name_value = gift.name.is_a?(Hash) && gift.name[user_lang] ? gift.name[user_lang] : "不明なギフト"
    desc_value = gift.desc.is_a?(Hash) && gift.desc[user_lang] ? gift.desc[user_lang] : "詳細不明"
    rewards_value = process_format_rewards(gift.rewards) || []
    {
        uid: gift.uid,
        name: name_value,
        desc: desc_value,
        end_at: gift.end_at,
        open_at: gift.open_at,
        rewards: rewards_value,
        created_at: gift.created_at,
    }
  end
end