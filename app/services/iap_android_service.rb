require 'google/apis/androidpublisher_v3'
require 'googleauth'
require 'base64'

class IapAndroidService
    def self.iap_android(user, params)
        ActiveRecord::Base.transaction do
            Rails.logger.info(category: "iap_android_service.iap_android", params: params)
            # レシート → 購入情報を抽出
            outer = JSON.parse(params[:receipt_data])
            payload_str = outer["Payload"]
            payload = JSON.parse(payload_str)
            purchase = JSON.parse(payload["json"])
            Rails.logger.info(category: "iap_android_service.iap_android", purchase: purchase)
            package_name = purchase["packageName"]
            product_id = purchase["productId"]
            purchase_token = purchase["purchaseToken"]
            order_id = purchase["orderId"]
            Rails.logger.info(category: "iap_android_service.iap_android", package_name: package_name, product_id: product_id, purchase_token: purchase_token, order_id: order_id)

            # Google認証セットアップ
            google_service = GooglePlayService.new
            # 継続型の購入の正当性を確認
            if product_id == "pack_pass" || product_id == "analysis_pass"
                # サブスクリプションの場合
                response = google_service.verify_purchase_subscription(product_id, purchase_token,package_name)
                Rails.logger.info(category: "iap_android_service.iap_android", subscription_response: response)
                buy_date = Time.zone.at(response.start_time_millis.to_i / 1000)
                expires_date = Time.zone.at(response.expiry_time_millis.to_i / 1000)
                auto_renew = true

                # サブスク状態チェック
                if response.cancel_reason.present? || response.payment_state != 1 # 1: 支払い完了
                    Rails.logger.warn("無効なサブスクリプション: #{order_id}")
                    raise StandardError.new("invalid_subscription")
                end

                # 二重登録チェック(サブスクの場合はorder_idに変更がない)
                if IapAnd.exists?(buy_token: purchase_token)
                    raise StandardError.new("already_processed_subscription")
                end

                result = IapService.subscription_action(user,product_id,expires_date)
            else
                # 消費型の購入の正当性を確認
                response = google_service.verify_purchase_product(product_id, purchase_token,package_name)
                unless response
                    Rails.logger.warn("製品購入検証失敗: #{order_id}")
                    raise StandardError.new("invalid_purchase")
                end

                purchase_state = response.purchase_state
                buy_date = Time.zone.at(response.purchase_time_millis.to_i / 1000)
                expires_date = ""
                auto_renew = false

                Rails.logger.info(category: "iap_android_service.iap_android", response: response)
                if purchase_state == 1 # 0: 購入完了, 1: 払い戻し, 2: キャンセル
                    Rails.logger.warn("無効な購入（キャンセル/払い戻し）: #{order_id}")
                    raise StandardError.new("invalid_purchase")
                end

                # 二重購入防止
                if IapAnd.exists?(order_id: order_id)
                    raise StandardError.new("already_processed_purchase")
                end

                # 必要に応じて購入を確認
                if response.acknowledgement_state == 0
                    google_service.acknowledge_product_purchase(product_id, purchase_token, "user_id: #{user.id}",package_name)
                end

                result = IapService.bought_process(user, product_id, "android")
            end
            # 登録
            iap_data = IapAnd.new(
                product: product_id,
                buy_date: buy_date,
                expires_date: expires_date,
                auto_renew: auto_renew,
                buy_token: purchase_token,
                order_id: order_id,
                open_id: user.open_id
            )
            Rails.logger.info(category: "iap_android_service.iap_android", iap_data: iap_data)
            ActiveRecord::Base.transaction do
                user.save!
                iap_data.save!
            end
            result
        end
    rescue => e
        ErrorService.handle_exception(e, "IapAndroidService", "iap_android")
    end

    def self.handle_rtdn(params)
        message_data = JSON.parse(Base64.decode64(params["message"]["data"]))
        package_name = message_data["packageName"]
        if message_data["testNotification"].present?
            return { success: true, result: "テスト用の通知です" }
        elsif message_data['subscriptionNotification'].present?
            # サブスクリプションの通知
            notification = message_data['subscriptionNotification']
            subscription_id = notification['subscriptionId']
            purchase_token = notification['purchaseToken']
            user = find_user_by_purchase_token(purchase_token)
            notification_type = notification['notificationType']
            Rails.logger.info(category: "iap_android_service.handle_rtdn", notification_type: notification_type)
            # TODO:notification_typeのタイプによっていろいろ処理する。
            # TODO:返金された場合はアイテムをそのままマイナスにする(マイナスになってもいい)

            Rails.logger.info("RTDN: #{notification_type} - #{subscription_id} - #{purchase_token}")
            # Google認証セットアップ
            google_service = GooglePlayService.new

            case notification_type
            when 2 # アクティブな定期購入が更新された。
                ActiveRecord::Base.transaction do
                    response = google_service.verify_purchase_subscription(subscription_id, purchase_token,package_name)

                    unless response
                        Rails.logger.warn("サブスクリプション検証失敗: #{purchase_token}")
                        return { error: true, message: "サブスクリプション検証失敗" }
                    end

                    Rails.logger.info(category: "iap_android_service.handle_rtdn", subscription_response: response)
                    buy_date = Time.zone.at(response.start_time_millis.to_i / 1000)
                    expires_date = Time.zone.at(response.expiry_time_millis.to_i / 1000)
                    order_id = response.order_id
                    auto_renew = response.auto_renewing

                    # サブスク状態チェック
                    if response.cancel_reason.present? || response.payment_state != 1 # 1: 支払い完了
                        Rails.logger.warn("無効なサブスクリプション: #{purchase_token}")
                        return { error: true, message: "無効なサブスクリプション" }
                    end

                    # ユーザーを検索
                    user = find_user_by_purchase_token(purchase_token)

                    unless user
                        Rails.logger.warn("ユーザーが見つかりません: #{purchase_token}")
                        return { error: true, message: "ユーザーが見つかりません" }
                    end

                    # 新しいIapAndレコードを作成
                    iap_data = IapAnd.new(
                        product: subscription_id,
                        buy_date: buy_date,
                        buy_token: purchase_token,
                        order_id: order_id,
                        open_id: user.open_id,
                        expires_date: expires_date,
                        auto_renew: auto_renew || false,
                        notification_type: notification_type.to_s
                    )

                    iap_data.save!
                    user.save!
                    # サブスクリプションの更新処理
                    result = IapService.subscription_action(user, subscription_id, expires_date)
                end

            when 3 # 定期購入がキャンセルされた
                ActiveRecord::Base.transaction do
                    Rails.logger.info(category: "iap_android_service.handle_rtdn", notification_type: notification_type)

                    # ユーザーを検索
                    user = find_user_by_purchase_token(purchase_token)

                    if user
                        # 既存のIapAndレコードを更新
                        iap_data = IapAnd.find_by(buy_token: purchase_token)
                        ci = ChargingItem.find_by(product_id: iap_data.product)
                        Rails.logger.info(category: "iap_android_service.handle_rtdn", ci: ci)

                        if iap_data
                            iap_data.update(
                                auto_renew: false,
                                notification_type: notification_type.to_s
                            )
                        end

                        # ユーザーのサブスクリプション状態を更新
                        pass_product_keys = ConstantService::PASS_PRODUCT_KEYS rescue ["pack_pass", "analysis_pass"]
                        analysis_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[1]) || {}
                        pack_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[0]) || {}

                        if subscription_id == pass_product_keys[0] # pack_pass
                            SaveDataService.update_user_save_data_hash?(user, "shop", {
                                passes: {
                                    pass_product_keys[0] => {is_active: pack_pass_info["is_active"] || false, expires_date: pack_pass_info["expires_date"] || "",auto_renew: false},
                                    pass_product_keys[1] => {is_active: analysis_pass_info["is_active"] || false, expires_date: analysis_pass_info["expires_date"] || "",auto_renew: analysis_pass_info["auto_renew"] || false}
                                }
                            })
                        elsif subscription_id == pass_product_keys[1] # analysis_pass
                            SaveDataService.update_user_save_data_hash?(user, "shop", {
                                passes: {
                                    pass_product_keys[0] => {is_active: pack_pass_info["is_active"] || false, expires_date: pack_pass_info["expires_date"] || "",auto_renew: pack_pass_info["auto_renew"] || false},
                                    pass_product_keys[1] => {is_active: analysis_pass_info["is_active"] || false, expires_date: analysis_pass_info["expires_date"] || "",auto_renew: false}
                                }
                            })
                        end

                        user.save!
                    end
                end
            when 19 # 定期購入が期限切れになった
                Rails.logger.info(category: "iap_android_service.handle_rtdn", notification_type: notification_type)
                ShopPassService.check_pass_active(user)
                user.save!
            else
                Rails.logger.info(category: "iap_android_service.handle_rtdn", notification_type: notification_type)
                Rails.logger.warn("未対応な通知タイプ: #{notification_type}")
            end
        elsif message_data['oneTimeProductNotification'].present?
            # 1回限りの購入の通知
            notification = message_data['oneTimeProductNotification']
            Rails.logger.info(category: "iap_android_service.handle_rtdn", notification: notification)
            case notification['notificationType']
            when 1 # 購入が確認された
                purchase_token = notification['purchaseToken']
                Rails.logger.info(category: "iap_android_service.handle_rtdn", purchase_token: purchase_token)
            when 4 # 購入が確認されなかった
                Rails.logger.warn("購入が確認されなかった: #{notification['purchaseToken']}")
            else
                Rails.logger.warn("未対応な通知タイプ: #{notification['notificationType']}")
            end
        elsif message_data['voidedPurchaseNotification'].present?
            # 返金された購入の通知
            notification = message_data['voidedPurchaseNotification']
            Rails.logger.info(category: "iap_android_service.handle_rtdn", notification: notification)
            purchase_token = notification['purchaseToken']
            case notification['productType']
            when "2" #  1 回だけの購入が取り消されました。
                # 返金された購入の処理
                iap_data = IapAnd.find_by(buy_token: purchase_token)
                ci = ChargingItem.find_by(product_id: iap_data.product)
                Rails.logger.info(category: "iap_android_service.handle_rtdn", ci: ci)

                if iap_data
                    # 返金フラグを設定
                    iap_data.update(
                        is_refunded: true,
                        refund_date: Time.zone.now,
                        notification_type: "REFUND"
                    )

                    # 必要に応じてユーザーの状態を更新
                    user = User.find_by(open_id: iap_data.open_id)

                    if user
                        ci = ChargingItem.find_by(android_key: iap_data.product)
                        Rails.logger.info(category: "iap_android_service.handle_rtdn", ci: ci)

                        # 返金処理（例：ダイヤを減らすなど）
                        if ci && ci.category == "dia"
                            # TODO: 返金処理を実装
                        end
                    end
                end
            else
                Rails.logger.warn("未対応な通知タイプ: #{notification['productType']}")
            end
        end

        return { success: true, result: response }
    rescue => e
        ErrorService.handle_exception(e, "GoogleNotificationsController", "rtdn")
        return { error: true, message: e.message }
    end

    def self.find_user_by_purchase_token(purchase_token)
        iap = IapAnd.find_by(buy_token: purchase_token)
        return nil unless iap
        User.find_by(open_id: iap.open_id)
    end
end
