class CardStatsService
  def self.process_deck_data(deck_data, result, rank, game_mode)
    begin
      # JSONデータのパースを試みる
      deck_parsed = JSON.parse(deck_data)
      
      # バリデーション1: データ構造のチェック
      unless deck_parsed.is_a?(Hash) && deck_parsed.key?('cd')
        Rails.logger.error "Invalid deck format: #{deck_data}"
        return {}
      end

      # バリデーション2: カードID文字列の形式チェック
      cards_str = deck_parsed['cd']
      unless cards_str.is_a?(String) && cards_str.match?(/^\d{8}(,\d{8})*$/)
        Rails.logger.error "Invalid cards format: #{cards_str}"
        return {}
      end

      status_data = {}

      # バリデーションを通過したデータを処理
      cards = cards_str.split(',')
      cards.each do |card|
        # 最初の5桁を取得
        card_id = card[0..4].to_i

        # 8桁目を通常カードの枚数を取得
        nr_card_count = card[7].to_i

        # 7桁目をシャインカードの枚数を取得
        sh_card_count = card[6].to_i

        # 6桁目をプレミアムカードの枚数を取得
        pr_card_count = card[5].to_i

        # 通常カード、シャインカード、プレミアムカードの合計枚数を取得
        total_used = nr_card_count + sh_card_count + pr_card_count

        # 複合キーを作成（カードID、ランク、ゲームモード）
        composite_key = {
          card_id: card_id,
          rank: rank,
          game_mode: game_mode
        }

        # status_dataにカードが無ければ新規追加、あれば加算
        card_status = status_data[composite_key]

        if card_status.nil?
          status_data[composite_key] = {
            nr: 0,
            sh: 0,
            pr: 0,
            nr_total: 0,
            sh_total: 0,
            pr_total: 0,
            total_used: 0,
            use_count: 0,
            win_count: 0,
            lose_count: 0,
            draw_count: 0,
            use_1_count: 0,
            use_2_count: 0,
            use_3_count: 0,
            use_over_3_count: 0,
          }
          card_status = status_data[composite_key]
        end
        
        card_status[:nr] += [nr_card_count, 1].min
        card_status[:sh] += [sh_card_count, 1].min
        card_status[:pr] += [pr_card_count, 1].min
        card_status[:nr_total] += nr_card_count
        card_status[:sh_total] += sh_card_count
        card_status[:pr_total] += pr_card_count
        card_status[:total_used] += total_used
        card_status[:use_count] += [total_used, 1].min
        card_status[:win_count] += 1 if result == 'win'
        card_status[:lose_count] += 1 if result == 'lose'
        card_status[:draw_count] += 1 if result == 'draw'
        if total_used == 1
          card_status[:use_1_count] += 1
        elsif total_used == 2
          card_status[:use_2_count] += 1
        elsif total_used == 3
          card_status[:use_3_count] += 1
        else
          card_status[:use_over_3_count] += 1
        end
      end

      status_data

    rescue JSON::ParserError => e
      Rails.logger.error "[Line #{__LINE__}] JSON parse error: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
      {}
    rescue => e
      Rails.logger.error "[Line #{__LINE__}] Unexpected error: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
      {}
    end
  end

  def self.create_daily_status
    pp "create_daily_status"
    # 今日の日付のmatchesを取得
    matches = Match.where(created_at: Date.today.all_day)

    result = {}

    # 使用したカードごとにデータを作成
    matches.each do |match|
      
      # 勝敗を管理
      player_1_result = "draw"
      player_2_result = "draw"
      if match.result == "win_1"
        player_1_result = "win"
        player_2_result = "lose"
      elsif match.result == "win_2"
        player_1_result = "lose"
        player_2_result = "win"
      end

      # ランクとゲームモードを取得
      rank = match.rank || 'unknown'
      game_mode = match.game_mode || 'unknown'

      # deck_1のデータを処理
      deck_1_status = process_deck_data(match.deck_1, player_1_result, rank, game_mode)
      pp "deck_1 status: #{deck_1_status}"

      # deck_2のデータを処理
      deck_2_status = process_deck_data(match.deck_2, player_2_result, rank, game_mode)
      pp "deck_2 status: #{deck_2_status}"

      # 結果をマージする
      merge_status_data(result, deck_1_status)
      merge_status_data(result, deck_2_status)
    end
    pp "--------------------------------"
    pp "result: #{result}"

    # データベースに保存 (一括でSQLを発行して効率化)
    card_records = result.map do |composite_key, status|
      {
        card_id: composite_key[:card_id],
        rank: composite_key[:rank],
        game_mode: composite_key[:game_mode],
        total_used: status[:total_used],
        use_count: status[:use_count],
        use_1_count: status[:use_1_count],
        use_2_count: status[:use_2_count],
        use_3_count: status[:use_3_count],
        use_over_3_count: status[:use_over_3_count],
        win_count: status[:win_count],
        lose_count: status[:lose_count],
        draw_count: status[:draw_count],
        created_at: Time.current,
        updated_at: Time.current,
        date: Date.today
      }
    end

    # 一括で挿入（一回のSQLクエリで処理）
    CardStatsDaily.insert_all(card_records) if card_records.present?

  end

  private

  def self.merge_status_data(target, source)
    source.each do |composite_key, status|
      if target[composite_key].nil?
        target[composite_key] = status
      else
        target_status = target[composite_key]
        
        # 各フィールドを加算
        status.each do |key, value|
          target_status[key] += value
        end
      end
    end
    
    target
  end
end 