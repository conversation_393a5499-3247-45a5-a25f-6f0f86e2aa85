class BannerService
    def self.fetch_and_update_user_banners(host:, port:)
        banners = Banner.within_valid_period
        banner_list = []
        banners.each do |banner|
            banner_list << {
                banner_image_url: banner.banner_image_url(host: host, port: port),
                title: banner.title,
                content: {
                    view_type: banner.view_type,
                    uid: banner.view_uid
                },
            }
        end
        return banner_list
    end
end