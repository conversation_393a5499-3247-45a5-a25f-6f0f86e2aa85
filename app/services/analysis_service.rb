class AnalysisService

    SEGMENT_DATA = {
        1 => { text: "無課金", html: "<span class='badge bg-light'>無課金</span>"},
        2 => { text: "微課金", html: "<span class='badge bg-info'>微課金</span>"},
        3 => { text: "中課金", html: "<span class='badge bg-warning'>中課金</span>"},
        4 => { text: "重課金", html: "<span class='badge bg-danger'>重課金</span>"},
        5 => { text: "超重課金", html: "<span class='badge bg-dark'>超重課金</span>"},
    }
    
    def self.get_segment_data(cost)
        index = get_segment_data_index(cost)
        return SEGMENT_DATA[index]
    end

    def self.get_segment_data_index(cost)
        index = 1
        (1..5).each do |i|
        segment_value = ConstantService.get("analysis.user_segmentation.user_segmentation_#{i}")
        if segment_value <= cost
            index = i
        else
            break
        end
        end
        return index
    end
end