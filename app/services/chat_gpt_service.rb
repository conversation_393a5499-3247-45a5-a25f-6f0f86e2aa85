class ChatGptService
    require 'openai'
  
    def initialize
      @openai = OpenAI::Client.new(access_token: "********************************************************************************************************************************************************************")
    end
  
    def chat(prompt, length = 200)
      response = @openai.chat(
        parameters: {
          model: "gpt-4", # Required. # 使用するGPT-3のエンジンを指定
          messages: [{ role: "system", content: "You are a helpful assistant. response to japanese" }, { role: "user", content: prompt }],
          temperature: 0.7, # 応答のランダム性を指定
          max_tokens: length,  # 応答の長さを指定
        },
        )
      response['choices'].first['message']['content']
    end
  end
  