class DiaService

    def self.fetch_current_dia(user,os)
        @free_dia = user.free_dia || 0
        case os
        when "ios"
            @ios_dia = user.ios_dia || 0
            return @free_dia + @ios_dia
        when "android"
            @android_dia = user.android_dia || 0
            return @free_dia + @android_dia
        when "steam"
            @steam_dia = user.steam_dia || 0
            return @free_dia + @steam_dia
        else
            return @free_dia
        end

    end

    def self.add_free_dia?(user, dia)
        user.free_dia += dia
        return true
    end

    def self.charge_dia?(user, os, dia)
        case os
        when "ios"
            user.ios_dia += dia
        when "android"
            user.android_dia += dia
        when "steam"
            user.steam_dia += dia
        else
            return false
        end
        cur_charged_dia = user.user_save_data.dig("shop","bonus","total_charged_dia") || 0
        bonus = user.user_save_data.dig("shop","bonus") || {}
        bonus["total_charged_dia"] = cur_charged_dia + dia
        unless SaveDataService.update_user_save_data_hash?(user,"shop",{
            bonus: bonus
        })
            return false
        end
        return true
    end

    def self.consume_dia?(user, os, dia)
        free_dia = user.free_dia || 0
        charged_dia = case os
            when "ios" then user.ios_dia || 0
            when "android" then user.android_dia || 0
            when "steam" then user.steam_dia || 0
            else 0
            end
        if free_dia + charged_dia < dia
            return false
        end
        if free_dia >= dia
            user.free_dia -= dia
        else
            user.free_dia = 0
            extra_need = dia - free_dia
        case os
            when "ios"
                user.ios_dia -= extra_need
            when "android"
                user.android_dia -= extra_need
            when "steam"
                user.steam_dia -= extra_need
            end
        end
        cur_consumed_dia = user.user_save_data.dig("shop","bonus","total_consumed_dia") || 0
        bonus = user.user_save_data.dig("shop","bonus") || {}
        bonus["total_consumed_dia"] = cur_consumed_dia + dia
        unless SaveDataService.update_user_save_data_hash?(user,"shop",{
            bonus: bonus
        })
            return false
        end
        return true
    end
end