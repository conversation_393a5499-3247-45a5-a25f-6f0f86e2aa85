class DeckService

    # デッキ保存 (直接保存するだけ)
    def self.save_deck(user, deck)
        begin
            unless SaveDataService.update_user_save_data_hash?(user, "decks", {deck_data: deck})
                raise StandardError.new("failed_to_save_deck")
            end
            user.save!
            return {deck: deck}
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::DecksController, "save_deck")
        end
    end

    # デッキ取得
    def self.get_deck(user)
        begin
            deck = user.user_save_data.dig("decks","deck_data")
            return {deck: deck}
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::DecksController, "get_deck")
        end
    end
end