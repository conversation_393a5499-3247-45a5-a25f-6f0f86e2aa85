class ItemService
    DATA_TEMPLATES = {
        "badges" => [],
        "titles" => [],
        "icons" => [],
        "icon_frames" => [],
        "pack_tickets" => {}, # pack_idをキーとしてcountを管理する
        "passes" => [{"pass_id" => 0, "start_at" => "", "end_at" => "","category" => ""}],
        "card_sleeves" => [],
        "playmats" => [],
        "bgms" => [],
        "chests" => [],
        "prebuilt_decks" => [],
        "skin_sands" => {}, # group_idをキーとしてcountを管理する
        "card_sands" => {}, # item_idをキーとしてcountを管理する
    }

    def self.init_item_data()
        {
            "badges" => [],
            "titles" => [1],
            "icons" => [1],
            "icon_frames" => [1],
            "pack_tickets" => {},
            "passes" => [],
            "card_sleeves" => [],
            "playmats" => [],
            "bgms" => [],
            "chests" => [],
            "prebuilt_decks" => [],
            "skin_sands" => {},
            "card_sands" => {}
        }
    end

    def self.add_rewards?(user,rewards,os_type=nil)
        has_items = user.has_items || {}
        rewards.each do |reward|
            item_type = reward["item_type"] || reward[:item_type]
            item_id = reward["item_id"] || reward[:item_id]
            count = reward["count"] || reward[:count]
            ext = reward["ext"] || reward[:ext]
            case item_type
            when "dia" # free_dia限定
                unless DiaService.add_free_dia?(user, count)
                    raise StandardError.new("failed_to_add_free_dia")
                end
            when "paid_dia"
                if os_type.nil? || os_type.empty?
                    raise StandardError.new("os_type_is_nil")
                end
                unless DiaService.charge_dia?(user,os_type,count)
                    raise StandardError.new("failed_to_charge_dia")
                end
            # 種類のみ必要なアイテム
            when "icon", "icon_frame", "card_sleeve", "title","playmat","bgm","chest","prebuilt_deck"
                current_data = has_items[item_type.pluralize] || []
                current_data << item_id
                # IDの重複を削除
                has_items[item_type.pluralize] = current_data.compact.uniq.sort
                user.has_items = has_items
            # 種類と個数が必要な中でもitem_idをキーとして保存している
            when "skin_sand", "card_sand", "pack_ticket"
                item_id = item_id.to_s # IDを文字列にしておく
                current_data = has_items[item_type.pluralize] || {}
                if current_data[item_id].present?
                    current_data[item_id]["count"] = (current_data[item_id]["count"] || 0) + count
                else
                    current_data[item_id] = {"count" => count}
                end
                has_items[item_type.pluralize] = current_data
                user.has_items = has_items
            when "nr_card"
                edition_type = 1
                BoxService.add_card_to_box(user, item_id, edition_type, count)
            when "sh_card"
                edition_type = 2
                BoxService.add_card_to_box(user, item_id, edition_type, count)
            when "pr_card"
                edition_type = 3
                BoxService.add_card_to_box(user, item_id, edition_type, count)
            when "special_pack"
                # rewardを破壊的に変更
                # special_packはitem_idが0固定
                item_id = 0
                reward["ext"] = PackService.pull_special_pack(user,os_type,item_id,count)
            when "main_pack_ticket"
                item_id = PackService.fetch_current_main_pack&.uid || 0
                reward["item_type"] = "pack_ticket"
                reward["item_id"] = item_id # 破壊的変更
                item_id = item_id.to_s # IDを文字列にしておく
                current_data = has_items["pack_tickets"] || {}
                if current_data[item_id].present?
                    current_data[item_id]["count"] = (current_data[item_id]["count"] || 0) + count
                else
                    current_data[item_id] = {"count" => count}
                end
                has_items["pack_tickets"] = current_data
                user.has_items = has_items
            when "none"
                # 何もしない
                next
            when "pack_point"
                user_pack_info = PackService.get_user_pack_info(user,item_id)
                pack = Pack.find_by(uid: item_id) # 過去に存在したパックの場合は取得できる
                if pack.nil?
                    raise StandardError.new("pack_not_found")
                end
                user_pack_info["pts"] += count
                unless SaveDataService.add_user_save_data?(user, "pack", {
                    pack_id: item_id, pts: user_pack_info["pts"], count: user_pack_info["count"]
                })
                    raise StandardError.new("failed_to_add_user_save_data")
                end
            else
                raise StandardError.new("invalid_item_type")
            end
        end
        return true
    end


    def self.get_item_data(user)
        has_items = user.has_items || {}
        return {
            badges: has_items["badges"] || [],
            titles: has_items["titles"] || [],
            icons: has_items["icons"] || [],
            icon_frames: has_items["icon_frames"] || [],
            pack_tickets: pack_tickets_to_array(has_items["pack_tickets"] || []),
            passes: has_items["passes"] || [],
            card_sleeves: has_items["card_sleeves"] || [],
            playmats: has_items["playmats"] || [],
            bgms: has_items["bgms"] || [],
            chests: has_items["chests"] || [],
            prebuilt_decks: has_items["prebuilt_decks"] || [],
            skin_sands: skin_sands_to_array(has_items["skin_sands"] || []),
            card_sands: card_sands_to_array(has_items["card_sands"] || []),
            skins: SkinService.get_skins_json(user),
            packs: user.user_save_data["pack"] || [], # save_dataだがクライアントに常に返したいからここに書く
            main_pack_tickets: has_items["main_pack_tickets"] || 0,
        }
    end

    def self.consume_pack_ticket?(user,pack_id,count)
        pack_tickets = user.has_items["pack_tickets"] || {}
        if pack_tickets[pack_id.to_s].present?
            cur_count = pack_tickets[pack_id.to_s]["count"]
            if cur_count >= count
                cur_count -= count
                if cur_count > 0
                    pack_tickets[pack_id.to_s]["count"] = cur_count
                else
                    pack_tickets.delete(pack_id.to_s)
                end
                user.has_items["pack_tickets"] = pack_tickets
                return true
            else
                return false
            end
        else
            return false
        end
    end

    def self.consume_main_pack_ticket?(user,count)
        if user.has_items["main_pack_tickets"] >= count
            user.has_items["main_pack_tickets"] -= count
            return true
        else
            return false
        end
    end
    def self.get_pack_ticket(user)
        pack_tickets = user.has_items["pack_tickets"] || {}
    end

    # アイテムキーから報酬を作成
    def self.create_rewards_from_item_key(item_key)
        rewards = []
        item_key.split(",").each do |item_key|
            rewards << parse_reward_string(item_key.strip) # 余計な空白を削除
        end
        rewards
    rescue => e
        raise StandardError.new(e.message)
    end

    private
    def self.validate_data_format(key,data)
        return false unless DATA_TEMPLATES[key]
        template = DATA_TEMPLATES[key]
        return false unless template.keys.all? { |k| data.key?(k) }
        template.each do |k,v|
            return false unless data[k].is_a?(v.class)
        end
        return true
    end

    # 報酬文字列から報酬を作成
    def self.parse_reward_string(reward_str)
        case reward_str
        # 個数のみ（item_typeに固定名を使い、item_idを0または固定）
        when /\Adia_(\d+)\z/
            { item_type: "dia", item_id: 0, count: $1.to_i ,ext: {}}

        when /\Aspecial_pack_(\d+)\z/
            { item_type: "special_pack", item_id: 0, count: $1.to_i ,ext: {}}

        # 種類のみ（countは常に1）
        when /\Aicon(\d+)\z/
            { item_type: "icon", item_id: $1.to_i, count: 1 ,ext: {}}

        when /\Aicon_frame(\d+)\z/
            { item_type: "icon_frame", item_id: $1.to_i, count: 1 ,ext: {}}

        when /\Acard_sleeve(\d+)\z/
            { item_type: "card_sleeve", item_id: $1.to_i, count: 1 ,ext: {}}

        when "prebuilt_deck", "playmat", "bgm", "title"
            { item_type: reward_str, item_id: 0, count: 1 ,ext: {}}

        # 種類と個数
        when /\Apack_ticket(\d+)_(\d+)\z/
            { item_type: "pack_ticket", item_id: $1.to_i, count: $2.to_i ,ext: {}}

        when /\Acard_sand(\d+)_(\d+)\z/
            { item_type: "card_sand", item_id: $1.to_i, count: $2.to_i ,ext: {}}

        when /\Anr_card(\d+)_(\d+)\z/
            { item_type: "nr_card", item_id: $1.to_i, count: $2.to_i ,ext: {}}

        when /\Ash_card(\d+)_(\d+)\z/
            { item_type: "sh_card", item_id: $1.to_i, count: $2.to_i ,ext: {}}

        when /\Apr_card(\d+)_(\d+)\z/
            { item_type: "pr_card", item_id: $1, count: $2.to_i ,ext: {}}

        else
            raise StandardError.new("unknown_reward_format")
        end
    end

    # パックチケットをハッシュから配列に変換
    def self.pack_tickets_to_array(pack_tickets)
        pack_tickets.map do |k, v|
            {
                "pack_id" => k.to_i,
                "count" => v["count"]
            }
        end
    end

    # カードサンドをハッシュから配列に変換
    def self.card_sands_to_array(card_sands)
        card_sands.map do |k, v|
            {
                "item_id" => k.to_i,
                "count" => v["count"]
            }
        end
    end

    def self.skin_sands_to_array(skin_sands)
        skin_sands.map do |k, v|
            {
                "group_id" => k.to_i,
                "count" => v["count"]
            }
        end
    end
end