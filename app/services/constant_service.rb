class ConstantService

    # 受け入れバージョン
    ACCEPT_IOS_VERSIONS = ["api_test_version", "1.1.2"]
    ACCEPT_ANDROID_VERSIONS = ["api_test_version", "1.1.2"]
    ACCEPT_STREAM_VERSIONS = ["api_test_version", "1.1.2"]

    # ログインの更新時刻（深夜４時）
    LOGIN_UPDATE_TIME = "04:00:00 +0900"
    # バトルパスの必要ポイントステップ
    BATTLE_PASS_MISSION_PTS = 100

    # デイリーショップのカード個数
    DAILY_SHOP_CARD_COUNT = 6
    #デイリーショップのカード販売枚数
    DAILY_SHOP_CARD_SALES_COUNT = 1

    # デイリーショップ確率（初回）
    DAILY_SHOP_PROBABILITIES_FIRST = {
        "bronze1"=> 60,
        "silver2"=> 40,
        "gold3"=> 0.0,
        "legend4"=> 0.0
    }
    # デイリーショップ確率（2回目以降）
    DAILY_SHOP_PROBABILITIES_OTHER = {
        "bronze1"=> 50,
        "silver2"=> 30,
        "gold3"=> 15,
        "legend4"=> 5
    }

    # 名前変更に必要なダイヤの個数
    NAME_CHANGE_COST = 500
    # 無料名前変更の回数上限
    NAME_CHANGE_FREE_COUNT_LIMIT = 1
    
    # 宝箱の最大個数
    CHESTS_COUNT = 4

    # 設定の最大容量
    MAX_SETTINGS_SIZE = 100

    # デイリーショップ更新に必要なダイヤの個数
    DAILY_SHOP_UPDATE_DIA_COSTS = [100,250]
    # パスの商品キー
    PASS_PRODUCT_KEYS = ["pack_pass","analysis_pass","main_battle_pass"]


    def self.default_data
      {
        "operation" => {
            "maintenance_immediate" => false, # 即時メンテナンス
            "maintenance_mode" => true, # 日時指定メンテナンス
            "maintenance_start" => "2025-05-21 00:00:00 +0900", # メンテナンス開始時間
            "maintenance_end" => "2025-05-21 00:00:00 +0900", # メンテナンス終了時間
            "server_version" => "1.0.0", # サーバーバージョン
            "aa_version_ios" => "0.1.0", # アセットバンドルバージョン
            "aa_version_android" => "0.1.0", # アセットバンドルバージョン
            "aa_version_stream" => "0.1.0", # アセットバンドルバージョン
        },
        "shop" => {
            "daily_shop_card_sales_count" => 1, # デイリーショップカード販売数
            
            "daily_shop_probabilities_first" => { # デイリーショップ確率(無料枠)
              "bronze1" => 60.0,
              "silver2" => 40.0,
              "gold3" => 5.0,
              "legend4" => 0.0,
            },
            
            "daily_shop_probabilities_other" => { # デイリーショップ確率(2項目め以降)
              "bronze1" => 50.0,
              "silver2" => 30.0,
              "gold3" => 15.0,
              "legend4" => 5.0,
            },

            "daily_shop_dia_costs" => { # デイリーショップ購入に必要なダイヤ数
              "bronze1" => 50,
              "silver2" => 30,
              "gold3" => 15,
              "legend4" => 5
            },

            "daily_shop_discount_draw_probabilities_1" => { # デイリーショップ割引抽選確率(ブロンズ)
              "per0" => 0,
              "per20" => 10,
              "per40" => 70,
              "per60" => 15,
              "per80" => 5
            },
            "daily_shop_discount_draw_probabilities_2" => { # デイリーショップ割引抽選確率(シルバー)
              "per0" => 0,
              "per20" => 10,
              "per40" => 70,
              "per60" => 15,
              "per80" => 5
            },
            
            "daily_shop_discount_draw_probabilities_3" => { # デイリーショップ割引抽選確率(ゴールド)
              "per0" => 0,
              "per20" => 10,
              "per40" => 70,
              "per60" => 15,
              "per80" => 5
            },
            
            "daily_shop_discount_draw_probabilities_4" => { # デイリーショップ割引抽選確率(レジェンド)
              "per0" => 0,
              "per20" => 10,
              "per40" => 70,
              "per60" => 15,
              "per80" => 5
            },

            "daily_shop_update_dia_costs" => [100,250], # デイリーショップ更新に必要なダイヤ数

            "card_change_dia_costs" => { # カード交換ダイヤコスト
              "bronze1" => 50,
              "silver2" => 30,
              "gold3" => 15,
              "legend4" => 5
            },

            "is_pack_pass_visible" => true, # パックパスの表示有無
            "is_analysis_pass_visible" => false, # 分析パスの表示有無

        },

        "pack" => {
          "card_change_pts_costs" => { # カード交換ポイントコスト
            "bronze1" => 50,
            "silver2" => 30,
            "gold3" => 15,
            "legend4" => 5
          },

          "pack_default_rare_probabilities" => { # パックデフォルトレア度確率
            "rarity_1" => 55,
            "rarity_2" => 35,
            "rarity_3" => 8,
            "rarity_4" => 2,
          },

          "pack_default_edition_probabilities" => { # パックデフォルトエディション確率
            "edition_1" => 94,
            "edition_2" => 5,
            "edition_3" => 1
          },

          "one_pack_points" => 5 # １パック開封ごとにたまるポイント
        },

        "home" => {
          "chest_probabilities" => { # 宝箱確率
            "wood" => 60,
            "silver" => 30,
            "gold" => 9,
            "rainbow" => 1
          }
        },

        "deck" => {
          "default_box" => [80009, 80013, 80017, 80024, 80025, 80008, 80021, 80028, 80026, 80027], # デフォルトボックス
          "default_deck_name" => ['デフォルトデッキ'], # デフォルトデッキ名
          "default_deck_code" => ['{"g":4,"cd":"80024003,80009003,80013003,80017003,80025003,80021003,80026003,80028003,80027003"}'], # デフォルトデッキコード
        },

        "skin" => {
          "sand_count_group" => 100, # スキンがかぶったときに取得できる砂の数
          "skin_max_level" => 3, # スキン最大レベル
          "skin_level_up_cost" => 1, # スキンレベルアップに必要なダイヤ
        },

        "other" => {
          "name_change_cost" => 500, # 有償名前変更必要ダイヤ
          "name_change_free_count_limit" => 1, # 無料名前変更回数上限
          "takeover_code_limit" => 3650 # 引継ぎコード有効期限(日)
        },

        "danger" => {
          "default_all_box" => false, # 最初から全カード所持(1で有効)
        }
      }
    end

    # キーの日本語訳マッピング
    def self.translations
      {
        # トップレベルキー
        "operation" => '運用',
        "shop" => 'ショップ',
        "pack" => 'パック',
        "other" => 'その他',
        "home" => 'ホーム',
        "deck" => 'デッキ',
        "skin" => 'スキン',
        "danger" => 'テスト用',

        # 運用内のキー
        "maintenance_immediate" => '即時メンテナンス',
        "maintenance_mode" => '日時指定メンテナンス',
        "maintenance_start" => 'メンテナンス開始時間',
        "maintenance_end" => 'メンテナンス終了時間',
        "server_version" => 'サーバーバージョン',
        "aa_version_ios" => 'アセットバンドルバージョン(iOS)',
        "aa_version_android" => 'アセットバンドルバージョン(Android)',
        "aa_version_stream" => 'アセットバンドルバージョン(Stream)',

        # ショップ内のキー
        "daily_shop_card_sales_count" => 'デイリーショップカード販売数',
        "daily_shop_probabilities_first" => 'デイリーショップ確率(無料枠)',
        "daily_shop_probabilities_other" => 'デイリーショップ確率(2項目め以降)',
        "daily_shop_dia_costs" => 'デイリーショップ購入に必要なダイヤ数',
        "daily_shop_update_dia_costs" => 'デイリーショップ更新ダイヤコスト',
        "card_change_dia_costs" => 'カード交換ダイヤコスト',
        "is_pack_pass_visible" => 'パックパスの表示有無',
        "is_analysis_pass_visible" => '分析パスの表示有無',
        
        # デイリーショップ割引抽選確率
        "daily_shop_discount_draw_probabilities_1" => 'デイリーショップ割引抽選確率(ブロンズ)',
        "daily_shop_discount_draw_probabilities_2" => 'デイリーショップ割引抽選確率(シルバー)',
        "daily_shop_discount_draw_probabilities_3" => 'デイリーショップ割引抽選確率(ゴールド)',
        "daily_shop_discount_draw_probabilities_4" => 'デイリーショップ割引抽選確率(レジェンド)',
        
        # 割引率
        "per0" => '0%割引 (割引なし)',
        "per20" => '20%割引',
        "per40" => '40%割引',
        "per60" => '60%割引',
        "per80" => '80%割引',
        
        # パック内のキー
        "card_change_pts_costs" => 'カード交換ポイントコスト',
        "pack_default_rare_probabilities" => 'パックデフォルトレア度確率',
        "pack_default_edition_probabilities" => 'パックデフォルトエディション確率',
        "one_pack_points" => '1パック開封ごとのポイント',
        
        # レア度
        "rarity_1" => 'レア度1(ブロンズ)',
        "rarity_2" => 'レア度2(シルバー)',
        "rarity_3" => 'レア度3(ゴールド)',
        "rarity_4" => 'レア度4(レジェンド)',
        
        # エディション
        "edition_1" => '加工1(通常)',
        "edition_2" => '加工2(シャイン)',
        "edition_3" => '加工3(プレミアム)',
        
        # ホーム内のキー
        "chest_probabilities" => '宝箱確率',
        
        # チェストタイプ
        "wood" => '木の宝箱',
        "silver" => 'シルバーの宝箱',
        "gold" => 'ゴールドの宝箱',
        "rainbow" => 'レインボーの宝箱',
        
        # デッキ内のキー
        "default_all_box" => '最初から全カード所持(1で有効)',
        "default_box" => 'デフォルトボックス',
        "default_deck_name" => 'デフォルトデッキ名',
        "default_deck_code" => 'デフォルトデッキコード',

        # スキン内のキー
        "sand_count_group" => 'スキンがかぶったときに取得できる砂の数',
        "skin_max_level" => 'スキン最大レベル',
        "skin_level_up_cost" => 'スキンレベルアップに必要な砂の個数',
        
        # その他のキー
        "name_change_cost" => '有償名前変更必要ダイヤ',
        "name_change_free_count_limit" => '無料名前変更回数上限',
        "takeover_code_limit" => '引継ぎコード有効期限(日)',
        
        # カードレアリティ
        "bronze1" => 'ブロンズ',
        "silver2" => 'シルバー',
        "gold3" => 'ゴールド',
        "legend4" => 'レジェンド'
      }
    end

    def self.get(key=nil)
        return ParameterSetting.settings if key.nil?
        
        keys = key.to_s.split('.')
        settings = ParameterSetting.settings
        
        # ParameterSetting.settingsから値を取得
        value = find_value_in_nested_hash(settings, keys)
        return value unless value.nil?
        
        # default_dataから値を取得
        defaults = default_data
        value = find_value_in_nested_hash(defaults, keys)
        
        return value
    end
    
    # ネストしたハッシュから値を再帰的に探索
    def self.find_value_in_nested_hash(hash, keys)
        return nil unless hash.is_a?(Hash)
        return hash if keys.empty?
        
        current_key = keys.first
        remaining_keys = keys[1..-1]
        
        # キーをシンボルとして試す
        symbol_key = current_key.to_sym rescue nil
        # キーを文字列として試す
        string_key = current_key.to_s
        
        # ハッシュ内にキーが存在するか確認（シンボル優先、次に文字列）
        if hash.key?(symbol_key)
            return hash[symbol_key] if remaining_keys.empty?
            return find_value_in_nested_hash(hash[symbol_key], remaining_keys)
        elsif hash.key?(string_key)
            return hash[string_key] if remaining_keys.empty?
            return find_value_in_nested_hash(hash[string_key], remaining_keys)
        end
        
        nil
    end
end