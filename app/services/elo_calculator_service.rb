class EloCalculatorService
  SCALE = 8000
  EVEN_DELTA = 100
  K = 2 * EVEN_DELTA # 同レートなら±100になるよう調整

  def self.expected_score(rating_a, rating_b, scale = SCALE)
    1.0 / (1.0 + 10.0 ** ((rating_b - rating_a) / scale.to_f))
  end

  def self.delta_win(rating_a, rating_b, k = K, scale = SCALE)
    ea = expected_score(rating_a, rating_b, scale)
    k * (1.0 - ea)
  end

  def self.delta_lose(rating_a, rating_b, k = K, scale = SCALE)
    ea = expected_score(rating_a, rating_b, scale)
    -k * ea
  end
end
