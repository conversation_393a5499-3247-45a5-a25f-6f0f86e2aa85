class ShopPassService
    def self.get_shop_passes(user,user_lang)
        begin
            # ShopPassスキーマは使用しない
            shop_pass_list = []
            pass_product_keys = ConstantService::PASS_PRODUCT_KEYS
            user_shop_passes = user.user_save_data.dig("shop", "passes")
            user_shop_passes = {
                pass_product_keys[0] => {"is_active" => false, "expires_date" => "", "auto_renew" => false},
                pass_product_keys[1] => {"is_active" => false, "expires_date" => "", "auto_renew" => false},
            } if user_shop_passes.blank?
            user_shop_passes.each do |pass_product_key, data|
                if pass_product_key == "pack_pass" && !ConstantService.get("shop.is_pack_pass_visible")
                    next
                end
                if pass_product_key == "analysis_pass" && !ConstantService.get("shop.is_analysis_pass_visible")
                    next
                end
                shop_pass_list << {
                    product_key: pass_product_key,
                    is_bought: data["is_active"]
                }
            end
            shop_pass_list << {
                product_key: pass_product_keys[2],
                is_bought: BattlePassService.is_premium_battle_pass?(user)
            }
            SaveDataService.update_user_save_data_hash?(user,"shop",{
                passes: user_shop_passes
            })
            user.save!
            return shop_pass_list
        rescue => e
            ErrorService.handle_exception(e, "ShopPassService", "get_shop_passes")
        end
    end

    def self.buy_pass(user,user_lang,os,category)
        begin
            shop_passes = get_shop_passes(user,user_lang)
            shop_pass = shop_passes.find { |shop_pass| shop_pass[:category] == category }
            unless shop_pass.present?
                raise StandardError.new("shop_pass_not_found")
            end
            if shop_pass[:is_bought]
                raise StandardError.new("shop_pass_already_bought")
            end
            # TODO: リアルマネー決済処理
            user.user_save_data["shop"]["passes"][shop_pass[:category]] = true
            user.save!
            return {
                shop: "api/shop/index",
                user:UserService.get_res(user)
            }
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_pass")
        end
    end

    # パスの有効期限をチェックして、期限切れの場合は無効にする
    def self.check_pass_active(user)
        begin
            user_shop_passes = user.user_save_data.dig("shop", "passes") || {}
            user_shop_passes.each do |pass_product_key, data|
                if data["expires_date"].blank? || data["expires_date"] < Time.now
                    data["is_active"] = false
                    data["auto_renew"] = false
                end
            end
            SaveDataService.update_user_save_data_hash?(user,"shop",{
                passes: user_shop_passes
            })
        rescue => e
            ErrorService.handle_exception(e, "ShopPassService", "check_pass_active")
        end
    end

    # 初回ログインとパックパス購入時の処理
    def self.handle_pack_pass(user,is_forced = false)
        begin
            has_pack_pass = check_pass_valid?(user,"pack_pass") || is_forced
            if has_pack_pass
                rewards = [{
                    "count" => 1,
                    "item_type" => "main_pack_ticket",
                    "item_id" => 0,
                    "ext" => {}
                }]

                # uidはcreate時に自動生成される
                user.gifts.create(
                    rewards: rewards,
                    desc: {
                        "ja" => "パックパスのギフトです"
                    },
                    name:{
                        "ja" => "メインパックチケット"
                    },
                    end_at:Time.now + 30.days
                )
            else
                return
            end
        rescue => e
            ErrorService.handle_exception(e, "ShopPassService", "handle_pack_pass")
        end
    end

    # 初回ログインと分析パス購入時の処理
    def self.handle_analysis_pass(user,is_forced = false)
        begin
            has_analysis_pass = check_pass_valid?(user,"analysis_pass") || is_forced
            if has_analysis_pass
                rewards = [{
                    "count" => 1,
                    "item_type" => "special_pack",
                    "item_id" => 0,
                    "ext" => {}
                }]
                # uidはcreate時に自動生成される
                user.gifts.create(
                    rewards: rewards,
                    desc: {
                        "ja" => "分析パスのギフトです"
                    },
                    name:{
                        "ja" => "スペシャルパック"
                    },
                    end_at:Time.now + 30.days
                )
            else
                return
            end
        rescue => e
            ErrorService.handle_exception(e, "ShopPassService", "handle_analysis_pass")
        end
    end

    def self.check_pass_valid?(user,pass_category)
        begin
            pass_info = user.user_save_data.dig("shop", "passes", pass_category) || {}
            if pass_info["is_active"] && pass_info["expires_date"] > Time.now
                return true
            else
                return false
            end
        rescue => e
            ErrorService.handle_exception(e, "ShopPassService", "check_pass_valid")
        end
    end
end
