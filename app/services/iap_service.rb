class IapService
    def self.get_charging_item(product_kind,os_type)
        if os_type == "ios"
            ci = ChargingItem.find_by(ios_key: product_kind)
        elsif os_type == "android"
            ci = ChargingItem.find_by(android_key: product_kind)
        else
            raise StandardError.new("invalid_os_type")
        end
        if ci.nil?
            raise StandardError.new("invalid_product_id")
        end
        return ci
    end

    # 消費型の課金処理
    def self.bought_process(user,product_key,os_type)
        if product_key == ConstantService::PASS_PRODUCT_KEYS[2] # main_battle_pass
            # 最新のシーズンのプレミアムバトルパスを購入する
            BattlePassService.update_premium_battle_pass(user)
            Rails.logger.info(category: "iap_service.bought_process", user: user.open_id, product_key: product_key, rewards: [])
            return { success: true, category: ConstantService::PASS_PRODUCT_KEYS[2] ,rewards: []}
        end
        if os_type == "ios"
            ci = ChargingItem.find_by(ios_key: product_key)
        elsif os_type == "android"
            ci = ChargingItem.find_by(android_key: product_key)
        else
            raise StandardError.new("invalid_os_type")
        end
        if ci.nil?
            raise StandardError.new("invalid_product_id")
        end

        # ダイヤ購入の場合
        if ci.category == "dia"
            rewards = ci.rewards
            unless ItemService.add_rewards?(user,rewards,os_type)
                raise StandardError.new("failed_to_add_rewards")
            end
            Rails.logger.info(category: "iap_service.bought_process", user: user.open_id, product_key: product_key, rewards: rewards)
            return { success: true, category: ci.category,rewards: rewards }

        # バンドル購入の場合
        elsif ci.category == "bundle"
            u_bundle_data = user.user_save_data.dig("shop","cash_bundles") || []
            # 最大購入回数を超える場合は購入できない
            max_count = ci.max_count

            # 購入回数のデータを更新する
            existing_bundle = u_bundle_data.find { |bundle| bundle["uid"] == ci.uid }
            if existing_bundle
                if existing_bundle["bought_count"] >= max_count
                    raise StandardError.new("exceeded_max_count")
                end
                existing_bundle["bought_count"] += 1
            else
                u_bundle_data << { uid: ci.uid, bought_count: 1 }
            end
            unless SaveDataService.update_user_save_data_hash?(user, "shop",{
                cash_bundles: u_bundle_data
            })
                raise StandardError.new("failed_to_update_cash_bundles")
            end

            unless ItemService.add_rewards?(user,ci.rewards)
                raise StandardError.new("failed_to_add_rewards")
            end
            Rails.logger.info(category: "iap_service.bought_process", user: user.open_id, product_key: product_key, rewards: ci.rewards)
            return { success: true, category: ci.category,rewards: ci.rewards }
        else
            raise StandardError.new("invalid_category")
        end
    end

    # 継続型課金の最初の処理
    def self.subscription_action(user,product_id,expires_date)
        pass_product_keys = ConstantService::PASS_PRODUCT_KEYS
        if product_id == pass_product_keys[0] # pack_pass
            # パックパス購入時の即時処理
            ShopPassService.handle_pack_pass(user,true) # TODO 即時処理がいらない場合はfalseにする
            rewards = [{
                "count" => 500,
                "item_type" => "dia",
                "item_id" => 0,
                "ext" => {}
            }]
            # TODO apple サーバーからの通知時はギフトに追加する
            unless ItemService.add_rewards?(user,rewards,"ios")
                raise StandardError.new("failed_to_add_rewards")
            end
            analysis_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[1]) || {}
            unless SaveDataService.update_user_save_data_hash?(user, "shop", {
                passes: {
                    pass_product_keys[0] => {is_active: true, expires_date: expires_date,auto_renew: true},
                    pass_product_keys[1] => {is_active: analysis_pass_info["is_active"] || false, expires_date: analysis_pass_info["expires_date"] || "",auto_renew: analysis_pass_info["auto_renew"] || false}
                }
            })
                raise StandardError.new("failed_to_update_user_save_data")
            end
            result = { success: true, category: product_id,rewards: rewards} # TODO 購入時のリスポンスは要変更
        elsif product_id == pass_product_keys[1] # analysis_pass
            pack_pass_info = user.user_save_data.dig("shop", "passes", pass_product_keys[0]) || {}
            # 分析パス購入時の即時処理
            ShopPassService.handle_analysis_pass(user,true) # TODO 即時処理がいらない場合はfalseにする
            unless SaveDataService.update_user_save_data_hash?(user, "shop", {
                passes: {
                    pass_product_keys[0] => {is_active: pack_pass_info["is_active"] || false, expires_date: pack_pass_info["expires_date"] || "",auto_renew: pack_pass_info["auto_renew"] || false},
                    pass_product_keys[1] => {is_active: true, expires_date: expires_date,auto_renew: true}
                }
            })
                raise StandardError.new("failed_to_update_user_save_data")
            end
            result = { success: true, category: product_id,rewards: []} # TODO 購入時のリスポンスは要変更
        end
    end

    def self.get_response(user,success,rewards,error_message, category)
        # categoryはdia,bundle,main_battle_pass,analysis_pass,pack_passのみ
        if success
            return {rewards: rewards,success: true,category: category,purchase_process: "complete",title: "課金処理が完了しました",message: "課金処理が完了しました"}
        end
        case error_message
        when "already_processed_purchase" # transactionのかぶりはcompleteにする
            return {rewards: [],success: false,category: category,purchase_process: "complete",title: "already_processed_purchase",message: ErrorService.api_error(error_message)[:error]}
        when "invalid_transaction"# ユーザー側の不正はcompleteにする
            Rails.logger.warn(category: "iap_service.get_response", user: user.open_id, error_message: error_message)
            return {rewards: [],success: false,category: category,
            purchase_process: "complete",title: "invalid_transaction",message: ErrorService.api_error(error_message)[:error]}
        when "premium_battle_pass_already_purchased"
            return {rewards: [],success: false,category: category,purchase_process: "complete",title: "premium_battle_pass_already_purchased",message: ErrorService.api_error(error_message)[:error]}
        else
            return {rewards: [],success: false,category: category,purchase_process: "pending",title: "server_error",message: ErrorService.api_error(error_message)[:error]}
        end
    end
end