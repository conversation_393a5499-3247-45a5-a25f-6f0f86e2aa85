class DiaChargeService
    def self.get_dia_charge(user,os)
        dia_charges= ChargingItem.within_valid_period.where(category: "dia")
        if dia_charges.nil?
            raise StandardError.new("dia_charges_not_found")
        end
        dia_charge_list = []
        dia_charges.each do |dc|
            if os == "ios"
                product_key = dc.ios_key
            elsif os == "android"
                product_key = dc.android_key
            elsif os == "steam"
                product_key = dc.steam_key
            end
            rewards = dc.rewards
            dia = rewards[0]["count"]
            dia_charge_list << {
                product_key: product_key,
                rewards: rewards
            }
        end
        return dia_charge_list
    end

    def self.buy_dia(user,os,uid)

        dia_charges = get_dia_charge(user)
        dia_charge = dia_charges.find { |dc| dc[:uid] == uid }
        if dia_charge.nil?
            raise StandardError.new("not_found")
        end
        # TODO リアルマネー決済処理
        if DiaService.charge_dia?(user,os,dia_charge[:dia])
            return {
                shop: "api/shop/index",
                user:UserService.get_res(user)
            }
        else
            return {error_type: "failed_to_charge_dia", error: "ダイアの購入に失敗しました"}
        end
    end
end
