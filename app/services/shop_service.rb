class ShopService
    def self.get_shop_index(user,user_lang,os)
        begin
            ActiveRecord::Base.transaction do
                @daily_shops_cards = DailyShopService.get_daily_shop_cards(user)
                @card_change_cards = CardChangeService.get_card_change_cards(user,user_lang)
                @dia_charge = DiaChargeService.get_dia_charge(user,os)
                @supplies = SupplyService.get_supplies(user)
                @bonus = ShopBonusService.get_shop_bonuses(user)
                @bundles = ShopBundleService.get_shop_bundles(user,os)
                @passes = ShopPassService.get_shop_passes(user,user_lang)
            end
            total_charged_dia = user.user_save_data.dig("shop", "bonus", "total_charged_dia") || 0
            total_consumed_dia = user.user_save_data.dig("shop", "bonus", "total_consumed_dia") || 0
            return {
                shop: {
                    daily_shop: @daily_shops_cards,
                    card_change: @card_change_cards,
                    dia_charge: @dia_charge,
                    pass: @passes,
                    supply: @supplies,
                    bonus: {
                        charged_bonus: @bonus[:charged_bonus],
                        consumed_bonus: @bonus[:consumed_bonus],
                        bundles: @bundles,
                        total_charged_dia: total_charged_dia,
                        total_consumed_dia: total_consumed_dia
                    },
                    info: {
                        current_time: Time.current,
                    }
                }
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "get_shop_index")
        end
    end
end
