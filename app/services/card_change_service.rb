class CardChangeService
    def self.get_card_change_cards(user,user_lang)
        begin
            need_dia_rarity = ConstantService.get("shop.card_change_dia_costs")
            set_card_data
            card_list = []
            @all_cards.each do |card|
                card_id = card["ID"]
                if card_id < 10000
                    next
                end
                card_list << {
                    item_type: "nr_card",
                    item_id: card["ID"],
                    count: 1,
                    ext: {},
                }
            end

            pack_data = []
            # 開催中のパック情報を送る
            packs_list = Pack.within_valid_period.order_by_period_start_at
            packs_list.each do |pack|
                if !pack.enable_pts
                    next
                end
                cur_pts = user.user_save_data["pack"]&.find { |saved_pack| saved_pack["pack_id"] == pack.uid }&.dig("pts") || 0
                card_ids = pack.card_data.map { |c| c["id"] }
                pack_data << {
                    uid: pack.uid,
                    name: pack.name[user_lang],
                    card_list: card_ids,
                    change_pts: pack.change_pts,
                    current_pts: cur_pts,
                    enable_pts: pack.enable_pts
                }
            end
            return {
                need_dia: need_dia_rarity,
                rewards: card_list,
                packs: pack_data,
            }
        rescue StandardError => e
            raise StandardError.new(e.message)
        end
    end

    def self.buy_card(user,user_lang,os,card_id,count,category,pack_id)
        begin
            card_changes = get_card_change_cards(user,user_lang)
            card = card_changes[:rewards].find { |card| card[:item_id] == card_id }
            if card.nil?
                raise StandardError.new("card_change_card_not_found")
            end
            rarity_int = MasterDatum.get_card_rarity(card_id)
            case rarity_int
            when 1
                rarity = "bronze1"
            when 2
                rarity = "silver2"
            when 3
                rarity = "gold3"
            when 4
                rarity = "legend4"
            end
            if category == "dia"
                need_dia = card_changes[:need_dia][rarity] || 0
                unless DiaService.consume_dia?(user, os, need_dia * count)
                    raise StandardError.new("not_enough_dia")
                end
            elsif category == "pts"
                if pack_id.present?
                    pack_data = card_changes[:packs].find { |pack| pack[:uid] == pack_id && pack[:card_list].include?(card_id) }
                else
                    pack_data = card_changes[:packs].find { |pack| pack[:card_list].include?(card_id) } # pack_idを指定しない場合は、card_idが含まれるパックのうち、開催時刻が最新のパックを取得
                end
                if pack_data.nil? || !pack_data[:enable_pts]
                    raise StandardError.new("pack_not_found")
                end
                cur_pts = pack_data[:current_pts]
                cur_count = user.user_save_data["pack"]&.find { |saved_pack| saved_pack["pack_id"] == pack_data[:uid] }&.dig("count") || 0
                consumed_pts = pack_data[:change_pts][rarity] * count
                if cur_pts < consumed_pts
                    raise StandardError.new("not_enough_pts")
                end
                cur_pts -= consumed_pts
                unless SaveDataService.add_user_save_data?(user, "pack", {
                    pack_id: pack_data[:uid], pts: cur_pts, count: cur_count
                })
                    raise StandardError.new("failed_to_add_user_save_data")
                end
            else
                raise StandardError.new("invalid_category")
            end
            unless ItemService.add_rewards?(user,[card])
                raise StandardError.new("failed_to_add_rewards")
            end
            user.save!
            shop_result = ShopService.get_shop_index(user,user_lang,os)
            if shop_result[:error_type].present?
                raise StandardError.new("failed_to_get_shop_index")
            end
            return {
                rewards: [card],
                shop: shop_result[:shop],
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_card")
        end
    end

    private
    def self.set_card_data()
        begin
            if MasterDatum.order(created_at: :desc).first.nil?
                raise StandardError.new("no_master_data")
            end
            @all_cards = MasterDatum.order(created_at: :desc).first.content["_cards"]
            cards_by_rarity = @all_cards.group_by { |card| card["Rarity"].to_i }
            @bronze1_cards = cards_by_rarity[1] || []
            @silver2_cards = cards_by_rarity[2] || []
            @gold3_cards = cards_by_rarity[3] || []
            @legend4_cards = cards_by_rarity[4] || []
        rescue StandardError => e
            raise StandardError.new(e.message)
        end
    end
end
