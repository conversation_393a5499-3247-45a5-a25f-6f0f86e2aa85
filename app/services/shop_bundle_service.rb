class ShopBundleService
    def self.get_shop_bundles(user,os)
        shop_dia_bundles = ShopBundle.within_valid_period.order_by_period_start_at
        shop_cash_bundles = ChargingItem.within_valid_period.where(category: "bundle").order_by_period_start_at
        u_dia_bundles = user.user_save_data.dig("shop", "dia_bundles") || []
        u_cash_bundles = user.user_save_data.dig("shop", "cash_bundles") || []
        dia_bundles_list = []
        cash_bundles_list = []
        shop_dia_bundles.each do |shop_bundle|
            existing_bundle = u_dia_bundles.find { |data| data["uid"] == shop_bundle.uid }
            dia_bundles_list << {
                uid: shop_bundle.uid,
                rewards: shop_bundle.rewards,
                need_dia: shop_bundle.cost,
                bought_count: existing_bundle ? existing_bundle["bought_count"] : 0,
                max_count: shop_bundle.max_count
            }
        end
        shop_cash_bundles.each do |cash_bundle|
            case os
            when "ios"
                product_key = cash_bundle.ios_key
            when "android"
                product_key = cash_bundle.android_key
            when "steam"
                product_key = cash_bundle.steam_key
            end
            existing_bundle = u_cash_bundles.find { |data| data["uid"] == cash_bundle.uid }
            cash_bundles_list << {
                uid: cash_bundle.uid,
                rewards: cash_bundle.rewards,
                product_key: product_key,
                bought_count: existing_bundle ? existing_bundle["bought_count"] : 0,
                max_count: cash_bundle.max_count,
            }
        end
        SaveDataService.update_user_save_data_hash?(user,"shop",{
            dia_bundles: u_dia_bundles,
            cash_bundles: u_cash_bundles
        })
        return {
            dia_bundles: dia_bundles_list,
            cash_bundles: cash_bundles_list
        }
    end

    def self.buy_dia_bundle(user,os,user_lang,uid)
        begin
            shop_bundles = get_shop_bundles(user,os)
            shop_bundle = shop_bundles[:dia_bundles].find { |dia_bundle| dia_bundle[:uid] == uid }
            if shop_bundle.blank?
                raise StandardError.new("shop_bundle_not_found")
            end
            u_dia_bundles = user.user_save_data.dig("shop", "dia_bundles") || []
            existing_bundle = u_dia_bundles.find { |data| data["uid"] == uid }
            if existing_bundle
                if shop_bundle[:max_count].present? && existing_bundle["bought_count"] >= shop_bundle[:max_count]
                    raise StandardError.new("exceeded_max_count")
                end
                existing_bundle["bought_count"] += 1
            else
                u_dia_bundles << { uid: uid, bought_count: 1 }
            end

            unless SaveDataService.update_user_save_data_hash?(user,"shop",{
                dia_bundles: u_dia_bundles
            })
                raise StandardError.new("failed_to_update_save_data")
            end
            unless DiaService.consume_dia?(user, os, shop_bundle[:need_dia])
                raise StandardError.new("not_enough_dia")
            end
            ItemService.add_rewards?(user,shop_bundle[:rewards])
            user.save!
            shop_index = ShopService.get_shop_index(user,user_lang,os)
            if shop_index[:error_type].present?
                raise StandardError.new(shop_index[:error_type])
            end
            return {
                rewards: shop_bundle[:rewards],
                shop: shop_index[:shop],
                user:UserService.get_res(user)
            }
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_dia_bundle")
        end
    end

    # 使わない
    def self.buy_cash_bundle(user, os, user_lang, receipt)
        begin
            if os == "ios"
                result = IapIosService.check_for_ios(user, receipt)[:ci] # このメソッドで相武哲以下とセーブデータ更新まで行っている
                if result[:error_type].present?
                    raise StandardError.new(result[:error_type])
                end
            elsif os == "android"
                result = IapAndroidService.check_for_and(user, receipt)
                if result[:error_type].present?
                    raise StandardError.new(result[:error_type])
                end
            end
            ci = result[:ci]
            # 購入成功時にcash_bundlesを更新
            u_cash_bundles = user.user_save_data.dig("shop", "cash_bundles") || []
            existing_bundle = u_cash_bundles.find { |data| data["uid"] == ci.uid }

            # バンドルの最大購入回数を取得
            cash_bundle = ChargingItem.find_by(uid: ci.uid, category: "bundle")
            if cash_bundle.blank?
                raise StandardError.new("shop_bundle_not_found")
            end

            if existing_bundle && existing_bundle["bought_count"] >= cash_bundle.max_count
                raise StandardError.new("exceeded_max_count")
            end
            shop_index = ShopService.get_shop_index(user, user_lang,os)
            if shop_index[:error_type].present?
                raise StandardError.new(shop_index[:error_type])
            end
            return {
                rewards: cash_bundle.rewards,
                shop: shop_index[:shop],
                user: UserService.get_res(user)
            }
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_cash_bundle")
        end
    end
end
