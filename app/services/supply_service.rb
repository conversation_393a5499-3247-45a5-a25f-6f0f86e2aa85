class SupplyService
    def self.get_supplies(user)
        begin
            supplies = Supply.within_valid_period.order_by_period_start_at
            icons =[]
            icon_frames =[]
            card_sleeves =[]
            playmats = []
            bgms = []


            # ユーザーのボーナス関連の情報取得
            item_data = ItemService.get_item_data(user)
            user_supply_icon = item_data[:icons]
            user_supply_icon_frame = item_data[:icon_frames]
            user_supply_card_sleeve = item_data[:card_sleeves]
            user_supply_playmat = item_data[:playmats]
            user_supply_bgm = item_data[:bgms]

            supplies.each do |supply|
                case supply.category
                when "icon"
                    rewards = supply.rewards
                    item_id = rewards.first["item_id"]
                    unless validate_rewards_type?("icon",rewards)
                        raise StandardError.new("invalid_rewards_type")
                    end
                    if user_supply_icon.include?(item_id)
                        has_item = true
                    else
                        has_item = false
                    end
                    icons << {
                        uid: supply.uid,
                        rewards: supply.rewards,
                        need_dia: supply.dia_cost,
                        has_item: has_item,
                    }
                when "icon_frame"
                    rewards = supply.rewards
                    item_id = rewards.first["item_id"]
                    unless validate_rewards_type?("icon_frame",rewards)
                        raise StandardError.new("invalid_rewards_type")
                    end
                    if user_supply_icon_frame.include?(item_id)
                        has_item = true
                    else
                        has_item = false
                    end
                    icon_frames << {
                        uid: supply.uid,
                        rewards: supply.rewards,
                        need_dia: supply.dia_cost,
                        has_item: has_item,
                    }
                when "card_sleeve"
                    rewards = supply.rewards
                    item_id = rewards.first["item_id"]
                    unless validate_rewards_type?("card_sleeve",rewards)
                        raise StandardError.new("invalid_rewards_type")
                    end
                    if user_supply_card_sleeve.include?(item_id)
                        has_item = true
                    else
                        has_item = false
                    end
                    card_sleeves << {
                        uid: supply.uid,
                        rewards: supply.rewards,
                        need_dia: supply.dia_cost,
                        has_item: has_item,
                    }
                when "playmat"
                    rewards = supply.rewards
                    item_id = rewards.first["item_id"]
                    unless validate_rewards_type?("playmat",rewards)
                        raise StandardError.new("invalid_rewards_type")
                    end
                    if user_supply_playmat.include?(item_id)
                        has_item = true
                    else
                        has_item = false
                    end
                    playmats << {
                        uid: supply.uid,
                        rewards: supply.rewards,
                        need_dia: supply.dia_cost,
                        has_item: has_item,
                    }
                when "bgm"
                    rewards = supply.rewards
                    item_id = rewards.first["item_id"]
                    unless validate_rewards_type?("bgm",rewards)
                        raise StandardError.new("invalid_rewards_type")
                    end
                    if user_supply_bgm.include?(item_id)
                        has_item = true
                    else
                        has_item = false
                    end
                    bgms << {
                        uid: supply.uid,
                        rewards: supply.rewards,
                        need_dia: supply.dia_cost,
                        has_item: has_item,
                    }
                end
            end
            return {
                icon: icons,
                icon_frame: icon_frames,
                card_sleeve: card_sleeves,
                playmat: playmats,
                bgm: bgms,
            }
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "get_supplies")
        end
    end

    def self.buy_supply(user,user_lang,os,category,uid)
        begin
            supplies = get_supplies(user)
            # 指定されたsupplyのカテゴリーの配列を取得
            one_supplies = supplies[category.to_sym]
            if one_supplies.nil?
                raise StandardError.new("supply_not_found")
            end
            # 指定されたsupplyのuidのsupplyを取得
            supply = one_supplies.find { |supply| supply[:uid] == uid }
            if supply.nil?
                raise StandardError.new("supply_not_found")
            end
            if supply[:has_item]
                raise StandardError.new("supply_already_owned")
            end
            need_dia = supply[:need_dia] || 0
            unless DiaService.consume_dia?(user, os, need_dia)
                raise StandardError.new("not_enough_dia")
            end
            unless ItemService.add_rewards?(user,supply[:rewards])
                raise StandardError.new("failed_to_add_rewards")
            end
            shop_result = ShopService.get_shop_index(user,user_lang,os)
            user.save!
            if shop_result[:error_type].present?
                raise StandardError.new("failed_to_get_shop_index")
            end
            return {
                rewards: supply[:rewards],
                shop: shop_result[:shop],
                user:UserService.get_res(user)
            }
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_supply")
        end
    end

    private

    # 報酬の型が指定したアイテムと同じかをチェックする
    def self.validate_rewards_type?(icon_type,rewards)
        rewards.map do |reward|
            if reward["item_type"] != icon_type
                return false
            end
        end
        return true
    end
end