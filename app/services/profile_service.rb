class ProfileService
    # 各キーのテンプレート
    DATA_TEMPLATES = {
        "icon" =>0,
        "icon_frame" =>0,
        "card_collections" =>[],
        "badges" =>[],
        "title" =>0,
        "chara_id" =>0
    }.freeze

    def self.init_profile()
        # TODO デフォルトのプロフィールを設定する
        {"icon" =>1,
        "icon_frame" =>1,
        "card_collections" =>[],
        "badges" =>[],
        "title" =>1,
        "chara_id" =>1 # TODO デフォルトでnilにしたいときはどのようにしたいか考える
        }
    end

    def self.index(user)
        user_data = UserService.get_res(user)
        name_change_count = user.user_save_data&.dig("profile", "name_change_count") || 0
        need_dia = ConstantService.get('other.name_change_cost')
        return {user: user_data, name_change: {changed_count: name_change_count, need_dia: need_dia},
            last_update_at: user.user_save_data&.dig("profile", "last_update_at") || Time.now.to_s}
        rescue StandardError => e
            ErrorService.handle_exception(e, "ProfileService", "index")
    end

    def self.get_user_profile(user)
        return {
            icon: user.profile_settings["icon"] || 1,
            icon_frame: user.profile_settings["icon_frame"] || 1,
            card_collections: user.profile_settings["card_collections"] || [],
            badges: user.profile_settings["badges"] || [],
            title: user.profile_settings["title"] || 1,
            chara_id: user.profile_settings["chara_id"] || 1,
        }
    end

    # TODO プロフィールの更新の時に自分の持っているアイテムかどうかを確認する
    def self.update_profile(user, params)
        begin
            params.each do |key, value|
                update_each_data(user, key, value)
            end
            unless SaveDataService.update_user_save_data_hash?(user, "profile", {last_update_at: Time.now.to_s})
                raise StandardError.new("failed_to_update_profile")
            end
            user.save!
            user.create_log("profile_update", params)
            return index(user)
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ProfileController, "update_profile")
        end
    end

    # TODO 名前のvalidateのエラーメッセージを出せるようにする
    def self.name_update(user, name, os)
        begin
            if name.nil?
                raise StandardError.new("invalid_name")
            end
            count = user.user_save_data&.dig("profile","name_change_count") || 0
            if count >= ConstantService.get('other.name_change_free_count_limit')
                unless DiaService.consume_dia?(user, os, ConstantService.get('other.name_change_cost'))
                    raise StandardError.new("not_enough_dia")
                end
            end
            user.name = name
            unless SaveDataService.update_user_save_data_hash?(user, "profile", {name_change_count: count + 1, last_update_at: Time.now.to_s})
                raise StandardError.new("failed_to_update_profile_name")
            end
            user.save!
            user.create_log("name_change", {name: name})
            return index(user)
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ProfileController, "name_update")
        rescue ActiveRecord::RecordInvalid => e
            ErrorService.handle_exception(e, Api::ProfileController, "name_update", "record_invalid")
        end
    end

    private
        # データのバリデーション
        def self.validate_data_format?(key)
            return false unless DATA_TEMPLATES[key]
            return true
        end

        # user_profile の特定のキーにデータを追加・更新するメソッド
        def self.update_each_data(user,key, new_data)
            unless validate_data_format?(key.to_s)
                raise StandardError.new("invalid_profile_settings")
            end
            validate_user_item(user, key, new_data)
            profile_setting = user.profile_settings || {}
            profile_setting[key] = new_data
            user.profile_settings = profile_setting
        end

        # userがアイテムを持っているかを確認する
        def self.validate_user_item(user, item_type, data)
            u_item = ItemService.get_item_data(user)
            case item_type.to_s
            when "badges"
                if data.is_a?(Array)
                    data.each do |badge|
                        raise StandardError.new("not_enough_badge") unless u_item[:badges].include?(badge)
                    end
                else
                    raise StandardError.new("invalid_badge")
                end
            when "title"
                raise StandardError.new("not_enough_title") unless u_item[:titles].include?(data)
            when "icon"
                raise StandardError.new("not_enough_icon") unless u_item[:icons].include?(data)
            when "icon_frame"
                raise StandardError.new("not_enough_icon_frame") unless u_item[:icon_frames].include?(data)
            when "chara_id"
                return true #TODO キャラIDのバリデーションはどうする？
            when "card_collections"
                u_card = user.box.keys
                if data.is_a?(Array)
                    data.each do |card|
                        raise StandardError.new("not_enough_card_id") unless u_card.include?(card["id"].to_s)
                        u_edition = user.box[card["id"].to_s]
                        raise StandardError.new("not_enough_card_edition") unless u_edition[card["edition"].to_s] > 0
                    end

                else
                    raise StandardError.new("invalid_card_collection")
                end
            else
                raise StandardError.new("invalid_item_type")
            end
        end
end