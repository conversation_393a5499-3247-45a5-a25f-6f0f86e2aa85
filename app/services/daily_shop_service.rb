# TODO デイリーショップのカードを購入する際に,uid指定にする
class DailyShopService

    def self.on_new_day(user)
        update_daily_shop_cards(user)
    end

    def self.update_daily_shop(user,os)
        # TODO 余計なタイミングでuser.saveをしている箇所がある気がする
        begin
            ActiveRecord::Base.transaction do
                update_count = user.user_save_data.dig("shop", "daily_shop", "update_count") || 0
                has_analysis_pass = ShopPassService.check_pass_valid?(user,"analysis_pass")
                if has_analysis_pass
                    if update_count == 0
                        need_dia = 0
                    else
                        costs = ConstantService.get('shop.daily_shop_update_dia_costs')
                        need_dia = costs[update_count - 1]
                    end
                else
                    costs = ConstantService.get('shop.daily_shop_update_dia_costs')
                    need_dia = costs[update_count]
                end
                if need_dia.nil?
                    raise StandardError.new("max_daily_shop_update_count")
                end
                unless DiaService.consume_dia?(user,os, need_dia)
                    raise StandardError.new("not_enough_dia")
                end
                update_count += 1
                update_daily_shop_cards(user) # user.saveしてる
                unless SaveDataService.update_user_save_data_hash?(user, "shop",{
                    daily_shop: {
                        items: user.user_save_data&.dig("shop", "daily_shop", "items")||[],
                        bought_uids: user.user_save_data&.dig("shop", "daily_shop", "bought_uids")||[],
                        update_count: update_count
                    }
                })
                    raise StandardError.new("failed_to_update_daily_shop_update_count")
                end
                user.save!
            end
            return {
                daily_shop: get_daily_shop_cards(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "update_daily_shop")
        end
    end

    def self.get_daily_shop_cards(user)
        begin
            # ユーザーのデイリーショップのステータスを取得
            user_daily_shop_status = user.user_save_data&.dig("shop", "daily_shop") || {}
            if user_daily_shop_status.blank?
                update_daily_shop_cards(user)
                user_daily_shop_status = user.user_save_data&.dig("shop", "daily_shop") || {}
            end
            bought_uids = user_daily_shop_status["bought_uids"] || []
            update_count = user_daily_shop_status["update_count"] || 0
            has_analysis_pass = ShopPassService.check_pass_valid?(user,"analysis_pass")
            costs = ConstantService.get('shop.daily_shop_update_dia_costs')
            if has_analysis_pass
                if update_count == 0
                    next_need_dia = 0
                else
                    next_need_dia = costs[update_count - 1] || -1
                end
            else
                next_need_dia = costs[update_count] || -1
            end
            is_max_update_count = false
            if next_need_dia == -1
                is_max_update_count = true
            end

            # ユーザーのデイリーショップのカードを取得
            daily_shop_cards = user_daily_shop_status["items"] || []
            items = []

            # ユーザーのログインカウントから7日までのカウントダウン
            login_days = user.login_days || 0
            rest_days = login_days % 7

            daily_shop_cards.each do |card|
                rewards = [{item_type: "nr_card", item_id: card["card_id"], count: card["card_count"],ext:{}}]

                discount_info = {
                    original_cost:0,
                    discount_percent:0
                }
                if card["original_cost"].present? && card["discount_percent"].present? && card["discount_percent"] > 0
                    discount_info = {
                        original_cost: card["original_cost"],
                        discount_percent: card["discount_percent"]
                    }
                end
                items << {
                    uid: card["uid"],
                    rewards: rewards,
                    need_dia: card["dia_cost"],
                    is_bought: bought_uids.include?(card["uid"]),
                    discount: discount_info
                }
            end
            return {items: items, rest_days: rest_days,current_date: Time.current,next_date: LoginService.next_update_login_time,update_count: update_count,
            is_max_update_count: is_max_update_count,next_need_dia: next_need_dia}
        rescue => e
            ErrorService.handle_exception(e, Api::ShopController, "get_daily_shop_cards")
            return {error_type: "failed_to_get_daily_shop_cards", error_message: e.message}
        end
    end

    def self.update_daily_shop_cards(user)
        begin
            daily_shop_cards = []
            set_card_data
            for i in 0..ConstantService::DAILY_SHOP_CARD_COUNT - 1
                card = draw_daily_card(i,user)

                if card["Rarity"]
                    # カードのレアリティに基づいて割引率を抽選
                    discount_percent = draw_discount(i,card["Rarity"])
                end

                # 割引後のコスト計算
                discounted_cost = @dia_cost
                if discount_percent > 0
                    discounted_cost = (@dia_cost * (1 - discount_percent / 100.0)).ceil
                end

                daily_shop_cards << {
                    uid: i+1,
                    card_id: card["ID"],
                    card_count: ConstantService::DAILY_SHOP_CARD_SALES_COUNT,
                    dia_cost: discounted_cost,
                    original_cost: @dia_cost,
                    discount_percent: discount_percent
                }
            end

            unless SaveDataService.update_user_save_data_hash?(user, "shop",{
                daily_shop: {
                    bought_uids: [],
                    items: daily_shop_cards,
                    update_count: 0
                }
            })
                raise StandardError.new("failed_to_update_daily_shop_cards")
            end
            user.save!
        rescue => e
            raise StandardError.new(e.message)
        end
    end

    def self.buy_daily_shop(user,user_lang,os,uid)
        begin
            cards = get_daily_shop_cards(user)[:items]
            card = cards.find { |card| card[:uid] == uid }
            user_daily_shop_status = user.user_save_data&.dig("shop", "daily_shop") || {}
            if card.nil?
                raise StandardError.new("daily_shop_card_not_found")
            end
            if card[:is_bought]
                raise StandardError.new("already_bought_daily_shop_card")
            end
            # ユーザーのデイリーショップのカードを購入
            user_daily_shop_status["bought_uids"] << card[:uid]
            unless DiaService.consume_dia?(user,os, card[:need_dia])
                raise StandardError.new("not_enough_dia")
            end

            ItemService.add_rewards?(user,card[:rewards])
            shop_result = ShopService.get_shop_index(user,user_lang,os)
            if shop_result[:error_type].present?
                raise StandardError.new("failed_to_get_shop_index")
            end
            user.save!
            return {
                rewards: card[:rewards],
                shop: shop_result[:shop],
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "buy_daily_shop")
        end
    end

    private

    def self.set_card_data()
        begin
            if MasterDatum.order(created_at: :desc).first.nil?
                raise StandardError.new("no_master_data")
            end
            @all_cards = MasterDatum.order(created_at: :desc).first.content["_cards"]
            cards_by_rarity = @all_cards.select { |card| card["ID"] > 10000 }.group_by { |card| card["Rarity"].to_i }
            @bronze1_cards = cards_by_rarity[1] || []
            @silver2_cards = cards_by_rarity[2] || []
            @gold3_cards = cards_by_rarity[3] || []
            @legend4_cards = cards_by_rarity[4] || []
        rescue StandardError => e
            raise StandardError.new(e.message)
        end
    end

    def self.draw_daily_card(index,user)
        begin
            rarity = draw_rarity(index,user)
            @dia_cost = ConstantService.get("shop.daily_shop_dia_costs")[rarity.to_sym] || ConstantService.get("shop.daily_shop_dia_costs")[rarity.to_s]
            case rarity.to_s
            when "bronze1"
                return @bronze1_cards.sample
            when "silver2"
                return @silver2_cards.sample
            when "gold3"
                return @gold3_cards.sample
            when "legend4"
                return @legend4_cards.sample
            else
                return
            end
        rescue StandardError => e
            raise StandardError.new(e.message)
        end
    end

    def self.draw_rarity(index,user)
        begin
            # デイリーショップのカードをランダムに選択
            param_setting = {
                "daily_shop_probabilities_first" => ConstantService.get("shop.daily_shop_probabilities_first"),
                "daily_shop_probabilities_other" => ConstantService.get("shop.daily_shop_probabilities_other"),
                "daily_shop_dia_costs" => ConstantService.get("shop.daily_shop_dia_costs")
            }
            if index == 0
                probabilities = param_setting["daily_shop_probabilities_first"]
            else
                probabilities = param_setting["daily_shop_probabilities_other"]
            end
            # 7日ごとにlegendカードが出るようにする
            login_days = user.login_days || 0
            rest_days = login_days % 7
            is_fixed_legendary = rest_days == 0
            if index == 5 && is_fixed_legendary
                return "legend4"
            end

            # 確率に従ってカードを選択
            act_prob = 0
            random_value = rand * 100
            probabilities.each do |rarity, prob|
                act_prob += prob
                if random_value <= act_prob
                    return rarity
                end
            end
        rescue StandardError => e
            raise StandardError.new("failed_to_draw_rarity")
        end
    end

    def self.draw_discount(index,rarity_key)
        begin
            # レアリティに応じた割引率の確率を取得
            discount_key = 'shop.daily_shop_discount_draw_probabilities_' + rarity_key.to_s

            probabilities = ConstantService.get(discount_key)
            return 0 unless probabilities.present?

            # 確率に従って割引率を選択
            act_prob = 0
            before_discount_percent = 0
            random_value = rand * 100
            if index == 0
                return 100
            else
                probabilities.each do |discount_str, prob|
                    act_prob += prob
                    # "per0", "per20" などの文字列から数値だけを取り出す
                    discount_percent = discount_str.to_s.gsub('per', '').to_i
                    if random_value < act_prob
                        return discount_percent
                    end
                end
            end

            # デフォルトは割引なし
            return 0
        rescue StandardError => e
            raise StandardError.new("failed_to_draw_discount")
        end
    end

    def self.pull_discount(original_cost, discount_percent)
        begin
            return {
                original_cost: original_cost,
                discount_percent: discount_percent,
                discounted_cost: (original_cost * (1 - discount_percent / 100.0)).ceil
            }
        rescue StandardError => e
            raise StandardError.new("failed_to_pull_discount")
        end
    end
end