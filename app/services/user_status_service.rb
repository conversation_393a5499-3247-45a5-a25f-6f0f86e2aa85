require 'json'

# Redis-based UserStatusService for high performance and low load
# 処理速度と処理負荷の低いRedis-onlyアーキテクチャ
module UserStatusService
  # Redis key patterns
  SESSION_KEY_PREFIX = "player_session"
  ACTIVE_PLAYERS_SET = "active_players"
  SESSION_EXPIRE = 1800 # 30 minutes

  class << self
    # Redis-based status update (no database, no in-memory storage)
    def update_status(user_id, status, metadata = {})
      return false unless user_id && !user_id.to_s.empty?

      session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

      # Prepare session data
      session_data = {
        player_id: user_id,
        status: status,
        updated_at: Time.now.to_i,
        metadata: metadata.to_json
      }

      begin
        # Redis operations - fast and persistent
        Redis.current.multi do |multi|
          multi.hset(session_key, session_data)
          multi.expire(session_key, SESSION_EXPIRE)
          multi.sadd(ACTIVE_PLAYERS_SET, user_id)
        end

        log_debug("Updated status for user #{user_id} → #{status}")
        true
      rescue => e
        log_error("Error updating status for user #{user_id}: #{e.message}")
        false
      end
    end

    # Redis-based status retrieval (no database queries)
    def get_status(user_id)
      return nil unless user_id && !user_id.to_s.empty?

      session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

      begin
        session_data = Redis.current.hgetall(session_key)
        return nil if session_data.empty?

        # Parse Redis data
        {
          status: session_data['status'],
          updated_at: Time.at(session_data['updated_at'].to_i),
          metadata: JSON.parse(session_data['metadata'] || '{}')
        }
      rescue => e
        log_error("Error getting status for user #{user_id}: #{e.message}")
        nil
      end
    end

    # Redis-based users by status (NO DATABASE QUERIES!)
    def get_users_by_status(status)
      result = {}

      begin
        # Get all active players from Redis set
        active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
        return result if active_players.empty?

        # Pipeline for performance - get all sessions at once
        session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

        sessions = Redis.current.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end

        # Filter by status and build result (NO DATABASE QUERIES)
        active_players.each_with_index do |player_id, index|
          session_data = sessions[index]
          next if session_data.empty?

          if session_data['status'] == status.to_s
            result[player_id] = {
              player_id: player_id,
              status: session_data['status'],
              updated_at: Time.at(session_data['updated_at'].to_i),
              metadata: JSON.parse(session_data['metadata'] || '{}')
            }
          end
        end
      rescue => e
        log_error("Error getting users by status #{status}: #{e.message}")
      end

      result
    end

    # Redis-based statistics (high performance)
    def get_stats
      stats = {
        total: 0,
        by_status: {}
      }

      begin
        # Get all active players from Redis
        active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
        stats[:total] = active_players.size

        return stats if active_players.empty?

        # Pipeline to get all statuses efficiently
        session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

        sessions = Redis.current.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'status') }
        end

        # Count by status
        sessions.each do |status|
          next if status.nil? || status.empty?

          stats[:by_status][status] ||= 0
          stats[:by_status][status] += 1
        end
      rescue => e
        log_error("Error getting stats: #{e.message}")
      end

      stats
    end

    # Redis-based all statuses (for admin dashboard)
    def get_all_statuses
      result = {}

      begin
        # Get all active players
        active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
        return result if active_players.empty?

        # Pipeline for performance
        session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

        sessions = Redis.current.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hgetall(key) }
        end

        # Build result hash
        active_players.each_with_index do |player_id, index|
          session_data = sessions[index]
          next if session_data.empty?

          result[player_id] = {
            status: session_data['status'],
            updated_at: Time.at(session_data['updated_at'].to_i),
            metadata: JSON.parse(session_data['metadata'] || '{}')
          }
        end
      rescue => e
        log_error("Error getting all statuses: #{e.message}")
      end

      result
    end

    # Redis-based disconnect handling
    def handle_disconnect(user_id)
      return false unless user_id && !user_id.to_s.empty?

      session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

      begin
        # Remove from Redis
        Redis.current.multi do |multi|
          multi.del(session_key)
          multi.srem(ACTIVE_PLAYERS_SET, user_id)
        end

        log_info("User #{user_id} disconnected, session removed from Redis")
        true
      rescue => e
        log_error("Error handling disconnect for user #{user_id}: #{e.message}")
        false
      end
    end

    # Redis-based count by status
    def count_by_status(status)
      begin
        active_players = Redis.current.smembers(ACTIVE_PLAYERS_SET)
        return 0 if active_players.empty?

        session_keys = active_players.map { |player_id| "#{SESSION_KEY_PREFIX}_#{player_id}" }

        statuses = Redis.current.pipelined do |pipeline|
          session_keys.each { |key| pipeline.hget(key, 'status') }
        end

        statuses.count { |s| s == status.to_s }
      rescue => e
        log_error("Error counting by status #{status}: #{e.message}")
        0
      end
    end

    # Add remove_status method for controller compatibility
    def remove_status(user_id)
      handle_disconnect(user_id)
    end

    private

    # Logging methods
    def log_info(message)
      log_message(:info, message)
    end

    def log_debug(message)
      log_message(:debug, message)
    end

    def log_error(message)
      log_message(:error, message)
    end

    def log_message(level, message)
      if defined?(Rails) && Rails.logger
        Rails.logger.send(level, "[UserStatusService] #{message}")
      else
        puts "[#{level.upcase}] [UserStatusService] #{message}"
      end
    end
  end

  # Optimized broadcast for admin dashboard (Redis-based)
  def self.broadcast_to_admin
    begin
      stats = get_stats

      # Get users by status efficiently (single Redis call per status)
      matching_users = get_users_by_status('matching')
      matched_users = get_users_by_status('matched')
      in_room_users = get_users_by_status('in_room')

      data = {
        matching: matching_users,
        matched: matched_users,
        in_room: in_room_users,
        stats: stats,
        timestamp: Time.now.to_i
      }

      ActionCable.server.broadcast("admin_user_status_updates", data)
      log_debug("Broadcasted admin update with #{stats[:total]} total users")
    rescue => e
      log_error("Error broadcasting to admin: #{e.message}")
    end
  end

  # Simplified broadcast method (no automatic broadcasting on every update)
  def self.broadcast_status_update
    broadcast_to_admin
  end

  # Get user metadata from Redis
  def self.get_user_metadata(user_id)
    session_key = "#{SESSION_KEY_PREFIX}_#{user_id}"

    begin
      metadata_json = Redis.current.hget(session_key, 'metadata')
      return nil if metadata_json.nil? || metadata_json.empty?

      JSON.parse(metadata_json)
    rescue => e
      log_error("Error getting user metadata for #{user_id}: #{e.message}")
      nil
    end
  end
end
