# Google Play APIとの対話を処理するサービス
require 'google/apis/androidpublisher_v3'
require 'googleauth'
require 'base64'
require 'net/http'
require 'uri'
require 'json'
require 'stringio'

class GooglePlayService
  # APIアクセス権限の範囲
  SCOPE = 'https://www.googleapis.com/auth/androidpublisher'
  # Google Play上のアプリケーションパッケージ名
  PACKAGE_NAME = Rails.application.credentials.google_play[:package_name]

  # サービスを初期化し、アクセストークンを取得します
  def initialize

    json_key = Rails.application.credentials.google_play[:service_account_json]
    return unless json_key.present?
    json_key_io = StringIO.new(json_key)

    authorization = Google::Auth::ServiceAccountCredentials.make_creds(
    json_key_io: json_key_io,
    scope: 'https://www.googleapis.com/auth/androidpublisher'
    )

    @service = Google::Apis::AndroidpublisherV3::AndroidPublisherService.new
    @service.authorization = authorization

  end

  # Google Playでサブスクリプション情報を確認します
  #
  def verify_purchase_subscription(subscription_id, token,package_name)
    response = @service.get_purchase_subscription(package_name, subscription_id, token)
  rescue => e
      Rails.logger.error("Google Play API error: #{e.message}")
      nil
  end

  def verify_purchase_product(product_id, token,package_name)
    response = @service.get_purchase_product(package_name, product_id, token)
  rescue => e
    Rails.logger.error("Google Play API error: #{e.message}")
    nil
  end

  # Google Playでサブスクリプションを確認します
  def acknowledge_subscription(subscription_id, token, developer_payload = '',package_name)
    response = @service.acknowledge_purchase_subscription(package_name, subscription_id, token, developer_payload)
  rescue => e
    Rails.logger.error("Google Play API error: #{e.message}")
    nil
  end

  # Google Playで製品購入を確認します
  def acknowledge_product_purchase(product_id, token, developer_payload = '',package_name)
    response = @service.acknowledge_purchase_product(package_name, product_id, token, developer_payload)
  rescue => e
    Rails.logger.error("Google Play API error: #{e.message}")
    nil
  end

    # Google Cloud Pub/Subからの通知を処理します
  #
  # def handle_pubsub_message(data)
  #   subscription_id = data['subscriptionId']
  #   token = data['purchaseToken']

  #   case data['notificationType']
  #   when 2  # Subscription renewed
  #     update_subscription_status(subscription_id, token)
  #   when 3  # Subscription canceled
  #     cancel_subscription(subscription_id, token)
  #   else
  #     Rails.logger.warn("Unknown notification type: #{data['notificationType']}")
  #   end
  # end

  # Google Playからの情報に基づいてサブスクリプションステータスを更新します
  # def update_subscription_status(subscription_id, token)
  #   result = verify_subscription(subscription_id, token)
  #   return false unless result

  #   subscription_data = {
  #     subscription_id: subscription_id,
  #     purchase_token: token,
  #     start_date: Time.at(result['startTimeMillis'].to_i / 1000),
  #     end_date: Time.at(result['expiryTimeMillis'].to_i / 1000),
  #     auto_renew: result['autoRenewing'],
  #     status: Subscription.map_google_status(result['paymentState']),
  #     amount: result['priceAmountMicros'].to_i / 1_000_000.0,
  #     currency: result['priceCurrencyCode'],
  #     payment_status: Payment.map_google_status(result['paymentState']),
  #     transaction_id: result['orderId']
  #   }

  #   SubscriptionService.update_subscription_status(subscription_data)
  # end

  # Google Playからの情報に基づいてサブスクリプションをキャンセルします
  # def cancel_subscription(subscription_id, token)
  #   result = verify_subscription(subscription_id, token)
  #   return false unless result

  #   SubscriptionService.cancel_subscription(token)
  # end

  # Google Playからの情報に基づいてサブスクリプションを作成または更新します
  # def upsert_subscription(subscription_id, token, user_id)
  #   result = verify_subscription(subscription_id, token)
  #   return false unless result

  #   # 必要に応じてサブスクリプションを確認します
  #   if result['acknowledgementState'] == 0 && result['paymentState'].present? && result['paymentState'] == 1
  #     acknowledge_subscription(subscription_id, token, "user_id: #{user_id}")
  #   end

  #   subscription_data = {
  #     subscription_id: subscription_id,
  #     purchase_token: token,
  #     start_date: Time.at(result['startTimeMillis'].to_i / 1000),
  #     end_date: Time.at(result['expiryTimeMillis'].to_i / 1000),
  #     auto_renew: result['autoRenewing'],
  #     status: Subscription.map_google_status(result['paymentState']),
  #     provider: :google,
  #     amount: result['priceAmountMicros'].to_i / 1_000_000.0,
  #     currency: result['priceCurrencyCode'],
  #     payment_status: Payment.map_google_status(result['paymentState']),
  #     transaction_id: result['orderId']
  #   }

  #   SubscriptionService.upsert_subscription(subscription_data, user_id)
  # end

  # Google Play上のサブスクリプション製品を無効化します
  # def deactivate_subscription_product(product_id)
  #   uri = URI.parse("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/#{PACKAGE_NAME}/subscriptions/#{PRODUCT_ID}")
  #   request = Net::HTTP::Post.new(uri)
  #   request['Authorization'] = "Bearer #{@access_token}"
  #   request['Content-Type'] = 'application/json'

  #   response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
  #     http.request(request)
  #   end

  #   case response
  #   when Net::HTTPSuccess
  #     JSON.parse(response.body)
  #   else
  #     Rails.logger.error("HTTP Request failed (status: #{response.code}): #{response.body}")
  #     nil
  #   end
  # end

  # Google Play上のサブスクリプション製品を有効化します
  # def activate_subscription_product
  #   uri = URI.parse("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/#{PACKAGE_NAME}/subscriptions/#{PRODUCT_ID}/basePlans/#{BASE_PLAN_ID}:activate")
  #   request = Net::HTTP::Post.new(uri)
  #   request['Authorization'] = "Bearer #{@access_token}"
  #   request['Content-Type'] = 'application/json'

  #   response = Net::HTTP.start(uri.hostname, uri.port, use_ssl: true) do |http|
  #     http.request(request)
  #   end

  #   case response
  #   when Net::HTTPSuccess
  #     JSON.parse(response.body)
  #   else
  #     Rails.logger.error("HTTP Request failed (status: #{response.code}): #{response.body}")
  #     nil
  #   end
  # end

  # サブスクリプションモードを切り替えます（有効化/無効化）
  # def toggle_subscription_mode
  #   system = System.first_or_create
  #   result = if system.subscription_mode
  #              deactivate_subscription_product()
  #            else
  #              activate_subscription_product
  #            end
  #   unless result.nil?
  #     system.update(subscription_mode: !system.subscription_mode)
  #     return true
  #   end
  #   false
  # end
end
