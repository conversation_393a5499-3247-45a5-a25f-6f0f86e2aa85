class LoginBonusService
    extend ProcessFormatRewards
    #  ログインボーナスのrewardsはそのイベントすべてアイテムが入っており、１日に１アイテムしかできない(複数アイテムは無理)
    def self.get_login_bonus(user,user_lang)
        begin
            # 期間内のログインボーナスを取得
            login_bonus_list = LoginBonus.within_valid_period.order_by_period_start_at
            # if login_bonus_list.empty?
            #     raise StandardError.new("login_bonus_not_found")
            # end
            login_bonus = []
            login_bonus_list.each do |lb|
                # イベントログインボーナスの場合は、uidと最後に取得したindexを保存する
                if lb.is_event
                    # ログインボーナス情報を取得
                    event_info = get_event_login_bonus_info(user, lb.uid)
                    last_index = event_info[:last_index]
                    last_receive_at = event_info[:last_receive_at]

                    if last_index.nil?
                        last_index = -1 # ログインボーナスが存在しないときは-1を示す
                    end
                    if last_index + 1 >= lb.rewards.length
                        next # すべてのログインボーナスを受け取っていた場合は表示しない
                    end
                else # 通常ログインボーナスの場合
                    normal_info = get_normal_login_bonus_info(user)
                    normal_count = normal_info[:normal_count]
                    if normal_count.nil?
                        normal_count = 0
                    end
                    last_receive_at = normal_info[:last_receive_at]
                    last_index = normal_count % 7 - 1 # index表記にするために-1をする
                end

                # すでに本日のログボを受け取っていた場合はスキップ
                unless check_first_time?(last_receive_at)
                    next
                end
                # ログインボーナスの内容を作成
                contents = []
                lb.rewards.each_with_index do |bonus,index|
                    if index > last_index
                        is_received = false
                    else
                        is_received = true
                    end
                    rewards = [bonus] # 中身が一つでも配列にする
                    contents << {
                        rewards: process_format_rewards(rewards),
                        is_received: is_received
                    }
                end

                login_bonus <<{
                    content: contents,
                    uid: lb.uid,
                    event_name: lb.title[user_lang],
                    event_desc: lb.desc[user_lang],
                    start_at: lb.period_start_at,
                    end_at: lb.period_end_at,
                    is_event: lb.is_event,
                    }
            end
            return {login_bonuses: login_bonus}
        rescue => e
            ErrorService.handle_exception(e, Api::LoginBonusesController, "get_login_bonus")
        end
    end

    def self.receive_event_login_bonus(user,uid,user_lang)
        begin
            login_bonus_list = LoginBonus.within_valid_period.order_by_period_start_at.where(is_event: true)
            if login_bonus_list.empty?
                raise StandardError.new("login_bonus_not_found")
            end
            login_bonus = login_bonus_list.find { |lb| lb.uid == uid }
            if login_bonus.nil?
                raise StandardError.new("login_bonus_not_found")
            end
            # 最後のログインボーナス受け取り日を取得
            last_bonus_info = get_event_login_bonus_info(user, uid)
            last_receive_at = last_bonus_info[:last_receive_at]
            last_index = last_bonus_info[:last_index]

            # 最後の受け取り日がある場合、その日の深夜4時以降かどうかをチェック
            unless check_first_time?(last_receive_at)
                raise StandardError.new("login_bonus_not_available")
            end
            # 今回受け取るログインボーナスのインデックス
            expected_index = last_index + 1
            if login_bonus.rewards.length <= expected_index
                raise StandardError.new("received_all_login_bonus")
            end

            # uidが異なる場合は新しく追加し、同じ場合は上書きをする
            u_event_info = user.user_save_data.dig("login_bonuses","event") || []
            # 同じuidの情報があるかを確認
            existing_event = u_event_info.find { |info| info["uid"] == uid }
            if existing_event
                # 既存のイベント情報がある場合は上書き
                existing_event["last_index"] = expected_index
                existing_event["last_receive_at"] = Time.current.to_s
            else
                # 新しいイベント情報を追加
                u_event_info << {
                    "uid" => uid,
                    "last_index" => expected_index,
                    "last_receive_at" => Time.current.to_s
                }
            end
            unless SaveDataService.update_user_save_data_hash?(user,"login_bonuses",{
                normal:user.user_save_data.dig("login_bonuses","normal") || {},
                event:u_event_info
                })
                raise StandardError.new("failed_to_update_save_data")
            end

            rewards = [login_bonus.rewards[expected_index]]

            unless ItemService.add_rewards?(user,rewards)
                raise StandardError.new("failed_to_update_item")
            end
            user.save!
            login_bonuses = get_login_bonus(user,user_lang)
            if login_bonuses[:error_type]
                return login_bonuses
            end

            receive_result = {
                rewards: rewards,
                login_bonuses: login_bonuses[:login_bonuses],
                user: UserService.get_res(user)
            }
            return receive_result
        rescue => e
            ErrorService.handle_exception(e, Api::LoginBonusesController, "receive_event_login_bonus")
        end
    end

    def self.receive_normal_login_bonus(user,user_lang)
        begin
            # 最新のログインボーナスのみ取得
            login_bonus = LoginBonus.within_valid_period.where(is_event: false).order_by_period_start_at.first
            if login_bonus.nil?
                raise StandardError.new("login_bonus_not_found")
            end
            normal_info = get_normal_login_bonus_info(user)
            normal_count = normal_info[:normal_count] || 0
            last_receive_at = normal_info[:last_receive_at]
            unless check_first_time?(last_receive_at)
                raise StandardError.new("login_bonus_not_available")
            end
            last_index = normal_count % 7 - 1
            expected_index = last_index + 1
            if login_bonus.rewards.length <= expected_index
                raise StandardError.new("received_all_login_bonus")
            end

            unless SaveDataService.update_user_save_data_hash?(user,"login_bonuses",{
                normal:{
                    got_count: normal_count + 1,
                    last_receive_at: Time.current.to_s
                },
                event:user.user_save_data.dig("login_bonuses","event") || []
            })
                raise StandardError.new("failed_to_update_save_data")
            end

            rewards = [login_bonus.rewards[expected_index]]

            unless ItemService.add_rewards?(user,rewards)
                raise StandardError.new("failed_to_update_item")
            end
            user.save!

            login_bonuses = get_login_bonus(user,user_lang)
            if login_bonuses[:error_type]
                return login_bonuses
            end

            receive_result = {
                rewards: rewards,
                login_bonuses: login_bonuses[:login_bonuses],
                user: UserService.get_res(user)
            }
            return receive_result

        rescue => e
            ErrorService.handle_exception(e, Api::LoginBonusesController, "receive_normal_login_bonus")
        end
    end

    private

    # uidを指定してログインボーナス情報を取得する
    def self.get_event_login_bonus_info(user, uid)
        login_bonuses = user.user_save_data.dig("login_bonuses","event") || []
        begin
            login_bonus = login_bonuses.find { |login_bonus| login_bonus["uid"] == uid }
            if login_bonus.nil?
                return {
                    last_index: -1,
                    last_receive_at: ""
                }
            end
            return {
                last_index: login_bonus["last_index"] || -1,
                last_receive_at: login_bonus["last_receive_at"]
            }
        rescue => e
            raise e
        end
    end

    # 通常ログインボーナス情報を取得する
    def self.get_normal_login_bonus_info(user)
        login_bonuses = user.user_save_data.dig("login_bonuses","normal") || {}
        normal_count = login_bonuses["got_count"]
        last_receive_at = login_bonuses["last_receive_at"]
        return {
            normal_count: normal_count,
            last_receive_at: last_receive_at
        }
    end

    # 初回ログインボーナスを受け取るかどうかをチェックする
    def self.check_first_time?(last_receive_at)
        last_update_time = LoginService.last_update_login_time
        if last_receive_at.present?
            last_receive_at < last_update_time
        else
            true
        end
    end
end