require 'benchmark'

class PackService
    # パック一覧取得
    def self.index_packs(user,user_lang)
        begin
            # TODO メインパックやスペシャルパックの処理を書く
            packs = []
            # シンプルにsort_orderで昇順ソート（データベースのデフォルト動作に任せる）
            pack_list = Pack.within_valid_period.order(sort_order: :asc)
            u_ticket_info = ItemService.get_pack_ticket(user)
            u_ticket_ids = u_ticket_info.keys.map { |pack_id| pack_id.to_i }
            pack_list.each do |pack|
                u_ticket_num = u_ticket_info.dig(pack.uid.to_s, "count") || 0
                if pack.enable_dia && pack.enable_ticket
                    category = "dia_ticket"
                elsif pack.enable_dia
                    category = "dia"
                elsif pack.enable_ticket
                    category = "ticket"
                else
                    next
                end
                # チケットを所持していない場合はスキップ
                if category == "ticket" && !pack.is_visible && !u_ticket_ids.include?(pack.uid)
                    next
                else
                    packs.push({
                    uid: pack.uid,
                    name: pack.name[user_lang],
                    desc: pack.desc[user_lang],
                    cards_per_pack: pack.cards_per_pack,
                    dia_cost: pack.dia_cost,
                    card_data: set_card_data(pack.card_data.flatten),
                    skin_data: set_skin_data(pack.skin_data.flatten),
                    rarity_probabilities: set_rarity_probabilities(pack),
                    main_card_id: pack.main_card_id,
                    category: category,
                    ticket_num: u_ticket_num,
                    start_at: pack.period_start_at,
                    end_at: pack.period_end_at,
                    enable_pts: pack.enable_pts,
                    per_skin_probability: pack.per_skin_prob.to_f,
                    edition_probabilities: set_edition_probabilities(pack),
                    is_special: pack.is_special,
                    is_main: pack.is_main,
                    })
                end
            end
            return {packs: packs}
        rescue => e
            ErrorService.handle_exception(e, Api::PacksController, "index_packs")
        end
    end

    # パック内カード一覧取得
    def self.index_cards(pack_id,user,os)
        begin
            pack = Pack.find_by(uid: pack_id)
            if pack.nil?
                return {error_type: "pack_not_found", error: "パックが見つかりません"}
            end
            cards = pack.card_data.flatten
            card_json = cards.map do |card|
                {
                    card_id: card["id"],
                    rarity: card["rarity"],
                    probability: card["probability"].to_f,
                    need_pts: card["need_pts"],
                    need_dia: card["need_dia"]
                }
            end
            user_pack_info = get_user_pack_info(user,pack_id)
            cur_pts = user_pack_info["pts"]
            cur_dia = DiaService.fetch_current_dia(user,os)
            return {cards: card_json, cur_pts: cur_pts, cur_dia: cur_dia}
        rescue => e
            ErrorService.handle_exception(e, Api::PacksController, "index_cards")
        end
    end

    # パック購入
    def self.buy_pack(user,os,pack_id,count,category)
        begin
            pack = Pack.find_by(uid: pack_id)
            if pack.nil?
                raise StandardError.new("pack_not_found")
            end
            # カテゴリーによって処理を分岐
            if category == "dia"
                if !pack.enable_dia
                    raise StandardError.new("dia_purchase_disabled")
                end
                total_cost = count * pack.dia_cost
                # ダイヤを消費
                unless DiaService.consume_dia?(user, os, total_cost)
                    raise StandardError.new("not_enough_dia")
                end
            elsif category == "ticket" # TODO チケット購入を完璧実装にする
                if !pack.enable_ticket
                    raise StandardError.new("ticket_purchase_disabled")
                end
                unless ItemService.consume_pack_ticket?(user,pack_id,count)
                    raise StandardError.new("not_enough_ticket")
                end
            else
                raise StandardError.new("invalid_pack_category")
            end
            user_pack_info = get_user_pack_info(user,pack_id)
            cur_pts = user_pack_info["pts"]
            cur_count = user_pack_info["count"]

            # 特殊パックの場合はポイントを加算しない
            if !pack.is_special || !pack.enable_pts
                cur_pts += ConstantService.get('pack.one_pack_points')*count
            end

            cur_count += count
            unless SaveDataService.add_user_save_data?(user, "pack", {
                pack_id: pack_id, pts: cur_pts, count: cur_count
            })
                raise StandardError.new("failed_to_add_user_save_data")
            end
            # パックを引く
            result = pull_pack(user,os,pack,count)

            if result[:error_type].present?
                return result
            end

            # ガチャ結果からリスポンスデータの作成
            # カードのエディションをnormal, shine, premiumの個数を判別
            response_data = []

            result[:results].each do |one_pack|
                card_data = []
                one_pack[:cards].each do |card|
                    normal = 0
                    shine = 0
                    premium = 0
                    case card[:edition]
                    when 1
                        normal = 1
                    when 2
                        shine = 1
                    when 3
                        premium = 1
                    end
                    card_data.push({card_id: card[:card_id], normal: normal, shine: shine, premium: premium})
                end
                response_data.push({cards: card_data, skins: one_pack[:skins], sands: one_pack[:sands]})
            end

            # ガチャ結果からカードとスキンを保存する
            picked_cards = []
            picked_skins = []
            picked_sands = []
            result[:results].each do |one_pack|
                cards = one_pack[:cards]
                skins = one_pack[:skins]
                sands = one_pack[:sands]
                cards.each do |card|
                    picked_cards.push({card_id: card[:card_id], edition: card[:edition]})
                end
                skins.each do |skin|
                    picked_skins.push({skin_id: skin[:skin_id]})
                end
                sands.each do |sand|
                    picked_sands.push({group_id: sand[:group_id], count: sand[:count]})
                end
            end

            # カード取得の場合はボックスを更新
            BoxService.update_box_from_results(user, picked_cards) # 理想は{card_id: card_id, edition: edition}の配列を渡す
            # スキン取得の場合はアイテムを更新
            SkinService.update_box_from_results(user, picked_skins) # 理想は{skin_id: skin_id}の配列を渡す
            # 砂取得の場合はアイテムを更新
            SkinService.update_sands_from_results(user, picked_sands) # 理想は{group_id: group_id, count: count}の配列を渡す
            user.save
            return {pack_result: response_data,user: UserService.get_res(user)}

        rescue => e
            ErrorService.handle_exception(e, Api::PacksController, "buy_pack")
        end
    end

    # カード購入
    def self.buy_card(user,os,pack_id,card_id,count,edition_type,category)
        begin
            pack = Pack.find_by!(uid: pack_id)
            card_data = pack.card_data.flatten
            card = card_data.find { |card| card["id"] == card_id }
            need_pts = card["need_pts"]
            need_dia = card["need_dia"]
            # TODO edition_typeによって必要なポイントが変える
            BoxService.add_card_to_box(user, card_id, edition_type, count)
            user.create_log("buy_card", { card_id: card_id, edition_type: edition_type, count: count })
            user_pack_info = get_user_pack_info(user,pack_id)
            cur_pts = user_pack_info["pts"]
            cur_count = user_pack_info["count"]
            case category
            when "dia"
                consumed_dia = need_dia*count
                unless DiaService.consume_dia?(user, os, consumed_dia)
                    raise StandardError.new("not_enough_dia")
                end
            when "pts"
                consumed_pts = need_pts*count
                if cur_pts < consumed_pts
                    raise StandardError.new("not_enough_pts")
                end
                cur_pts -= consumed_pts
                unless SaveDataService.add_user_save_data?(user, "pack", {
                    pack_id: pack_id, pts: cur_pts, count: cur_count
                })
                    raise StandardError.new("failed_to_add_user_save_data")
                end
            else
                raise StandardError.new("invalid_pack_category")
            end
            user.save
            return {
                consumed_pts: consumed_pts || 0,
                consumed_dia: consumed_dia || 0,
                card_id: card["id"],
                user: UserService.get_res(user)
            }
        rescue => e
            ErrorService.handle_exception(e, Api::PacksController, "buy_card")
        end
    end

    # スペシャルパックを引く
    def self.pull_special_pack(user,os,pack_id,count)
        begin
            pack = Pack.find_by!(uid: pack_id)
            if pack.nil? || !pack.is_special
                raise StandardError.new("special_pack_not_found")
            end

            user_pack_info = get_user_pack_info(user,pack_id)
            cur_pts = user_pack_info["pts"]
            cur_count = user_pack_info["count"]

            cur_count += count
            unless SaveDataService.add_user_save_data?(user, "pack", {
                pack_id: pack_id, pts: cur_pts, count: cur_count
            })
                raise StandardError.new("failed_to_add_user_save_data")
            end
            # パックを引く
            result = pull_pack(user,os,pack,count)

            if result[:error_type].present?
                return result
            end

            # ガチャ結果からリスポンスデータの作成
            # カードのエディションをnormal, shine, premiumの個数を判別
            response_data = []

            result[:results].each do |one_pack|
                card_data = []
                one_pack[:cards].each do |card|
                    normal = 0
                    shine = 0
                    premium = 0
                    case card[:edition]
                    when 1
                        normal = 1
                    when 2
                        shine = 1
                    when 3
                        premium = 1
                    end
                    card_data.push({card_id: card[:card_id], normal: normal, shine: shine, premium: premium})
                end
                response_data.push({cards: card_data, skins: one_pack[:skins], sands: one_pack[:sands]})
            end

            # ガチャ結果からカードとスキンを保存する
            picked_cards = []
            picked_skins = []
            picked_sands = []
            result[:results].each do |one_pack|
                cards = one_pack[:cards]
                skins = one_pack[:skins]
                sands = one_pack[:sands]
                cards.each do |card|
                    picked_cards.push({card_id: card[:card_id], edition: card[:edition]})
                end
                skins.each do |skin|
                    picked_skins.push({skin_id: skin[:skin_id]})
                end
                sands.each do |sand|
                    picked_sands.push({group_id: sand[:group_id], count: sand[:count]})
                end
            end

            # カード取得の場合はボックスを更新
            BoxService.update_box_from_results(user, picked_cards) # 理想は{card_id: card_id, edition: edition}の配列を渡す
            # スキン取得の場合はアイテムを更新
            SkinService.update_box_from_results(user, picked_skins) # 理想は{skin_id: skin_id}の配列を渡す
            # 砂取得の場合はアイテムを更新
            SkinService.update_sands_from_results(user, picked_sands) # 理想は{group_id: group_id}の配列を渡す
            return {pack_result: response_data}
        rescue => e
            ErrorService.handle_exception(e, Api::PacksController, "buy_pack")
        end
    end

    def self.fetch_current_main_pack
        # 最新のメインパックを取得
        Rails.cache.fetch('packs/current_main_pack', expires_in: 1.minute) do
            Pack.where(is_main: true).within_valid_period.order_by_period_start_at.first
        end
    end

    private
    def self.set_card_data(card_data)
        card_data_with_probability = []
        card_data.each do |card|
            if card.present?
                card_id = card["id"]
                card_rarity = card["rarity"]
                card_probability = card["probability"].to_f
                card_data_with_probability << {card_id: card_id, rarity: card_rarity, probability: card_probability}
            end
        end
        return card_data_with_probability
    end

    def self.set_skin_data(skin_data)
        skin_data_with_probability = []
        skin_data.each do |skin|
            if skin.present?
                skin_id = skin["id"]
                skin_probability = skin["probability"].to_f
                skin_data_with_probability << {skin_id: skin_id, probability: skin_probability}
            end
        end
        return skin_data_with_probability
    end

    def self.set_rarity_probabilities(pack)
        # 新しい構造のrarity_probを既存のフォーマットに変換
        rarity_map = {
            "bronze1" => 1,
            "silver2" => 2,
            "gold3" => 3,
            "legend4" => 4
        }
        
        pack_rarity_probabilities = []
        
        if pack.rarity_prob.present?
            pack.rarity_prob.each do |key, value|
                if rarity_map[key].present?
                    pack_rarity_probabilities << {
                        rarity: rarity_map[key],
                        probability: value.to_f
                    }
                end
            end
        end
        
        return pack_rarity_probabilities
    end

    def self.set_edition_probabilities(pack)
        edition_map = {
            "normal" => "nr",
            "shine" => "sh",
            "premium" => "pr"
        }
        edition_probabilities = []
        pack.edition_prob.each do |key, value|
            edition_probabilities << {
                edition: edition_map[key],
                probability: value.to_f
            }
        end
        return edition_probabilities
    end

    # パックを指定した回数分引く
    def self.pull_pack(user, os, pack, pull_count)
        cards_per_pack = pack.cards_per_pack
        selected_cards = pack.card_data
        
        # 新しい構造のrarity_probとedition_probを使用
        # デフォルト値はConstantService.getから取得
        default_rare_prob = ConstantService.get('pack.pack_default_rare_probabilities')
        default_edition_prob = ConstantService.get('pack.pack_default_edition_probabilities')
        
        rarity_probabilities = {
            r1: pack.rarity_prob["bronze1"].to_f || default_rare_prob["rarity_1"].to_f,
            r2: pack.rarity_prob["silver2"].to_f || default_rare_prob["rarity_2"].to_f,
            r3: pack.rarity_prob["gold3"].to_f || default_rare_prob["rarity_3"].to_f,
            r4: pack.rarity_prob["legend4"].to_f || default_rare_prob["rarity_4"].to_f
        }

        edition_probabilities = {
            nr: pack.edition_prob["normal"].to_f || default_edition_prob["edition_1"].to_f,
            sh: pack.edition_prob["shine"].to_f || default_edition_prob["edition_2"].to_f,
            pr: pack.edition_prob["premium"].to_f || default_edition_prob["edition_3"].to_f
        }

        results = []
        drawn_skins = []
        skin_data = pack.skin_data

        # カードの抽選
        for i in 1..pull_count do
            cards_result = []
            for j in 1..cards_per_pack do
                selected_rarity_cards = []
                selected_card = nil

                # カードレアリティの抽選
                rarity = draw_rarity(rarity_probabilities)

                # そのレアリティのカードをセット（存在する場合のみ）
                selected_cards.each do |card|
                    if card["rarity"] == rarity
                        selected_rarity_cards << card
                    end
                end

                if selected_rarity_cards.present?
                    # 選ばれたレアリティのカードからランダムに一つを選択
                    selected_card = selected_rarity_cards.sample

                    # エディションの抽選
                    edition_type = draw_edition(edition_probabilities, rarity)

                    # 結果の追加
                    cards_result << {card_id: selected_card["id"], edition: edition_type, rarity: rarity}
                end
            end

            # スキンの抽選
            skin_result = []
            sands_result = []
            # スキンが当たった場合
            picked_skin = draw_skin(pack)
            if picked_skin.nil?
                skin_result = []
                sands_result = []
            else
                skin_id = picked_skin["id"]
                # スキンを所持しているか確認
                skins_box = user.skins_box || {}
                # すでに持っているスキンの場合は砂を追加する
                if skins_box[skin_id.to_s].present? || drawn_skins.include?(skin_id)
                    group_id = MasterDatum.skin_groups(skin_id)
                    sands_result.push({ group_id: group_id, count: ConstantService.get('skin.sand_count_group') || 100})
                else
                    # 新しいスキンの場合はそのまま追加
                    skin_result.push({ skin_id: skin_id })
                    drawn_skins.push(skin_id)
                end
            end
            results << {cards: cards_result, skins: skin_result, sands: sands_result}
        end
        return {results: results}
    end

    # レアリティの抽選
    def self.draw_rarity(rarity_probabilities)
        r1 = rarity_probabilities[:r1]
        r2 = rarity_probabilities[:r2]
        r3 = rarity_probabilities[:r3]
        r4 = rarity_probabilities[:r4]
        
        total = r1 + r2 + r3 + r4
        # 小数点以下まで正確に扱うために乱数を0〜total範囲の浮動小数点で生成
        random = rand * total
        
        if random < r4
            return 4 # Legendary
        elsif random < r4 + r3
            return 3 # Epic
        elsif random < r4 + r3 + r2
            return 2 # Rare
        else
            return 1 # Normal
        end
    end

    # スキンの抽選
    def self.draw_skin(pack)
        # skinが当たるか抽選
        rand_val = rand*100
        if rand_val < pack.per_skin_prob
            return pack.skin_data.sample # スキンは当確率で当たる。ピックアップとかはない
        else
            return nil
        end
    end

    # エディションの抽選
    def self.draw_edition(edition_probabilities, rarity = nil)
        e1 = edition_probabilities[:nr]  # ノーマル
        e2 = edition_probabilities[:sh]  # シャイン
        e3 = edition_probabilities[:pr]  # プレミアム
        
        # レアリティが3または4の場合のみプレミアムを出現させる
        if rarity && (rarity != 3 && rarity != 4)
            # レアリティが1または2の場合はプレミアムの確率をノーマルに振り分ける
            e1 = e1 + e3  # ノーマルにプレミアムの確率を加算
            e3 = 0        # プレミアムの確率を0に
        end
        
        total = e1 + e2 + e3
        # 小数点以下まで正確に扱うために乱数を0〜total範囲の浮動小数点で生成
        random = rand * total
        
        if random < e3
            return 3  # Premium
        elsif random < e3 + e2
            return 2  # Shine
        else
            return 1  # Normal
        end
    end
    
    # ユーザーのパック情報を取得
    def self.get_user_pack_info(user,pack_id)
        user_pack_infos = user.user_save_data["pack"] || []
        user_pack_info = user_pack_infos.find { |d| d["pack_id"] == pack_id } || {"pack_id" => pack_id, "pts" => 0, "count" => 0}
        return user_pack_info
    end
end