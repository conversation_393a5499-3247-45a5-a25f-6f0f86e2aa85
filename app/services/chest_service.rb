class ChestService

    def self.on_new_day(user)
        init_chest(user)
    end

    def self.add_win_count(user)
        daily_win_count = user.user_save_data["details"]["daily_win_count"]
        unless SaveDataService.update_user_save_data_hash?(user, "details", {daily_win_count: daily_win_count + 1})
            raise StandardError.new("failed_to_update_user_save_data")
        end
        user.save!
        return {
            "daily_win_count" => user.user_save_data["details"]["daily_win_count"]
        }
    rescue StandardError => e
        ErrorService.handle_exception(e, Api::ChestsController, "add_win_count")
    end

    def self.open_chest(user,uid)
        u_chests_data = fetch_and_update_user_chests(user)
        u_chest = u_chests_data.find { |chest| chest[:uid] == uid }
        chest = Chest.find_by(category: u_chest[:category])
        if chest.blank?
            raise StandardError.new("chest_not_found")
        end
        if u_chest[:status] == "received"
            raise StandardError.new("chest_already_opened")
        end
        if u_chest[:status] == "unachieved"
            raise StandardError.new("chest_not_achieved")
        end
        new_rewards = generate_chest_rewards(chest)

        unless ItemService.add_rewards?(user,new_rewards)
            raise StandardError.new("failed_to_add_rewards")
        end
        u_chest[:status] = "received"
        unless SaveDataService.update_user_save_data_array?(user, "chests",u_chests_data)
            raise StandardError.new("failed_to_update_user_save_data")
        end
        user.save!
        return {
            rewards: new_rewards,
            user: UserService.get_res(user)
        }
    rescue StandardError => e
        ErrorService.handle_exception(e, Api::ChestsController, "open_chest")
    end

    def self.fetch_and_update_user_chests(user)
        begin
            u_chests_data =  user.user_save_data.dig("chests") # [{category: "wood",status:"unachieved"}*4]
            # 宝箱データがない場合は初期化
            if u_chests_data.blank?
                result = init_chest(user)
                if result&.has_key?("error_type")
                    raise StandardError.new(result["error_type"])
                end
                u_chests_data =  user.user_save_data.dig("chests")
            end
            daily_win_count = user.user_save_data.dig("details","daily_win_count") || 0
            items = []
            u_chests_data.each_with_index do |chest,index|
                status = chest["status"]
                if status == "received"
                    chest["status"] = "received"
                elsif daily_win_count >= index
                    chest["status"] = "unreceived"
                else
                    chest["status"] = "unachieved"
                end
                items << {
                    uid: index+1,
                    category: chest["category"],
                    status: chest["status"]
                }
            end
            unless SaveDataService.update_user_save_data_array?(user, "chests",items)
                raise StandardError.new("failed_to_update_user_save_data")
            end
            user.save!
            return items
        rescue StandardError => e
            Rails.logger.error("failed_to_get_chests: #{e.message}")
            raise StandardError.new("failed_to_get_chests")
        end
    end

    def self.init_chest(user)
        begin
            # パラメータの取得
            chest_prob = ConstantService.get("home.chest_probabilities")

            # 宝箱の種類の抽選
            total_prob = chest_prob.values.sum
            chests = []
            ConstantService::CHESTS_COUNT.times do |index|
                rand_prob = rand(total_prob)
                sum_prob = 0
                new_chest_cat = ""
                chest_prob.each do |category, prob|
                    sum_prob += prob
                    if rand_prob < sum_prob
                        new_chest_cat = category.to_s
                        break
                    end
                end
                chests << {category: new_chest_cat, status: "unachieved", uid: index+1}
            end
            unless SaveDataService.update_user_save_data_array?(user, "chests", chests)
                raise StandardError.new("failed_to_update_user_save_data")
            end
            unless SaveDataService.update_user_save_data_hash?(user, "details", {daily_win_count: 0})
                raise StandardError.new("failed_to_update_user_save_data")
            end
            user.save!
            return {"success": true}
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ChestsController, "init_chest")
        end
    end

    private

    def self.draw_rewards?(ext)
        prob = ext.dig("rate") || 100
        return rand*100 < prob
    end

    def self.draw_count(ext)
        min = ext.dig("min") || 0
        max = ext.dig("max") || 0
        return rand(min..max).to_i
    end

    def self.generate_chest_rewards(chest)
        new_rewards = chest.rewards.map do |reward|
            item_type = reward["item_type"] || reward[:item_type]
            item_id = reward["item_id"] || reward[:item_id]
            count = reward["count"] || reward[:count]
            ext = reward["ext"] || reward[:ext]
            # 確率チェック
            if ext.dig("rate").present?
                next nil unless draw_rewards?(ext)
                # extから関連するキーを削除
                ext.delete("rate")
            end
            # 個数の範囲チェック
            if ext.dig("use_range")
                count = draw_count(ext)
                # extから関連するキーを削除
                ext.delete("use_range")
                ext.delete("min")
                ext.delete("max")
            end
            {
                "item_type" => item_type,
                "item_id" => item_id,
                "count" => count,
                "ext" => ext
            }
        end
        # 空の要素を削除
        new_rewards.compact!
        return new_rewards
    end
end
