class ShopBonusService
    # 累計チャージと累計消費チャージの一覧取得
    def self.get_shop_bonuses(user)
        shop_bonuses = ShopBonus.within_valid_period.order_by_period_start_at
        charged_bonus_list = []
        consumed_bonus_list = []

        # ボーナス関連のデータ取得
        charged_bonuses = shop_bonuses.where(category: "charged_bonus")
        consumed_bonuses = shop_bonuses.where(category: "consumed_bonus")
        # ユーザーのボーナス関連の情報取得
        user_charged_bonus_ids = user.user_save_data.dig("shop", "bonus", "charged_bonuses") || []
        user_consumed_bonus_ids = user.user_save_data.dig("shop", "bonus", "consumed_bonuses") || []
        user_total_charged_dia = user.user_save_data.dig("shop", "bonus", "total_charged_dia") || 0
        user_total_consumed_dia = user.user_save_data.dig("shop", "bonus", "total_consumed_dia") || 0

        charged_bonuses.each do |charged_bonus|
            status = user_charged_bonus_ids.include?(charged_bonus.uid) ? "received" : "unreceived"
            need_dia = charged_bonus.need_dia || 0
            if status == "unreceived"
                status = "unachieved" if user_total_charged_dia < need_dia
            end
            charged_bonus_list << {
                uid: charged_bonus.uid,
                rewards: charged_bonus.rewards,
                need_dia: need_dia,
                status: status
            }
        end
        consumed_bonuses.each do |consumed_bonus|
            status = user_consumed_bonus_ids.include?(consumed_bonus.uid) ? "received" : "unreceived"
            need_dia = consumed_bonus.need_dia || 0
            if status == "unreceived"
                status = "unachieved" if user_total_consumed_dia < need_dia
            end
            consumed_bonus_list << {
                uid: consumed_bonus.uid,
                rewards: consumed_bonus.rewards,
                need_dia: need_dia,
                status: status
            }
        end
        SaveDataService.update_user_save_data_hash?(user,"shop",{
            bonus: {
                charged_bonuses: user_charged_bonus_ids,
                consumed_bonuses: user_consumed_bonus_ids,
                total_charged_dia: user_total_charged_dia,
                total_consumed_dia: user_total_consumed_dia
            }
        })
        user.save!
        return {
            charged_bonus: charged_bonus_list,
            consumed_bonus: consumed_bonus_list
        }
    end

    # 累計チャージの報酬を受け取る
    def self.receive_charged_rewards(user,user_lang,uid)
        begin
            shop_bonuses = get_shop_bonuses(user)
            charged_bonus = shop_bonuses[:charged_bonus].find { |charged_bonus| charged_bonus[:uid] == uid }
            if charged_bonus.nil?
                raise StandardError.new("charged_rewards_not_found")
            end
            if charged_bonus[:status] == "received"
                raise StandardError.new("already_received_charged_rewards")
            end

            if charged_bonus[:status] == "unachieved"
                raise StandardError.new("not_achieved_charged_rewards")
            end

            user.user_save_data["shop"]["bonus"]["charged_bonuses"] << charged_bonus[:uid]
            unless ItemService.add_rewards?(user,charged_bonus[:rewards])
                raise StandardError.new("failed_to_add_rewards")
            end
            user.save!
            return {
                rewards: charged_bonus[:rewards],
                shop: "api/shop/index",
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "receive_charged_rewards")
        end
    end

    # 累計消費の報酬を受け取る
    def self.receive_consumed_rewards(user,user_lang,uid)
        begin
            shop_bonuses = get_shop_bonuses(user)
            consumed_bonus = shop_bonuses[:consumed_bonus].find { |consumed_bonus| consumed_bonus[:uid] == uid }
            if consumed_bonus.nil?
                raise StandardError.new("consumed_rewards_not_found")
            end
            if consumed_bonus[:status] == "received"
                raise StandardError.new("already_received_consumed_rewards")
            end
            if consumed_bonus[:status] == "unachieved"
                raise StandardError.new("not_achieved_consumed_rewards")
            end
            user.user_save_data["shop"]["bonus"]["consumed_bonuses"] << consumed_bonus[:uid]
            unless ItemService.add_rewards?(user,consumed_bonus[:rewards])
                raise StandardError.new("failed_to_add_rewards")
            end
            shop_result = ShopService.get_shop_index(user,user_lang,os)
            if shop_result[:error_type].present?
                raise StandardError.new("failed_to_get_shop_index")
            end
            user.save!
            return {
                rewards: consumed_bonus[:rewards],
                shop: shop_result[:shop],
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::ShopController, "receive_consumed_rewards")
        end
    end
end
