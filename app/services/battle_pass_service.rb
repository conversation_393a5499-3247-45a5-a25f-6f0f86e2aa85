# TODO mission_ptsは増えない
class BattlePassService
    extend ProcessF<PERSON>atR<PERSON>ards
    def self.get_battle_pass(user)
        begin
            # TODO ユーザーに関係しないところはcacheでデータを持っておく
            set_basic_battle_pass_info(user)
            free = []
            premium = []

            @battle_pass.free_rewards.each_with_index do |free_reward,index|
                need_mission_pts = ConstantService::BATTLE_PASS_MISSION_PTS * (index + 1)
                is_free_received = index <= @battle_pass_info["free"]["last_index"]
                free << {
                    need_mission_pts: need_mission_pts,
                    content:{
                    rewards:process_format_rewards([free_reward]),
                    is_received: is_free_received
                    }
                }
            end
            @battle_pass.premium_rewards.each_with_index do |premium_reward,index|
                need_mission_pts = ConstantService::BATTLE_PASS_MISSION_PTS * (index + 1)
                is_premium_received = index <= @battle_pass_info["premium"]["last_index"]
                premium << {
                    need_mission_pts: need_mission_pts,
                    content:{
                    rewards:process_format_rewards([premium_reward]),
                    is_received: is_premium_received
                    }
                }
            end

            return {
                battle_passes:{
                    free:free,
                    premium:premium,
                    season:@season,
                    start_at:@start_at,
                    end_at:@end_at,
                    cur_mission_pts:@battle_pass_info["mission_pts"],
                    is_premium:@battle_pass_info["is_premium"]
                }
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::BattlePassesController, "get_battle_pass")
        end
    end

    # uid指定で受け取れるようにする
    def self.receive_single_battle_pass(user, category)
        begin
            set_basic_battle_pass_info(user)
            expected_index = @battle_pass_info[category]["last_index"] + 1
            receive_battle_pass(user, category, expected_index)
            user.save!
            battle_passes = get_battle_pass(user)
            if battle_passes[:error_type]
                return battle_passes
            end
            return{
                rewards:@rewards,
                battle_passes: battle_passes[:battle_passes],
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::BattlePassesController, "receive_single_battle_pass")
        end
    end

    def self.receive_all_battle_pass(user)
        begin
            all_rewards = []
            set_basic_battle_pass_info(user)
            ActiveRecord::Base.transaction do
                @battle_pass.free_rewards.each_with_index do |free_reward,index|
                    if  @battle_pass_info["free"]["last_index"] < index
                        if @battle_pass_info["mission_pts"] < ConstantService::BATTLE_PASS_MISSION_PTS * (index + 1)
                            break
                        end
                        receive_battle_pass(user, "free", index)
                        all_rewards += @rewards
                    end
                end
                @battle_pass.premium_rewards.each_with_index do |premium_reward,index|
                    if @battle_pass_info["is_premium"] && @battle_pass_info["premium"]["last_index"] < index
                        if @battle_pass_info["mission_pts"] < ConstantService::BATTLE_PASS_MISSION_PTS * (index + 1)
                            break
                        end
                        receive_battle_pass(user, "premium", index)
                        all_rewards += @rewards
                    end
                end
            end
            if all_rewards.length == 0
                return {error_type: "no_rewards", error: "受け取り可能な報酬がありません"}
            end
            user.save!
            battle_passes = get_battle_pass(user)
            if battle_passes[:error_type]
                return battle_passes
            end
            return{
                rewards:all_rewards,
                battle_passes: battle_passes[:battle_passes],
                user:UserService.get_res(user)
            }
        rescue StandardError => e
            ErrorService.handle_exception(e, Api::BattlePassesController, "receive_all_battle_pass")
        end
    end

    # ミッションポイントを更新したいときに呼ぶ
    def self.add_mission_pts?(user,pts)
        set_basic_battle_pass_info(user)
        unless SaveDataService.add_user_save_data?(user, "battle_passes", {
            season: @season,
            free: {
                last_index: @battle_pass_info["free"]["last_index"]
            },
            premium: {
                last_index: @battle_pass_info["premium"]["last_index"]
            },
            is_premium: @battle_pass_info["is_premium"],
            mission_pts: @battle_pass_info["mission_pts"] + pts
        })
            return false
        end
        return true
    end

    # TODO プレミアムバトルパスを購入した時に呼ぶ
    def self.update_premium_battle_pass(user)
        set_basic_battle_pass_info(user)
        if @battle_pass_info["is_premium"]
            raise StandardError.new("premium_battle_pass_already_purchased")
        end
        unless SaveDataService.add_user_save_data?(user, "battle_passes", {
            season: @season,
            free: {
                last_index: @battle_pass_info["free"]["last_index"]
            },
            premium: {
                last_index: @battle_pass_info["premium"]["last_index"]
            },
            is_premium: true,
            mission_pts: @battle_pass_info["mission_pts"]
        })
            raise StandardError.new("failed_to_update_save_data")
        end
        rescue StandardError => e
            ErrorService.handle_exception(e, "BattlePassService", "update_premium_battle_pass")
    end
    # プレミアムバトルパスに入っているかを確認する
    def self.is_premium_battle_pass?(user)
        set_basic_battle_pass_info(user)
        @battle_pass_info["is_premium"]
    end
    private
    # seasonを指定してバトルパス情報を取得する
    def self.get_user_battle_pass_info(user, season)
        u_battle_passes = user.user_save_data["battle_passes"] || []
        u_battle_pass = u_battle_passes.find { |u_battle_pass| u_battle_pass["season"] == season }
        return {
            "mission_pts" => u_battle_pass&.dig("mission_pts") || 0,
            "free" => u_battle_pass&.dig("free") || {"last_index" => -1},
            "premium" => u_battle_pass&.dig("premium") || {"last_index" => -1},
            "is_premium" => u_battle_pass&.dig("is_premium") || false
        }
    end

    def self.set_basic_battle_pass_info(user)
        @battle_pass = BattlePass.within_valid_period.order_by_period_start_at.last # start_atが一番若いバトルパスを取得
        if @battle_pass.blank?
            initial_start_at = BattlePass.minimum(:start_at)
            if Time.current < initial_start_at
                @battle_pass = BattlePass.where(start_at: initial_start_at) # 初回バトルパスだけ特別
            else
                raise StandardError.new("battle_pass_not_found")
            end
        end

        # シーズン間で期間に重なりがないこと、一つシーズンにつき1つの期間の前提
        @start_at = @battle_pass.period_start_at
        @end_at = @battle_pass.period_end_at
        @season = @battle_pass.season
        @battle_pass_info = get_user_battle_pass_info(user, @season)
    end

    def self.receive_battle_pass(user, category, index)
        cur_mission_pts = @battle_pass_info["mission_pts"]
        need_mission_pts =  ConstantService::BATTLE_PASS_MISSION_PTS * (index + 1)
        if category == "free"
            free_rewards = @battle_pass.free_rewards
            if index >= free_rewards.length
                raise StandardError.new("reward_not_found")
            end
            if cur_mission_pts < need_mission_pts
                @rewards = []
                raise StandardError.new("pts_not_enough")
            end
            unless SaveDataService.add_user_save_data?(user, "battle_passes",{
                season: @season,
                mission_pts: cur_mission_pts,
                free: {
                    last_index: index,
                },
                premium: {
                    last_index: @battle_pass_info["premium"]["last_index"],
                },
                is_premium: @battle_pass_info["is_premium"]
            })
                raise StandardError.new("failed_to_update_save_data")
            end
            @battle_pass_info["free"]["last_index"] = index
            @rewards = [free_rewards[index]] # 配列にしないとItemService.add_rewards?でエラーになる
            unless ItemService.add_rewards?(user, @rewards)
                raise StandardError.new("failed_to_add_rewards")
            end

        elsif category == "premium"
            premium_rewards = @battle_pass.premium_rewards
            if index >= premium_rewards.length
                raise StandardError.new("reward_not_found")
            end
            unless @battle_pass_info["is_premium"]
                raise StandardError.new("premium_battle_pass_not_purchased")
            end
            if cur_mission_pts < need_mission_pts
                @rewards = []
                raise StandardError.new("pts_not_enough")
            end
            unless SaveDataService.add_user_save_data?(user, "battle_passes",{
                season: @season,
                mission_pts: cur_mission_pts,
                free: {
                    last_index: @battle_pass_info["free"]["last_index"],
                },
                premium: {
                    last_index: index,
                },
                is_premium: @battle_pass_info["is_premium"]
            })
                raise StandardError.new("failed_to_update_save_data")
            end
            @battle_pass_info["premium"]["last_index"] = index
            @rewards = [premium_rewards[index]] # 配列にしないとItemService.add_rewards?でエラーになる
            unless ItemService.add_rewards?(user, @rewards)
                raise StandardError.new("failed_to_add_rewards")
            end
        else
            raise StandardError.new("invalid_battle_pass_category")
        end
    end

    def self.get_current_season
        battle_pass = BattlePass.within_valid_period.order_by_period_start_at.last
        if battle_pass.blank?
            raise StandardError.new("battle_pass_not_found")
        end
        battle_pass.season
        rescue StandardError => e
            ErrorService.handle_exception(e, "BattlePassService", "get_current_season")
    end
end
