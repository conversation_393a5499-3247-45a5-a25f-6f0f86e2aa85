<%= form_with(model: shop_bundle, url: shop_bundle.persisted? ? shop_bundle_path(shop_bundle.uid) : shop_bundles_path) do |form| %>
  <% if shop_bundle.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(shop_bundle.errors.count, "error") %> prohibited this shop_bundle from being saved:</h2>

      <ul>
        <% shop_bundle.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="card p-4 mb-4">
    <%= form.label :uid, "UID" %>
    <%= form.number_field :uid %>
    <%= json_lang_form(form, :name, label: "バンドル名") %>
    <%= json_lang_form(form, :desc, label: "バンドル説明", as: :text_area) %>

    <div>
      <%= form.label :必要ダイヤ数, style: "display: block" %>
      <%= form.number_field :cost, min: 0 %>
    </div>

    <div>
      <%= form.label :購入上限, style: "display: block" %>
      <%= form.number_field :max_count, min: 0 %>
    </div>
  </div>
  <div class="card p-3 mb-4">
    <%= render 'shared/rewards', rewards: shop_bundle.rewards, param_name: 'shop_bundle[rewards]'%>
  </div>

  <div class="card p-3 mb-4">
      <%= render 'shared/period', form: form %>
  </div>

  <div class="actions">
    <%= form.submit %>
  </div>
<% end %>
