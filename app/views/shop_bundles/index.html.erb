<h1 class="mb-6">バンドル一覧</h1>

<%= render partial: "shared/table", locals: { 
    items: @shop_bundles, 
    columns: ["UID", "バンドル名",  "開始日", "終了日", "期間"], 
    values: [
      ->(item) { item.uid },
      ->(item) { json_lang(item.name)},
      ->(item) { item.start_at },
      ->(item) { item.end_at },
      ->(item) { item.period&.name || '(自由指定)' }
    ],
    model_name: "バンドル一覧",
    edit_path: ->(item) { edit_shop_bundle_path(item.uid) },
    delete_path: ->(item) { shop_bundle_path(item.uid) },
    new_path: new_shop_bundle_path
  } %>
  