<% content_for :page_title, "パラメータ設定" %>

<% if @parameter_setting %>
  <div class="card card-primary mb-5">
    <div class="card-header">
      <h3 class="card-title">現在適用中のパラメータ設定 (ID: <%= @parameter_setting.id %>)</h3>
    </div>
    <div class="card-body">
      <%= render partial: 'form', locals: { parameter_setting: @parameter_setting } %>
    </div>
  </div>
<% end %>

<%= render partial: "shared/table", locals: { 
    items: @parameter_settings, 
    columns: ["ID", "最終更新日時", "ステータス"], 
    values: [
      ->(item) { item.id },
      ->(item) { item.updated_at.strftime('%Y-%m-%d %H:%M:%S') },
      ->(item) { item.current? ? "適用中" : "未適用" }
    ],
    model_name: "パラメータ設定一覧",
    edit_path: ->(item) { edit_parameter_setting_path(item) },
    delete_path: ->(item) { parameter_setting_path(item) },
    new_path: new_parameter_setting_path
  } %>

<div class="mt-3">
  <small class="text-muted">
    <i class="fas fa-info-circle"></i> 
    最新の設定がゲーム内で自動的に使用されます。
  </small>
</div> 