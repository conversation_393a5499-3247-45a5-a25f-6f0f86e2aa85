<div x-data="{
    default_data: <%= ConstantService.default_data.to_json %>,
    data: null,
    editMode: false,
    originalData: null,
    expandedSections: {},
    
    // キーの日本語訳マッピング
    translations: <%= ConstantService.translations.to_json %>,
    
    // キーの日本語訳を取得
    getTranslation(key) {
        return this.translations[key] || key;
    },
    
    init() {
      // 初期データをデフォルトから設定
      <% if @parameter_setting&.settings.present? %>
        // デフォルトデータをベースに、保存されたデータをディープマージ
        this.data = this.deepMerge(
          JSON.parse(JSON.stringify(this.default_data)), 
          <%= @parameter_setting.settings.to_json %>
        );
      <% else %>
        this.data = JSON.parse(JSON.stringify(this.default_data));
      <% end %>
      
      // 全てのセクションを展開した状態にする
      Object.keys(this.data).forEach(shopKey => {
        this.expandedSections[shopKey] = shopKey !== 'danger'; // danger以外を展開
        
        Object.keys(this.data[shopKey]).forEach(categoryKey => {
          if (typeof this.data[shopKey][categoryKey] === 'object' && this.data[shopKey][categoryKey] !== null) {
            this.expandedSections[`${shopKey}.${categoryKey}`] = shopKey !== 'danger';
          }
        });
      });
    },
    
    // ディープマージ関数
    deepMerge(target, source) {
      // ソースがnullか未定義の場合はターゲットを返す
      if (!source) return target;
      
      const result = { ...target };
      
      // sourceの各キーについて処理
      Object.keys(source).forEach(key => {
        // 対象キーがtargetにも存在しかつ、両方がオブジェクト（配列ではない）の場合は再帰的にマージ
        if (
          key in target && 
          typeof source[key] === 'object' && 
          source[key] !== null && 
          typeof target[key] === 'object' && 
          target[key] !== null && 
          !Array.isArray(source[key]) && 
          !Array.isArray(target[key])
        ) {
          result[key] = this.deepMerge(target[key], source[key]);
        } else {
          // それ以外の場合はsourceの値を使用
          result[key] = source[key];
        }
      });
      
      return result;
    },
    
    toggleSection(path) {
      this.expandedSections[path] = !this.expandedSections[path];
    },
    
    isSectionExpanded(path) {
      return this.expandedSections[path] !== false;
    },
    
    startEdit() {
      this.originalData = JSON.parse(JSON.stringify(this.data));
      this.editMode = true;
    },
    
    cancelEdit() {
      this.data = JSON.parse(JSON.stringify(this.originalData));
      this.editMode = false;
    },
    
    saveChanges() {
      document.getElementById('parameter_setting_form').submit();
    },
    
    resetToDefault() {
      this.data = JSON.parse(JSON.stringify(this.default_data));
      this.editMode = false;
      this.originalData = null;
    },
    
    logChange(path, value) {
      console.log(`値が変更されました: ${path}`, value);
    },
    
    addItem(path, defaultValue = 0.0) {
      const parts = path.split('.');
      let current = this.data;
      
      // 最後のキー以外のパスを辿る
      for(let i = 0; i < parts.length - 1; i++) {
        current = current[parts[i]];
      }
      
      // 最後のキー（オブジェクト）に新しいアイテムを追加
      const lastObj = current[parts[parts.length - 1]];
      const newKey = this.generateUniqueKey(lastObj);
      lastObj[newKey] = defaultValue;
    },
    
    removeItem(path, key) {
      const parts = path.split('.');
      let current = this.data;
      
      // パスを辿る
      for(let i = 0; i < parts.length; i++) {
        current = current[parts[i]];
      }
      
      // アイテムを削除
      delete current[key];
    },
    
    generateUniqueKey(obj) {
      let i = 1;
      while(obj['item' + i] !== undefined) {
        i++;
      }
      return 'item' + i;
    },
    
    addNestedObject(path) {
      const parts = path.split('.');
      let current = this.data;
      
      // 最後のキー以外のパスを辿る
      for(let i = 0; i < parts.length - 1; i++) {
        current = current[parts[i]];
      }
      
      // 最後のキー（オブジェクト）に新しいネストオブジェクトを追加
      const lastObj = current[parts[parts.length - 1]];
      const newKey = this.generateUniqueKey(lastObj);
      lastObj[newKey] = {};
    },
    
    // 指定されたパスが配列かどうかをチェック
    isArrayPath(path) {
      try {
        const parts = path.split('.');
        let current = this.default_data;
        
        for (let i = 0; i < parts.length - 1; i++) {
          current = current[parts[i]];
        }
        
        const lastPart = parts[parts.length - 1];
        return Array.isArray(current[lastPart]);
      } catch (e) {
        return false;
      }
    },
    
    // 値の型を取得
    getValueType(path) {
      try {
        const defaultValue = this.getValueByPath(path, this.default_data);
        if (typeof defaultValue === 'string' && defaultValue.match(/^\d{4}-\d{2}-\d{2}.*\+\d{4}$/)) {
          return 'datetime';
        }
        return typeof defaultValue;
      } catch (e) {
        return 'number'; // デフォルトは数値型
      }
    },
    
    // 指定されたパスから値を取得する（オプションでデータソースを指定可能）
    getValueByPath(path, source = null) {
      try {
        const dataSource = source || this.data;
        const parts = path.split('.');
        let current = dataSource;
        
        for (let i = 0; i < parts.length; i++) {
          if (parts[i].includes('[')) {
            // 配列アクセスの場合
            const arrayPart = parts[i].split('[');
            const arrayName = arrayPart[0];
            const index = parseInt(arrayPart[1].replace(']', ''));
            
            current = current[arrayName][index];
          } else {
            current = current[parts[i]];
          }
        }
        
        return current;
      } catch (e) {
        console.error(`パス ${path} の値を取得できませんでした`, e);
        return null;
      }
    },
    
    // 値を設定する
    setValueByPath(path, value) {
      try {
        // 型に応じて値を変換
        const valueType = this.getValueType(path);
        let convertedValue = value;
        
        if (valueType === 'number') {
          convertedValue = parseFloat(value);
          if (isNaN(convertedValue)) {
            convertedValue = 0;
          }
        } else if (valueType === 'boolean') {
          convertedValue = Boolean(value);
        }
        
        const parts = path.split('.');
        let current = this.data;
        
        // 最後のパス部分を除いて辿る
        for (let i = 0; i < parts.length - 1; i++) {
          if (parts[i].includes('[')) {
            // 配列アクセスの場合
            const arrayPart = parts[i].split('[');
            const arrayName = arrayPart[0];
            const index = parseInt(arrayPart[1].replace(']', ''));
            
            current = current[arrayName][index];
          } else {
            current = current[parts[i]];
          }
        }
        
        // 最後のパス部分を処理
        const lastPart = parts[parts.length - 1];
        if (lastPart.includes('[')) {
          // 配列アクセスの場合
          const arrayPart = lastPart.split('[');
          const arrayName = arrayPart[0];
          const index = parseInt(arrayPart[1].replace(']', ''));
          
          current[arrayName][index] = convertedValue;
        } else {
          current[lastPart] = convertedValue;
        }
        
        this.logChange(path, convertedValue);
      } catch (e) {
        console.error(`パス ${path} の値を設定できませんでした`, e);
      }
    },
    
    // 現在のデータをコンソールに整形して表示する
    logCurrentData() {
      console.log(JSON.stringify(this.data, null, 2));
    }
}">

<%= form_with(model: @parameter_setting, id: 'parameter_setting_form', local: true) do |form| %>
  <!-- 隠しフィールドでJSONデータを送信 -->
  <input type="hidden" name="parameter_setting[settings]" x-bind:value="JSON.stringify(data)" />

  <h1 class="d-flex justify-content-between align-items-center mb-4">
    <span>パラメータ設定</span>
    
    <div>
      <template x-if="!editMode">
        <div>
          <button type="button" @click="startEdit()" class="btn btn-primary mr-2">
            <i class="fas fa-edit"></i> 編集モード
          </button>
          <button type="button" @click="resetToDefault()" class="btn btn-secondary">
            <i class="fas fa-undo"></i> デフォルトに戻す
          </button>
        </div>
      </template>
      
      <template x-if="editMode">
        <div>
          <button type="button" @click="cancelEdit()" class="btn btn-secondary mr-2">
            <i class="fas fa-times"></i> キャンセル
          </button>
          <button type="button" @click="saveChanges()" class="btn btn-success mr-2">
            <i class="fas fa-save"></i> 変更を保存
          </button>
          <button type="button" @click="logCurrentData()" class="btn btn-info">
            <i class="fas fa-code"></i> データを表示
          </button>
        </div>
      </template>
    </div>
  </h1>

  <!-- データ表示部分 -->
  <div>
    <!-- オブジェクト表示用のテンプレート -->
    <template x-for="(shopValue, shopKey) in data" :key="shopKey">
      <div class="card card-outline card-primary mb-4">
        <div class="card-header" @click="toggleSection(shopKey)" style="cursor: pointer;">
          <div class="d-flex justify-content-between align-items-center">
            <h2 class="card-title" 
                x-text="getTranslation(shopKey)" 
                :class="{ 'text-danger': shopKey === 'danger' }"></h2>
            <i class="fas" :class="isSectionExpanded(shopKey) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
          </div>
        </div>
        <div class="card-body" x-show="isSectionExpanded(shopKey)">
          <!-- 各ショップの中身をループ処理 -->
          <div>
            <!-- まずプリミティブ値を表示 -->
            <template x-for="(categoryValue, categoryKey) in shopValue" :key="categoryKey">
              <template x-if="typeof categoryValue !== 'object' || categoryValue === null">
                <div class="form-group mb-2">
                  <div class="d-flex align-items-center">
                    <label class="control-label mr-3 mb-0" style="width: 200px;" x-text="getTranslation(categoryKey)"></label>
                    <div class="input-group input-group-sm" x-show="getValueType(shopKey + '.' + categoryKey) !== 'boolean' && getValueType(shopKey + '.' + categoryKey) !== 'datetime'">
                      <input 
                             :type="getValueType(shopKey + '.' + categoryKey) === 'number' ? 'number' : 'text'" 
                             :value="categoryValue"
                             x-on:input="setValueByPath(shopKey + '.' + categoryKey, $event.target.value)"
                             class="form-control"
                             :disabled="!editMode">
                      <div class="input-group-append" x-show="getValueType(shopKey + '.' + categoryKey) === 'number'">
                        <span class="input-group-text"><i class="fas fa-percentage"></i></span>
                      </div>
                    </div>
                    <div class="input-group input-group-sm" x-show="getValueType(shopKey + '.' + categoryKey) === 'datetime'">
                      <input 
                             type="datetime-local" 
                             :value="(categoryValue && typeof categoryValue === 'string') ? categoryValue.replace(/\s\+\d{4}$/, '').replace(' ', 'T') : ''"
                             x-on:input="setValueByPath(shopKey + '.' + categoryKey, $event.target.value.replace('T', ' ') + ' +0900')"
                             class="form-control"
                             :disabled="!editMode">
                      <div class="input-group-append">
                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                      </div>
                    </div>
                    <div class="custom-control custom-switch" x-show="getValueType(shopKey + '.' + categoryKey) === 'boolean'">
                      <input type="checkbox" 
                             class="custom-control-input" 
                             :id="'switch-' + shopKey + '-' + categoryKey"
                             :checked="categoryValue"
                             x-on:change="setValueByPath(shopKey + '.' + categoryKey, $event.target.checked)"
                             :disabled="!editMode">
                      <label class="custom-control-label" :for="'switch-' + shopKey + '-' + categoryKey"></label>
                    </div>
                  </div>
                </div>
              </template>
            </template>
            
            <!-- 次に配列を表示 -->
            <template x-for="(categoryValue, categoryKey) in shopValue" :key="categoryKey">
              <template x-if="Array.isArray(categoryValue)">
                <div class="card card-outline card-secondary mb-3">
                  <div class="card-header py-2" @click="toggleSection(shopKey + '.' + categoryKey)" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                      <h3 class="card-title mb-0 h5" x-text="getTranslation(categoryKey) + ' (配列)'"></h3>
                      <div class="d-flex">
                        <div x-show="editMode" class="btn-group mr-2" @click.stop>
                          <button type="button" x-on:click="data[shopKey][categoryKey].push(0)" class="btn btn-sm btn-success">
                            <i class="fas fa-plus"></i> 項目追加
                          </button>
                        </div>
                        <i class="fas" :class="isSectionExpanded(shopKey + '.' + categoryKey) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                      </div>
                    </div>
                  </div>
                  <div class="card-body py-2" x-show="isSectionExpanded(shopKey + '.' + categoryKey)">
                    <!-- 配列の各要素を表示 -->
                    <template x-for="(item, index) in categoryValue" :key="index">
                      <div class="form-group mb-2">
                        <div class="d-flex align-items-center">
                          <label class="control-label mr-3 mb-0" style="width: 200px;" x-text="'要素 ' + index"></label>
                          <div class="input-group input-group-sm" x-show="typeof item !== 'boolean' && getValueType(shopKey + '.' + categoryKey + '[' + index + ']') !== 'datetime'">
                            <input 
                                   :type="typeof item === 'number' ? 'number' : 'text'" 
                                   :value="item"
                                   x-on:input="setValueByPath(shopKey + '.' + categoryKey + '[' + index + ']', $event.target.value)"
                                   class="form-control"
                                   :disabled="!editMode">
                            <div class="input-group-append" x-show="typeof item === 'number'">
                              <span class="input-group-text"><i class="fas fa-percentage"></i></span>
                            </div>
                          </div>
                          <div class="input-group input-group-sm" x-show="getValueType(shopKey + '.' + categoryKey + '[' + index + ']') === 'datetime'">
                            <input 
                                   type="datetime-local" 
                                   :value="(item && typeof item === 'string') ? item.replace(/\s\+\d{4}$/, '').replace(' ', 'T') : ''"
                                   x-on:input="setValueByPath(shopKey + '.' + categoryKey + '[' + index + ']', $event.target.value.replace('T', ' ') + ' +0900')"
                                   class="form-control"
                                   :disabled="!editMode">
                            <div class="input-group-append">
                              <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                          </div>
                          <div class="custom-control custom-switch" x-show="typeof item === 'boolean'">
                            <input type="checkbox" 
                                   class="custom-control-input" 
                                   :id="'switch-' + shopKey + '-' + categoryKey + '-' + index"
                                   :checked="item"
                                   x-on:change="setValueByPath(shopKey + '.' + categoryKey + '[' + index + ']', $event.target.checked)"
                                   :disabled="!editMode">
                            <label class="custom-control-label" :for="'switch-' + shopKey + '-' + categoryKey + '-' + index"></label>
                          </div>
                          <button 
                              type="button"
                              x-show="editMode" 
                              x-on:click="data[shopKey][categoryKey].splice(index, 1)" 
                              class="btn btn-sm btn-danger ml-2">
                              <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </template>
            </template>
            
            <!-- 最後にオブジェクトを表示 -->
            <template x-for="(categoryValue, categoryKey) in shopValue" :key="categoryKey">
              <template x-if="typeof categoryValue === 'object' && categoryValue !== null && !Array.isArray(categoryValue)">
                <div class="card card-outline card-secondary mb-3">
                  <div class="card-header py-2" @click="toggleSection(shopKey + '.' + categoryKey)" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                      <h3 class="card-title mb-0 h5" x-text="getTranslation(categoryKey)"></h3>
                      <i class="fas" :class="isSectionExpanded(shopKey + '.' + categoryKey) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                    </div>
                  </div>
                  <div class="card-body py-2" x-show="isSectionExpanded(shopKey + '.' + categoryKey)">
                    <!-- オブジェクトの各プロパティを表示 -->
                    <template x-for="(propValue, propKey) in categoryValue" :key="propKey">
                      <div class="form-group mb-2">
                        <div class="d-flex align-items-center">
                          <label class="control-label mr-3 mb-0" style="width: 200px;" x-text="getTranslation(propKey)"></label>
                          <div class="input-group input-group-sm" x-show="getValueType(shopKey + '.' + categoryKey + '.' + propKey) !== 'boolean' && getValueType(shopKey + '.' + categoryKey + '.' + propKey) !== 'datetime'">
                            <input 
                                   :type="getValueType(shopKey + '.' + categoryKey + '.' + propKey) === 'number' ? 'number' : 'text'" 
                                   :value="propValue"
                                   x-on:input="setValueByPath(shopKey + '.' + categoryKey + '.' + propKey, $event.target.value)"
                                   class="form-control"
                                   :disabled="!editMode">
                            <div class="input-group-append" x-show="getValueType(shopKey + '.' + categoryKey + '.' + propKey) === 'number'">
                              <span class="input-group-text"><i class="fas fa-percentage"></i></span>
                            </div>
                          </div>
                          <div class="input-group input-group-sm" x-show="getValueType(shopKey + '.' + categoryKey + '.' + propKey) === 'datetime'">
                            <input 
                                   type="datetime-local" 
                                   :value="(propValue && typeof propValue === 'string') ? propValue.replace(/\s\+\d{4}$/, '').replace(' ', 'T') : ''"
                                   x-on:input="setValueByPath(shopKey + '.' + categoryKey + '.' + propKey, $event.target.value.replace('T', ' ') + ' +0900')"
                                   class="form-control"
                                   :disabled="!editMode">
                            <div class="input-group-append">
                              <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                          </div>
                          <div class="custom-control custom-switch" x-show="getValueType(shopKey + '.' + categoryKey + '.' + propKey) === 'boolean'">
                            <input type="checkbox" 
                                   class="custom-control-input" 
                                   :id="'switch-' + shopKey + '-' + categoryKey + '-' + propKey"
                                   :checked="propValue"
                                   x-on:change="setValueByPath(shopKey + '.' + categoryKey + '.' + propKey, $event.target.checked)"
                                   :disabled="!editMode">
                            <label class="custom-control-label" :for="'switch-' + shopKey + '-' + categoryKey + '-' + propKey"></label>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>
    </template>
    
    <div class="d-flex justify-content-end mt-4">
      <template x-if="!editMode">
        <div>
          <button type="button" @click="startEdit()" class="btn btn-primary mr-2">
            <i class="fas fa-edit"></i> 編集モード
          </button>
          <button type="button" @click="resetToDefault()" class="btn btn-secondary">
            <i class="fas fa-undo"></i> デフォルトに戻す
          </button>
        </div>
      </template>
      
      <template x-if="editMode">
        <div>
          <button type="button" @click="cancelEdit()" class="btn btn-secondary mr-2">
            <i class="fas fa-times"></i> キャンセル
          </button>
          <button type="button" @click="saveChanges()" class="btn btn-success mr-2">
            <i class="fas fa-save"></i> 変更を保存
          </button>
          <button type="button" @click="logCurrentData()" class="btn btn-info">
            <i class="fas fa-code"></i> データを表示
          </button>
        </div>
      </template>
    </div>
  </div>
<% end %>