<h1>アカウント情報を編集</h1>

<%= form_with model: @user, url: account_path, method: :patch, local: true do |form| %>
<div class="form-group">
    <%= form.label :name, "名前" %>
    <%= form.text_field :name, class: "form-control" %>
</div>

<div class="form-group">
    <%= form.label :email, "メールアドレス" %>
    <%= form.email_field :email, class: "form-control" %>
</div>

<div class="form-group">
    <%= form.label :password, "パスワード (変更しない場合は空のまま)" %>
    <%= form.password_field :password, class: "form-control" %>
</div>

<div class="form-group">
    <%= form.label :password_confirmation, "パスワード確認" %>
    <%= form.password_field :password_confirmation, class: "form-control" %>
</div>

<%= form.submit "保存", class: "btn btn-success" %>
<% end %>

<%= link_to "キャンセル", account_path, class: "btn btn-secondary" %>