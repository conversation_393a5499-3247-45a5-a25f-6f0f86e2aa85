<h1 class="mb-6">ログインボーナス一覧</h1>


<%= render partial: "shared/table", locals: { 
    items: @login_bonuses, 
    columns: ["UID", "ログインボーナス名",  "開始日", "終了日", "期間"], 
    values: [
      ->(item) { item.uid },
      ->(item) { json_lang(item.title)},
      ->(item) { item.period_start_at },
      ->(item) { item.period_end_at },
      ->(item) { item.period&.name || '(自由指定)' }
    ],
    model_name: "ログインボーナス一覧",
    edit_path: ->(item) { edit_login_bonus_path(item.uid) },
    delete_path: ->(item) { login_bonus_path(item.uid) },
    new_path: new_login_bonus_path
  } %>