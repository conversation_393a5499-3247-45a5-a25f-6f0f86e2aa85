<%= form_with(model: login_bonus, url: login_bonus.persisted? ? login_bonus_path(login_bonus.uid) : login_bonuses_path, local: true) do |f| %>
  <% if login_bonus.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(login_bonus.errors.count, "error") %> prohibited this login bonus from being saved:</h2>
      <ul>
        <% login_bonus.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= f.label :uid %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :is_event %>
    <%= f.check_box :is_event %>
  </div>

  <div class="form-group">
    <%= json_lang_form(f, :title, label: "タイトル") %>
    <%= json_lang_form(f, :desc, label: "説明", as: :text_area) %>
  </div>

  <div class="form-group">
    <%= render 'shared/rewards', rewards: @login_bonus.rewards, param_name: 'login_bonus[rewards]' %>
  </div>

  
  <%= render 'shared/period', form: f %>


  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
  </div>
<% end %>

<script>
function toggleLoginBonusDateFields(value) {
  const dateFields = document.getElementById('login-bonus-date-fields');
  if (value && value !== '') {
    dateFields.style.display = 'none';
  } else {
    dateFields.style.display = 'block';
  }
}

// 初期表示時にも実行
document.addEventListener('DOMContentLoaded', function() {
  const periodSelect = document.querySelector('select[name="login_bonus[period_id]"]');
  if (periodSelect) {
    toggleLoginBonusDateFields(periodSelect.value);
  }
});
</script> 