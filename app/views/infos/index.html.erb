<p style="color: green"><%= notice %></p>

<h1 class="mb-6">お知らせ</h1>

<%= render partial: "shared/table", locals: {
    items: @infos,
    columns: ["UID", "タイトル", "カテゴリ"], 
    values: [
      ->(item) { item.uid },
      ->(item) { json_lang(item.title) },
      ->(item) { 
        case item.category
        when 'event' then 'イベント'
        when 'bug' then '不具合情報'
        when 'info' then '最新情報'
        when 'other' then 'その他'
        else item.category || '未設定'
        end
      }
    ],
    model_name: "お知らせ一覧",
    edit_path: ->(item) { edit_info_path(item.uid) },
    delete_path: ->(item) { info_path(item.uid) },
    new_path: new_info_path
  } %>