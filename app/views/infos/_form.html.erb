<%= form_with(model: info) do |form| %>
  <% if info.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(info.errors.count, "error") %> prohibited this info from being saved:</h4>
      <ul>
        <% info.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group mb-3">
    <%= form.label :uid, 'UID', class: 'form-label' %>
    <%= form.number_field :uid, class: 'form-control' %>
  </div>

  <div class="card p-4 mb-4">
    <%= json_lang_form(form, :title, label: "タイトル") %>
  </div>

  <div class="form-group">
    <%= form.label :image, "タイトル画像" %>
    <%= form.file_field :image, class: "form-control" %>
    <% if info.persisted? && info.image&.attached? && info.image.blob&.persisted? %>
      <div class="mt-2">
        <%= image_tag @info.image, width: 150, class: "img-thumbnail" %>
      </div>
    <% end %>
  </div>

  <div class="field">
    <%= form.label :content %>
    <%= form.rich_text_area :content %>
  </div>

  <%= render 'shared/period', form: form %>

  <div class="form-group mb-3">
    <%= form.label :category, 'カテゴリ', class: 'form-label' %>
    <%= form.select :category, 
                  [
                    ['イベント', 'event'],
                    ['不具合情報', 'bug'],
                    ['最新情報', 'info'],
                    ['その他', 'other']
                  ],
                  { include_blank: '選択してください' },
                  { class: 'form-control' } %>
  </div>

  <div class="actions">
    <%= form.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %>

<script>
function toggleInfoDateFields(value) {
  const dateFields = document.getElementById('info-date-fields');
  if (value && value !== '') {
    dateFields.style.display = 'none';
  } else {
    dateFields.style.display = 'block';
  }
}

// 初期表示時にも実行
document.addEventListener('DOMContentLoaded', function() {
  const periodSelect = document.querySelector('select[name="info[period_id]"]');
  if (periodSelect) {
    toggleInfoDateFields(periodSelect.value);
  }
});
</script>
