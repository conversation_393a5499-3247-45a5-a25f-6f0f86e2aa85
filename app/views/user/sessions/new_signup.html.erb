<h1>新規登録</h1>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザー追加</h3>
    </div>
    <div class="card-body">
        <%= form_with(model: @user, url: user_signup_path, local: true) do |f| %>
        <div class="form-group">
            <%= f.label :name, "名前" %>
            <%= f.text_field :name, class: "form-control", placeholder: "名前を入力してください" %>
        </div>
        <div class="form-group">
            <%= f.submit "ユーザーを追加", class: "btn btn-success btn-sm" %>
        </div>
        <% end %>
    </div>
</div>

<% if @user.errors.any? %>
  <div class="alert alert-danger">
    <h4>エラーが発生しました:</h4>
    <ul>
      <% @user.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
    </ul>
  </div>
<% end %>
