<h1>パック詳細</h1>

<% if @pack %>
  <p><strong>パック名:</strong> <%= @pack.name %></p>

  <% if @pack.image&.attached? %>
    <div>
      <%= image_tag @pack.image, width: 300, class: "img-thumbnail" %>
    </div>
  <% else %>
    <p>画像はありません</p>
  <% end %>

  <p><strong>有効期間:</strong>
    <%= @pack.start_at&.strftime('%Y-%m-%d %H:%M:%S') || '未設定' %> ～
    <%= @pack.end_at&.strftime('%Y-%m-%d %H:%M:%S') || '未設定' %>
  </p>

  <% if @pack.card_data.present? %>
    <p><strong>内容:</strong> <%= @pack.card_data %></p>
  <% end %>

  <p><strong>パック排出量:</strong> <%= @pack.cards_per_pack || '未設定' %></p>

  <p><strong>ダイヤ消費量:</strong> <%= @pack.dia_cost || 0 %></p>

  <div style="margin-top: 20px;">
    <%= form_with url: user_gacha_pull_path(@user.open_id), method: :post, local: true do |f| %>
      <label>ガチャ回数:</label>
      <%= select_tag :pull_count, options_for_select( (1..100).map{|n| [n, n]}, 1 ) %>
      <%= hidden_field_tag :pack_id, @pack.uid %>
      <%= submit_tag 'ガチャを引く', class: 'btn btn-primary' %>
    <% end %>

  </div>
<% else %>
  <p>パックが見つかりません。</p>
<% end %>

<%= link_to 'ガチャ一覧へ戻る', user_gacha_show_path(@user.open_id), class: 'btn btn-secondary' %>
