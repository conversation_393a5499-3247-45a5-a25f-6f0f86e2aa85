<% if flash[:notice] %>
  <div class="alert alert-success">
    <%= flash[:notice] %>
  </div>
<% end %>

<% if flash[:alert] %>
  <div class="alert alert-danger">
    <%= flash[:alert] %>
  </div>
<% end %>

<div class="container my-4">
  <h1 class="mb-4">課金アイテム一覧</h1>
  
  <div class="mb-4">
    <%= link_to "カテゴリで表示", user_charging_item_categories_path(current_user.open_id), class: "btn btn-outline-primary" %>
  </div>
  
  <div class="mb-4">
    <h5>カテゴリを選択</h5>
    <div class="btn-group mb-3">
      <%= link_to "すべて", user_charging_items_path(current_user.open_id), class: "btn #{params[:category].blank? ? 'btn-primary' : 'btn-outline-primary'}" %>
      <%= link_to "ダイヤ", user_charging_items_path(current_user.open_id, category: "dia"), class: "btn #{params[:category] == 'dia' ? 'btn-primary' : 'btn-outline-primary'}" %>
      <%= link_to "バンドル", user_charging_items_path(current_user.open_id, category: "bundle"), class: "btn #{params[:category] == 'bundle' ? 'btn-primary' : 'btn-outline-primary'}" %>
      
      <% (@categories - ['dia', 'bundle']).each do |category| %>
        <% if category.present? %>
          <%= link_to category, user_charging_items_path(current_user.open_id, category: category), class: "btn #{params[:category] == category ? 'btn-primary' : 'btn-outline-primary'}" %>
        <% end %>
      <% end %>
    </div>
  </div>

  <% if @charging_items.present? %>
    <div class="row">
      <% @charging_items.each do |item| %>
        <div class="col-md-4 mb-4">
          <div class="card h-100">
            <div class="card-header">
              <h5 class="card-title"><%= json_lang(item.name) %></h5>
              <% if item.category.present? %>
                <span class="badge <%= item.category == 'dia' ? 'bg-info' : (item.category == 'bundle' ? 'bg-success' : 'bg-secondary') %>">
                  <%= item.category == 'dia' ? 'ダイヤ' : (item.category == 'bundle' ? 'バンドル' : item.category) %>
                </span>
              <% end %>
            </div>
            <div class="card-body">
              <p class="card-text"><%= json_lang(item.desc) %></p>
              
              <% if item.rewards.present? %>
                <div class="mb-3">
                  <h6>報酬</h6>
                  <ul class="list-group list-group-flush">
                    <% item.rewards.each do |reward_type, reward_value| %>
                      <li class="list-group-item"><%= reward_type %>: <%= reward_value %></li>
                    <% end %>
                  </ul>
                </div>
              <% end %>
              
              <% if item.start_at.present? || item.end_at.present? %>
                <div class="text-muted small mb-2">
                  <% if item.start_at.present? && item.end_at.present? %>
                    販売期間: <%= item.start_at.strftime('%Y/%m/%d') %> - <%= item.end_at.strftime('%Y/%m/%d') %>
                  <% elsif item.start_at.present? %>
                    販売開始: <%= item.start_at.strftime('%Y/%m/%d') %>
                  <% elsif item.end_at.present? %>
                    販売終了: <%= item.end_at.strftime('%Y/%m/%d') %>
                  <% end %>
                </div>
              <% end %>
              
              <% if item.max_count.present? %>
                <div class="text-muted small mb-3">
                  購入上限: <%= item.max_count %>回
                </div>
              <% end %>
              
              <%= form_with url: user_purchase_charging_item_path(current_user.open_id), method: :post, local: true do |f| %>
                <%= hidden_field_tag :charging_item_uid, item.uid %>
                <%= f.submit '購入', class: 'btn btn-primary w-100' %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="alert alert-info">
      現在、利用可能な課金アイテムはありません。
    </div>
  <% end %>
</div>

<% content_for :scripts do %>
  <script>
    // ページロード後に実行
    document.addEventListener('DOMContentLoaded', function() {
      // カテゴリフィルタ用のスクリプト
      const categoryLinks = document.querySelectorAll('.category-link');
      categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const category = this.dataset.category;
          window.location.href = '<%= user_charging_items_path(current_user.open_id) %>?category=' + category;
        });
      });
    });
  </script>
<% end %>
