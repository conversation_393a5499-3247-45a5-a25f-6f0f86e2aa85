<div class="container my-4">
  <h1 class="mb-4">課金アイテムカテゴリ</h1>

  <div class="row">
    <div class="col-12 mb-4">
      <%= link_to "すべてのアイテムを表示", user_charging_items_path(@user.open_id), class: "btn btn-primary btn-lg w-100" %>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <h5 class="card-title">ダイヤ</h5>
          <p class="card-text">ゲーム内通貨のダイヤを購入できます</p>
          <div class="d-grid gap-2">
            <%= link_to "表示", user_charging_items_path(@user.open_id, category: "dia"), class: "btn btn-info btn-lg" %>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-6 mb-4">
      <div class="card h-100">
        <div class="card-body text-center">
          <h5 class="card-title">バンドル</h5>
          <p class="card-text">お得なアイテムのセットを購入できます</p>
          <div class="d-grid gap-2">
            <%= link_to "表示", user_charging_items_path(@user.open_id, category: "bundle"), class: "btn btn-success btn-lg" %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <% if (@categories - @predefined_categories).present? %>
    <h4 class="mb-3">その他のカテゴリ</h4>
    <div class="row">
      <% (@categories - @predefined_categories).each do |category| %>
        <% if category.present? %>
          <div class="col-md-4 mb-4">
            <div class="card">
              <div class="card-body">
                <h5 class="card-title"><%= category %></h5>
                <%= link_to "表示", user_charging_items_path(@user.open_id, category: category), class: "btn btn-outline-primary" %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  <% end %>
</div> 