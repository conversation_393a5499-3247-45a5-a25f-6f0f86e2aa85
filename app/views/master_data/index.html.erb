<% content_for :page_title, "マスターデータ管理" %>

<div class="mb-4">
    <%= button_to '最新取得', master_data_create_path, method: :post, class: "btn btn-primary" %>
</div>

<%= form_with url: multi_delete_master_data_path, method: :delete, local: true do %>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">マスターデータ一覧</h3>
    </div>
    <div class="card-body">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>
                        <input type="checkbox" id="select_all" />
                    </th>
                    <th>UID</th>
                    <th>概要</th>
                    <th>作成日</th>
                    <th>編集</th>
                    <th>JSON表示</th>
                    <th>削除</th>
                </tr>
            </thead>
            <tbody>
                <% if @data.present? %>
                <% @data.each do |datum| %>
                <tr>
                    <td>
                        <%= check_box_tag "uids[]", datum.uid, false, class: "select_item" %>
                    </td>
                    <td><%= datum.uid %></td>
                    <td><%= datum.title %></td>
                    <td><%= time_ago_in_words(datum.created_at) %></td>
                    <td>
                        <%= link_to "編集", master_datum_path(datum.uid), class: "btn btn-info btn-sm" %>
                    </td>
                    <td>
                        <%= link_to "JSON表示", "#", class: "btn btn-primary btn-sm", 
                            data: { 
                                toggle: "modal", 
                                target: "#json_modal_#{datum.uid}",
                                uid: datum.uid
                            }, 
                            onclick: "loadJsonData(#{datum.uid})" %>
                    </td>
                    <td>
                    </td>
                </tr>
                <% end %>
                <% else %>
                <tr>
                    <td colspan="7" class="text-center">検索結果が見つかりませんでした。</td>
                </tr>
                <% end %>
            </tbody>
        </table>
    </div>
    <div class="card-footer">
        <%= submit_tag "選択した項目を削除", data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" },class: "btn btn-danger" %>
    </div>
</div>
<% end %>

<!-- JSON表示用モーダル -->
<div class="modal fade" id="json_modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jsonModalLabel">マスターデータ JSON</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <pre id="json_content" style="max-height: 500px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">閉じる</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        const selectAll = document.getElementById("select_all");
        const checkboxes = document.querySelectorAll(".select_item");

        selectAll.addEventListener("change", (e) => {
            checkboxes.forEach(cb => cb.checked = e.target.checked);
        });
    });

    function loadJsonData(uid) {
        fetch(`/master_data/${uid}/show_json.json`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('json_content').textContent = JSON.stringify(data, null, 2);
                document.getElementById('jsonModalLabel').textContent = `マスターデータ JSON (UID: ${uid})`;
                
                // モーダルを表示
                const modal = new bootstrap.Modal(document.getElementById('json_modal'));
                modal.show();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('JSONデータの取得に失敗しました。');
            });
    }
</script>