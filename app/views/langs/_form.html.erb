<%= form_with(model: lang, url: lang.persisted? ? lang_path(lang.uid) : langs_path) do |form| %>
  <% if lang.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(lang.errors.count, "error") %> prohibited this lang from being saved:</h4>
      <ul>
        <% lang.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mb-3">
    <%= form.label :uid, "UID" %>
    <%= form.number_field :uid %>
  </div>

  <div class="mb-3">
    <%= form.label :locale, "言語を選択", class: "form-label" %>
    <%= form.select :locale,
                   Lang.options_for_select,
                   { include_blank: "選択してください" },
                   { class: "form-select" } %>
    <div class="form-text">ISO 639-1 言語コードに基づく言語を選択してください</div>
  </div>

  <div class="mb-3">
    <%= form.label :position, "順序", class: "form-label" %>
    <%= form.number_field :position, class: "form-control" %>
  </div>

  <div class="actions">
    <%= form.submit "保存", class: "btn btn-primary" %>
    <%= link_to "戻る", langs_path, class: "btn btn-secondary ms-2" %>
  </div>
<% end %> 