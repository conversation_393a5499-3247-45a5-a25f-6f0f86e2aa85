<h1>対戦記録詳細</h1>

<div class="card">
  <div class="card-body">
    <h5 class="card-title"><%= get_player_name(@match.try(:player_0)) %> vs <%= get_player_name(@match.try(:player_1)) %></h5>
    
    <div class="row">
      <div class="col-md-6">
        <p>
          <strong>プレイヤー0:</strong>
          <%= get_player_link(@match.try(:player_0)) %>
        </p>

        <p>
          <strong>使用種族0:</strong>
          <%= @match.group_0 %>
        </p>

        <p>
          <strong>デッキ0:</strong>
          <pre><%= @match.deck_0 %></pre>
        </p>

        <p>
          <strong>マッチング時間0:</strong>
          <%= @match.matching_time_0 %> 秒
        </p>

        <p>
          <strong>対戦前レーティング0:</strong>
          <%= @match.before_rate_0 %>
        </p>

        <p>
          <strong>対戦後レーティング0:</strong>
          <%= @match.after_rate_0 %>
        </p>
      </div>

      <div class="col-md-6">
        <p>
          <strong>プレイヤー1:</strong>
          <%= get_player_link(@match.try(:player_1)) %>
        </p>

        <p>
          <strong>使用種族1:</strong>
          <%= @match.group_1 %>
        </p>

        <p>
          <strong>デッキ1:</strong>
          <pre><%= @match.deck_1 %></pre>
        </p>

        <p>
          <strong>マッチング時間1:</strong>
          <%= @match.matching_time_1 %> 秒
        </p>

        <p>
          <strong>対戦前レーティング1:</strong>
          <%= @match.before_rate_1 %>
        </p>

        <p>
          <strong>対戦後レーティング1:</strong>
          <%= @match.after_rate_1 %>
        </p>
      </div>
    </div>

    <div class="row mt-4">
      <div class="col-md-12">
        <p>
          <strong>結果:</strong>
          <%= @match.result == 'win' ? '勝利' : '敗北' %>
        </p>

        <p>
          <strong>対戦日時:</strong>
          <%= @match.played_at %>
        </p>

        <p>
          <strong>ゲームモード:</strong>
          <%= display_game_mode(@match.game_mode) %>
        </p>

        <p>
          <strong>イベントID:</strong>
          <%= @match.event_id %>
        </p>

        <p>
          <strong>ランク:</strong>
          <%= @match.rank %>
        </p>

        <% if @match.replay_data.present? %>
          <p>
            <strong>リプレイデータ:</strong>
            <pre><%= JSON.pretty_generate(@match.replay_data) rescue @match.replay_data.to_s %></pre>
          </p>
        <% end %>

        <% if @match.other_data.present? %>
          <p>
            <strong>その他データ:</strong>
            <pre><%= JSON.pretty_generate(@match.other_data) rescue @match.other_data.to_s %></pre>
          </p>
        <% end %>
      </div>
    </div>
  </div>
</div>

<div class="mt-3">
  <%= link_to '編集', edit_match_path(@match), class: 'btn btn-primary' %> |
  <%= link_to '戻る', matches_path, class: 'btn btn-secondary' %>
</div>
