<h1>対戦記録一覧</h1>

<table class="table table-striped">
  <thead>
    <tr>
      <th>プレイヤー0</th>
      <th>プレイヤー1</th>
      <th>結果</th>
      <th>対戦日時</th>
      <th>ゲームモード</th>
      <th>イベントID</th>
      <th>ランク</th>
      <th colspan="3">操作</th>
    </tr>
  </thead>

  <tbody>
    <% @matches.each do |match| %>
      <tr>
        <td><%= match.player_0 %></td>
        <td><%= match.player_1 %></td>
        <td><%= match.result %></td>
        <td><%= match.played_at %></td>
        <td><%= display_game_mode(match.game_mode) %></td>
        <td><%= match.event_id %></td>
        <td><%= match.rank %></td>
        <td><%= link_to '詳細', match %></td>
        <td><%= link_to '編集', edit_match_path(match) %></td>
        <td><%= link_to '削除', match, data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" },class: "btn btn-danger btn-sm" %></td>
      </tr>
    <% end %>
  </tbody>
</table>

<br>

<%= link_to '新規対戦記録', new_match_path, class: 'btn btn-primary' %>
