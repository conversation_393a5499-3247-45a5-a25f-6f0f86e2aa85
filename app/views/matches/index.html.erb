<h1>対戦記録一覧</h1>

<!-- 検索フォーム -->
<div class="card mb-4">
  <div class="card-header">
    <h5>検索・フィルタ</h5>
  </div>
  <div class="card-body">
    <%= form_with url: matches_path, method: :get, local: true, class: "row g-3" do |form| %>
      <div class="col-md-3">
        <%= form.label :player_id, "プレイヤーID", class: "form-label" %>
        <%= form.text_field :player_id, value: params[:player_id], class: "form-control", placeholder: "プレイヤーID" %>
      </div>
      
      <div class="col-md-3">
        <%= form.label :game_mode, "ゲームモード", class: "form-label" %>
        <%= form.select :game_mode, 
            options_for_select([
              ['すべて', ''],
              ['ランクマッチ(rank)', 'rank'],
              ['フリー(free)', 'free'],
              ['ルーム(room)', 'room'],
              ['イベント(event)', 'event']
            ], params[:game_mode]), 
            {}, { class: "form-select" } %>
      </div>
      
      <div class="col-md-3">
        <%= form.label :start_date, "開始日", class: "form-label" %>
        <%= form.date_field :start_date, value: params[:start_date], class: "form-control" %>
      </div>
      
      <div class="col-md-3">
        <%= form.label :end_date, "終了日", class: "form-label" %>
        <%= form.date_field :end_date, value: params[:end_date], class: "form-control" %>
      </div>
      
      <div class="col-12">
        <%= form.submit "検索", class: "btn btn-primary" %>
        <%= link_to "リセット", matches_path, class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
</div>

<!-- 結果件数表示 -->
<p class="text-muted">
  <%= @matches.total_count %>件中 <%= @matches.offset_value + 1 %>〜<%= [@matches.offset_value + @matches.limit_value, @matches.total_count].min %>件を表示
</p>

<table class="table table-striped">
  <thead>
    <tr>
      <th>プレイヤー0</th>
      <th>プレイヤー1</th>
      <th>結果</th>
      <th>対戦日時</th>
      <th>ゲームモード</th>
      <th>イベントID</th>
      <th>ランク</th>
      <th colspan="3">操作</th>
    </tr>
  </thead>

  <tbody>
    <% @matches.each do |match| %>
      <tr>
        <td><%= get_player_link(match.try(:player_0)) %></td>
        <td><%= get_player_link(match.try(:player_1)) %></td>
        <td><%= match.result %></td>
        <td><%= match.played_at %></td>
        <td><%= display_game_mode(match.game_mode) %></td>
        <td><%= match.event_id %></td>
        <td><%= match.rank %></td>
        <td><%= link_to '詳細', match %></td>
        <td><%= link_to '編集', edit_match_path(match) %></td>
        <td><%= link_to '削除', match, data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" },class: "btn btn-danger btn-sm" %></td>
      </tr>
    <% end %>
  </tbody>
</table>

<!-- ページング -->
<div class="d-flex justify-content-center">
  <%= paginate @matches %>
</div>

<br>

<%= link_to '新規対戦記録', new_match_path, class: 'btn btn-primary' %>
