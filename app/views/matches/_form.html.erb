<%= form_with(model: match, local: true) do |form| %>
  <% if match.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(match.errors.count, "error") %> prohibited this match from being saved:</h2>

      <ul>
        <% match.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= form.label :player_0, 'プレイヤー0' %>
    <%= form.text_field :player_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :player_1, 'プレイヤー1' %>
    <%= form.text_field :player_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :result, '結果' %>
    <%= form.select :result, [['プレイヤー0勝利 (win_0)', 'win_0'], ['プレイヤー1勝利 (win_1)', 'win_1'], ['引き分け (draw)', 'draw']], {}, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :reason, '理由' %>
    <%= form.select :reason, [['ライフ0 (life_0)', 'life_0'], ['デッキ枚数0 (deck_hand_0)', 'deck_hand_0'], ['ターン数上限 (turn_limit)', 'turn_limit'], ['降参 (surrender)', 'surrender'], ['切断 (disconnected)', 'disconnected'], ['対戦不成立 (match_not_started)', 'match_not_started']], {}, class: 'form-control' %>
  </div>


  <div class="field">
    <%= form.label :played_at, '対戦日時' %>
    <%= form.datetime_field :played_at, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :game_mode, 'ゲームモード' %>
    <%= form.select :game_mode, [['ランク(rank)', 'rank'], ['フリーマッチ(free)', 'free'], ['ルーム(room)', 'room'], ['イベント(event)', 'event']], {}, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :event_id, 'イベントID' %>
    <%= form.number_field :event_id, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :matching_time_0, 'マッチング時間0' %>
    <%= form.number_field :matching_time_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :matching_time_1, 'マッチング時間1' %>
    <%= form.number_field :matching_time_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :deck_0, 'デッキ0' %>
    <%= form.text_area :deck_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :deck_1, 'デッキ1' %>
    <%= form.text_area :deck_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :group_0, '使用種族0' %>
    <%= form.text_field :group_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :group_1, '使用種族1' %>
    <%= form.text_field :group_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :rank, 'ランク' %>
    <%= form.text_field :rank, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :before_rate_0, '対戦前レーティング0' %>
    <%= form.number_field :before_rate_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :before_rate_1, '対戦前レーティング1' %>
    <%= form.number_field :before_rate_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :after_rate_0, '対戦後レーティング0' %>
    <%= form.number_field :after_rate_0, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :after_rate_1, '対戦後レーティング1' %>
    <%= form.number_field :after_rate_1, class: 'form-control' %>
  </div>

  <div class="field">
    <%= form.label :replay_data, 'リプレイデータ' %>
    <%= form.text_area :replay_data, class: 'form-control', rows: 10 %>
  </div>

  <div class="field">
    <%= form.label :other_data, 'その他データ' %>
    <%= form.text_area :other_data, class: 'form-control', rows: 10 %>
  </div>

  <div class="actions">
    <%= form.submit '保存', class: 'btn btn-primary' %>
  </div>
<% end %> 