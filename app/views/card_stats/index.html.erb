<h1>カードスタッツ</h1>

<%= form_tag card_stats_create_daily_status_path, method: :post do %>
  <%= submit_tag "日次スタッツを作成", class: "btn btn-primary" %>
<% end %>

<div class="mt-4">
  <h2>日次カードスタッツ一覧</h2>
  <div class="table-responsive">
    <table class="table table-striped table-bordered">
      <thead class="thead-dark">
        <tr>
          <th>カードID</th>
          <th>カード名</th>
          <th>日付</th>
          <th>使用回数</th>
          <th>総使用枚数</th>
          <th>1枚使用回数</th>
          <th>2枚使用回数</th>
          <th>3枚使用回数</th>
          <th>3枚超使用回数</th>
          <th>勝利数</th>
          <th>敗北数</th>
          <th>引分数</th>
          <th>勝率</th>
        </tr>
      </thead>
      <tbody>
        <% @card_stats_dailies.each do |stat| %>
          <tr>
            <td><%= stat.card_id %></td>
            <td><%= MasterDatum.get_card(stat.card_id)&.dig("Name") %></td>
            <td><%= stat.date %></td>
            <td><%= stat.use_count %></td>
            <td><%= stat.total_used %></td>
            <td><%= stat.use_1_count %></td>
            <td><%= stat.use_2_count %></td>
            <td><%= stat.use_3_count %></td>
            <td><%= stat.use_over_3_count %></td>
            <td><%= stat.win_count %></td>
            <td><%= stat.lose_count %></td>
            <td><%= stat.draw_count %></td>
            <td><%= number_to_percentage(stat.win_count.to_f / (stat.win_count + stat.lose_count) * 100, precision: 1) if stat.win_count + stat.lose_count > 0 %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>

 