<%= form_with(model: deck, url: deck.persisted? ? deck_path(deck.uid) : decks_path) do |form| %>
  <% if deck.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(deck.errors.count, "error") %> prohibited this deck from being saved:</h2>

      <ul>
        <% deck.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :desc, style: "display: block" %>
    <%= form.text_field :desc %>
  </div>

  <div>
    <%= form.hidden_field :data, id: 'deck_data_field' %>
  </div>

  <div>
    <%= form.label :uid, style: "display: block" %>
    <%= form.number_field :uid %>
  </div>

  <div>
    <%= form.label :data, style: "display: block" %>
    <%= form.text_area :data %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>