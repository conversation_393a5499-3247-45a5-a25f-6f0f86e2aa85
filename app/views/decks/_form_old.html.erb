<%= form_with(model: deck, url: deck.persisted? ? deck_path(deck.uid) : decks_path) do |form| %>
  <% if deck.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(deck.errors.count, "error") %> prohibited this deck from being saved:</h2>

      <ul>
        <% deck.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name %>
  </div>

  <div>
    <%= form.label :desc, style: "display: block" %>
    <%= form.text_field :desc %>
  </div>

  <div>
    <%= form.hidden_field :data, id: 'deck_data_field' %>
  </div>

  <div>
    <%= form.label :uid, style: "display: block" %>
    <%= form.number_field :uid %>
  </div>

  <%# データを JavaScript 変数として定義 %>
  <script>
    document.addEventListener('alpine:init', () => {
      Alpine.data('deckEditor', () => ({
        data: <%= raw (deck.data.present? ? deck.data : '{ "g": 1, "cd": "" }') %>,
        cards: [],
        deckCode: '',
        
        init() {
          this.parseCards();
          // フォーム送信前にデータを更新
          const form = document.querySelector('form');
          form.addEventListener('submit', () => {
            document.getElementById('deck_data_field').value = JSON.stringify(this.data);
          });
          
          // 初期デッキコードを生成
          this.generateDeckCode();
        },
        
        parseCards() {
          if (!this.data.cd) {
            this.cards = [];
            return;
          }
          
          this.cards = this.data.cd.split(',').map(card => {
            const cardNumber = card.substring(0, 5);
            const premium = parseInt(card.substring(5, 6)) || 0;
            const shine = parseInt(card.substring(6, 7)) || 0;
            const normal = parseInt(card.substring(7, 8)) || 0;
            
            return { cardNumber, premium, shine, normal };
          });
        },
        
        updateData() {
          this.data.cd = this.cards.map(card => 
            `${card.cardNumber}${card.premium}${card.shine}${card.normal}`
          ).join(',');
          // データが更新されるたびにhidden fieldも更新
          document.getElementById('deck_data_field').value = JSON.stringify(this.data);
          // デッキコードも更新
          this.generateDeckCode();
        },
        
        generateDeckCode() {
          // JSONデータをそのままデッキコードとして使用
          this.deckCode = JSON.stringify(this.data);
        },
        
        importDeckCode() {
          try {
            // JSONデータとしてパース
            let newData = JSON.parse(this.deckCode);
            
            // 必須フィールドの確認
            if (newData && typeof newData === 'object') {
              // gフィールドの確認と修正
              if (typeof newData.g !== 'number' || isNaN(newData.g)) {
                newData.g = 1;
              }
              
              // cdフィールドの確認
              if (typeof newData.cd !== 'string') {
                newData.cd = '';
              }
              
              // データを更新
              this.data = newData;
              this.parseCards();
              this.updateData();
            } else {
              throw new Error('無効なデータ形式です');
            }
          } catch (e) {
            alert('デッキコードの形式が正しくありません。JSONフォーマットで入力してください。');
            console.error('デッキコードのパースエラー:', e);
          }
        },
        
        addCard() {
          this.cards.push({ cardNumber: '00000', premium: 0, shine: 0, normal: 0 });
          this.updateData();
        },
        
        removeCard(index) {
          this.cards.splice(index, 1);
          this.updateData();
        },
        
        updateCard(index, field, value) {
          if (field === 'cardNumber') {
            // カード番号が5桁になるように調整
            value = value.padStart(5, '0').substring(0, 5);
          } else {
            // 最大3枚までに制限
            value = Math.min(Math.max(0, parseInt(value) || 0), 3);
          }
          
          this.cards[index][field] = value;
          this.updateData();
        }
      }));
    });
  </script>

  <div x-data="deckEditor">
    <h3>デッキデータ編集</h3>
    
    <div class="mb-3">
      <label>デッキコード:</label>
      <div class="input-group">
        <input type="text" x-model="deckCode" class="form-control" placeholder="デッキコードを入力または貼り付け">
        <button @click="importDeckCode()" class="btn btn-secondary" type="button">インポート</button>
      </div>
      <small class="form-text text-muted">形式: {"g":4,"cd":"90003001,40005003,..."}のようなJSON形式</small>
    </div>
    
    <div class="mb-3">
      <label>種族:</label>
      <select x-model="data.g" class="form-select" @change="updateData()">
        <option value="1">ドラゴン</option>
        <option value="2">アンドロイド</option>
        <option value="3">エレメンタル</option>
        <option value="4">ルミナス</option>
        <option value="5">シェイド</option>
      </select>
    </div>
    
    
<%= render partial: "shared/card_edition_select_inline", locals: { } %>
    
    <div class="mb-3">
      <strong>プレビュー:</strong>
      <pre x-text="JSON.stringify(data, null, 2)" x-effect="$watch('data', value => { /* デバッグ用 */ console.log('データ更新:', value) })"></pre>
    </div>

    <div>
      <%= form.submit %>
    </div>
  </div>
<% end %>