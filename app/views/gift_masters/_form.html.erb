<%= form_with(model: gift_master, url: gift_master.persisted? ? gift_master_path(gift_master.uid) : gift_masters_path) do |form| %>
  <% if gift_master.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(gift_master.errors.count, "error") %> prohibited this gift_master from being saved:</h4>
      <ul>
        <% gift_master.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group mb-3">
    <p>uidは自動生成されます。</p>
  </div>

  <div class="card">
    <div class="card-header">
      <h5>配布対象</h5>
    </div>
    <div class="card-body">
      <div>
        <%= form.label :user_id, "ユーザーID（空の場合は全員ギフト）" %><br>
        <%= form.text_field :user_id, placeholder: "例: 100000000000000" %>
      </div>
    </div>
  </div>

  <div class="card p-3 mb-4">
    <div class="card-header">
      <h5>ギフト内容</h5>
    </div>
    <div class="card-body">
      <%= json_lang_form(form, :name, label: "ギフトの名前") %>
      <%= json_lang_form(form, :desc, label: "説明", as: :text_area) %>
    </div>
  </div>

  <div>
    <%= render 'shared/rewards', rewards: gift_master.rewards, param_name: 'gift_master[rewards]' %>
  </div>

  <div class="card p-3 mb-4">
    <%= render 'shared/period', form: form %>
  </div>

  <div class="actions">
    <%= form.submit 'ギフトを作成', class: 'btn btn-primary' %>
  </div>
<% end %>
