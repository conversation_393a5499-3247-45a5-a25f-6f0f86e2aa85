<% content_for :page_title, "マスターデータ管理" %>

<div class="mb-4">
    <%= button_to '最新取得', master_data_create_path, method: :post, class: "btn btn-primary" %>
</div>

<%= form_with url: multi_delete_master_data_path, method: :delete, local: true do %>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">マスターデータ一覧</h3>
    </div>
    <div class="card-body">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th>
                        <input type="checkbox" id="select_all" />
                    </th>
                    <th>UID</th>
                    <th>ID</th>
                    <th>概要</th>
                    <th>変更内容</th>
                    <th>作成日</th>
                    <th>編集</th>
                    <th>削除</th>
                </tr>
            </thead>
            <tbody>
                <% if @data.present? %>
                <% @data.each do |datum| %>
                <tr>
                    <td>
                        <%= check_box_tag "uids[]", datum.uid, false, class: "select_item" %>
                    </td>
                    <td><%= datum.uid %></td>
                    <td><%= datum.title %></td>
                    <td><%= datum.desc %></td>
                    <td><%= time_ago_in_words(datum.created_at) %></td>
                    <td>
                        <%= link_to "編集", master_datum_path(datum.uid), class: "btn btn-info btn-sm" %>
                    </td>
                    <td>
                        <%= link_to "削除", master_datum_path(datum.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: "btn btn-danger btn-sm" %>
                    </td>
                </tr>
                <% end %>
                <% else %>
                <tr>
                    <td colspan="7" class="text-center">検索結果が見つかりませんでした。</td>
                </tr>
                <% end %>
            </tbody>
        </table>
    </div>
    <div class="card-footer">
        <%= submit_tag "選択した項目を削除", class: "btn btn-danger", data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" } %>
    </div>
</div>
<% end %>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        const selectAll = document.getElementById("select_all");
        const checkboxes = document.querySelectorAll(".select_item");

        selectAll.addEventListener("change", (e) => {
            checkboxes.forEach(cb => cb.checked = e.target.checked);
        });
    });
</script>