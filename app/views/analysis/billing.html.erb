<% content_for :title, "課金分析" %>

<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <section class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6">
          <h1>課金分析</h1>
        </div>
        <div class="col-sm-6">
          <ol class="breadcrumb float-sm-right">
            <li class="breadcrumb-item"><%= link_to "ホーム", root_path %></li>
            <li class="breadcrumb-item"><%= link_to "分析", analysis_index_path %></li>
            <li class="breadcrumb-item active">課金分析</li>
          </ol>
        </div>
      </div>
    </div>
  </section>

  <!-- Main content -->
  <section class="content">
    <div class="container-fluid">
      <!-- フィルター部分 -->
      <div class="row mb-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">分析設定</h3>
            </div>
            <div class="card-body">
              <%= form_with url: billing_analysis_index_path, method: :get, local: true, class: "row" do |form| %>
                <!-- 表示形式切り替え -->
                <div class="col-md-3">
                  <%= form.label :view_type, "表示形式", class: "form-label" %>
                  <%= form.select :view_type, 
                      options_for_select([['日別', 'daily'], ['月別', 'monthly'], ['時間別', 'hourly']], @view_type),
                      {}, { class: "form-control" } %>
                  <% if @view_type == 'hourly' %>
                    <small class="text-muted">
                      ※時間別は最大7日間<br>
                      ・両方指定: 7日超過時は終了日から7日前<br>
                      ・開始日のみ: 開始日から7日間<br>
                      ・終了日のみ: 終了日から7日前<br>
                      ・未指定: 直近7日間
                    </small>
                  <% end %>
                </div>

                <!-- プラットフォーム切り替え -->
                <div class="col-md-3">
                  <%= form.label :platform, "プラットフォーム", class: "form-label" %>
                  <%= form.select :platform, 
                      options_for_select([['全プラットフォーム', 'all'], ['iOS', 'ios'], ['Android', 'android']], @platform),
                      {}, { class: "form-control" } %>
                </div>

                <!-- 開始日 -->
                <div class="col-md-2">
                  <%= form.label :start_date, "開始日", class: "form-label" %>
                  <%= form.date_field :start_date, value: @start_date, class: "form-control" %>
                </div>

                <!-- 終了日 -->
                <div class="col-md-2">
                  <%= form.label :end_date, "終了日", class: "form-label" %>
                  <%= form.date_field :end_date, value: @end_date, class: "form-control" %>
                </div>

                <!-- 検索ボタン -->
                <div class="col-md-2 d-flex align-items-end">
                  <%= form.submit "分析", class: "btn btn-primary" %>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- サマリー情報 -->
      <div class="row mb-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                期間サマリー 
                (<%= @start_date.strftime('%Y/%m/%d') %> - <%= @end_date.strftime('%Y/%m/%d') %>) 
                - <%= @platform == 'all' ? '全プラットフォーム' : (@platform == 'ios' ? 'iOS' : 'Android') %>
                <% if @view_type == 'hourly' %>
                  <small class="text-info">【7日間制限適用】</small>
                <% end %>
              </h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4">
                  <div class="info-box">
                    <span class="info-box-icon bg-success"><i class="fas fa-yen-sign"></i></span>
                    <div class="info-box-content">
                      <span class="info-box-text">総売上</span>
                      <span class="info-box-number">¥<%= number_with_delimiter(@total_summary[:total_amount]) %></span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="info-box">
                    <span class="info-box-icon bg-info"><i class="fas fa-shopping-cart"></i></span>
                    <div class="info-box-content">
                      <span class="info-box-text">総購入数</span>
                      <span class="info-box-number"><%= number_with_delimiter(@total_summary[:total_count]) %></span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="info-box">
                    <span class="info-box-icon bg-warning"><i class="fas fa-calculator"></i></span>
                    <div class="info-box-content">
                      <span class="info-box-text">平均購入額</span>
                      <span class="info-box-number">¥<%= @total_summary[:total_count] > 0 ? number_with_delimiter((@total_summary[:total_amount] / @total_summary[:total_count]).round) : 0 %></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- プロダクト別サマリー -->
      <div class="row mb-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                プロダクト別サマリー 
                - <%= @platform == 'all' ? '全プラットフォーム' : (@platform == 'ios' ? 'iOS' : 'Android') %>
              </h3>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>プロダクトID</th>
                      <th>単価</th>
                      <th>購入数</th>
                      <th>売上額</th>
                      <th>シェア</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @total_summary[:product_totals].sort_by { |k, v| -v[:amount] }.each do |product_key, data| %>
                      <tr>
                        <td><%= product_key %></td>
                        <td>¥<%= number_with_delimiter(data[:unit_price]) %></td>
                        <td><%= number_with_delimiter(data[:count]) %></td>
                        <td><strong>¥<%= number_with_delimiter(data[:amount]) %></strong></td>
                        <td>
                          <% share_percentage = @total_summary[:total_amount] > 0 ? ((data[:amount].to_f / @total_summary[:total_amount]) * 100).round(1) : 0 %>
                          <%= share_percentage %>%
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 期間別詳細データ -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <%= @view_type == 'monthly' ? '月別' : @view_type == 'hourly' ? '時間別' : '日別' %>詳細データ 
                - <%= @platform == 'all' ? '全プラットフォーム' : (@platform == 'ios' ? 'iOS' : 'Android') %>
              </h3>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th><%= @view_type == 'hourly' ? '時間' : '日付' %></th>
                      <th>売上(円)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- 合計行 -->
                    <tr class="table-success">
                      <td><strong>合計</strong></td>
                      <td><strong>¥<%= number_with_delimiter(@total_summary[:total_amount]) %></strong></td>
                    </tr>
                    
                    <!-- データ行 -->
                    <% if @billing_data.empty? %>
                      <tr>
                        <td colspan="2" class="text-center">
                          データがありません
                        </td>
                      </tr>
                    <% else %>
                      <% @billing_data.each do |period_data| %>
                        <tr>
                          <td><%= period_data[:period] %></td>
                          <td>¥<%= number_with_delimiter(period_data[:total_amount]) %></td>
                        </tr>
                      <% end %>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<style>
.info-box {
  margin-bottom: 0;
}

.table th {
  white-space: nowrap;
}

.table td {
  vertical-align: middle;
}

<% if @view_type == 'hourly' %>
.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}

.table td:first-child {
  font-size: 0.9em;
}
<% end %>
</style> 