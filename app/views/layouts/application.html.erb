<!DOCTYPE html>
<html>

<head>
    <title>T1管理サイト</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= javascript_importmap_tags %>

    <%= stylesheet_link_tag 'application', media: 'all', 'data-turbolinks-track': 'reload' %>
    <%= javascript_include_tag 'application', 'data-turbolinks-track': 'reload' %>
    
    <!-- 開発環境では非minifiedバージョンを使用 -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/alpine.js"></script>
    <!-- デバッグモードを有効化 -->
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        window.Alpine = window.Alpine || {};
        window.Alpine.debug = true;
      });
    </script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@rails/actioncable@7.0.0/app/assets/javascripts/actioncable.js"></script>

    <!-- UserStatusWebSocket Manager - Load early for all user_status pages -->
    <% if master_user_signed_in? %>
    <script>
      // Initialize UserStatusWebSocket manager early
      window.UserStatusWebSocket = {
        cable: null,
        subscriptions: {},
        currentContext: null,
        callbacks: {},

        connect: function(context, callbacks) {
          console.log("UserStatusWebSocket.connect called for context:", context);

          this.currentContext = context;
          this.callbacks = callbacks || {};

          if (!this.cable) {
            this.setupCable();
          }

          this.setupSubscriptions();

          // Immediately call connection change if we have a callback
          if (this.callbacks.onConnectionChange) {
            // Give a small delay to let the connection establish
            setTimeout(() => {
              if (this.cable && this.cable.connection && this.cable.connection.isOpen()) {
                this.callbacks.onConnectionChange('connected');
              } else {
                this.callbacks.onConnectionChange('connecting');
              }
            }, 100);
          }
        },

        setupCable: function() {
          const adminId = <%= current_master_user&.id || 'null' %>;

          let wsUrl = '/cable?client_type=admin';
          if (adminId) {
            wsUrl += '&token=admin-' + adminId;
          }

          console.log("Creating ActionCable consumer at:", wsUrl);
          this.cable = ActionCable.createConsumer(wsUrl);
        },

        setupSubscriptions: function() {
          const self = this;

          // Admin channel
          if (!this.subscriptions.admin) {
            this.subscriptions.admin = this.cable.subscriptions.create("Admin::UserStatusChannel", {
              connected() {
                console.log("Connected to Admin::UserStatusChannel!");
                if (self.callbacks.onConnectionChange) {
                  self.callbacks.onConnectionChange('connected');
                }
              },

              disconnected() {
                console.log("Disconnected from Admin::UserStatusChannel");
                if (self.callbacks.onConnectionChange) {
                  self.callbacks.onConnectionChange('disconnected');
                }
              },

              received(data) {
                console.log("Received admin data:", data);
                if (self.callbacks.onAdminData) {
                  self.callbacks.onAdminData(data);
                }
              }
            });
          }

          // Rank match channel
          if (!this.subscriptions.rank) {
            this.subscriptions.rank = this.cable.subscriptions.create(
              { channel: "Admin::MatchStatusChannel", match_type: "rank" },
              {
                received(data) {
                  console.log("Received rank match data:", data);
                  if (self.callbacks.onRankData) {
                    self.callbacks.onRankData(data);
                  }
                }
              }
            );
          }

          // Free match channel
          if (!this.subscriptions.free) {
            this.subscriptions.free = this.cable.subscriptions.create(
              { channel: "Admin::MatchStatusChannel", match_type: "free" },
              {
                received(data) {
                  console.log("Received free match data:", data);
                  if (self.callbacks.onFreeData) {
                    self.callbacks.onFreeData(data);
                  }
                }
              }
            );
          }

          // Room match channel
          if (!this.subscriptions.room) {
            this.subscriptions.room = this.cable.subscriptions.create(
              { channel: "Admin::MatchStatusChannel", match_type: "room" },
              {
                received(data) {
                  console.log("Received room match data:", data);
                  if (self.callbacks.onRoomData) {
                    self.callbacks.onRoomData(data);
                  }
                }
              }
            );
          }
        },

        disconnect: function() {
          console.log("UserStatusWebSocket.disconnect called");
          if (this.cable) {
            this.cable.disconnect();
            this.cable = null;
            this.subscriptions = {};
          }
        }
      };

      // Set current master user ID
      window.currentMasterUserId = <%= current_master_user&.id || 'null' %>;

      console.log("UserStatusWebSocket manager initialized");
    </script>
    <% end %>

    <!-- AdminLTE CSS -->
    <%= stylesheet_link_tag "https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css", media: "all", "data-turbo-track": "reload" %>
    <!-- Font Awesome -->
    <%= stylesheet_link_tag "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css", media: "all", "data-turbo-track": "reload" %>
</head>
<% if master_user_signed_in? %>

<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">

        <!-- ヘッダー -->
        <%= render 'shared/header' %>
        <!-- サイドバー -->
        <%= render 'shared/sidebar' %>


        <!-- メインコンテンツ -->
        <div class="content-wrapper">

            <!-- コンテンツヘッダー -->
            <%= render 'shared/content_header', page_title: yield(:page_title) %>

            <div class="content">
                <div class="container-fluid">

                    <!-- フラッシュメッセージ -->
                    <% flash.each do |key, message| %>
                    <div class="alert alert-<%= key == "notice" ? "success" : "danger" %>">
                        <%= message %>
                    </div>
                    <% end %>


                    <!-- コンテンツ -->
                    <%= yield %>
                </div>
            </div>
        </div>

        <!-- フッター -->
        <%= render 'shared/footer' %>
    </div>

    <!-- 必要なスクリプト -->
    <%= javascript_include_tag "https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "https://cdnjs.cloudflare.com/ajax/libs/bootstrap/4.6.2/js/bootstrap.bundle.min.js", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js", "data-turbo-track": "reload", defer: true %>
</body>

<% else %>
<%= yield %>
<% end %>

</html>
