<h1>宝箱詳細</h1>

<div class="card mb-4">
  <div class="card-header">
    <strong><%= @chest.category %> 宝箱</strong> (UID: <%= @chest.uid %>)
  </div>
  <div class="card-body">
    <h5 class="card-title">報酬一覧</h5>
    <% if @chest.rewards.present? %>
      <div class="row">
        <% @chest.rewards.each do |reward| %>
          <div class="col-md-6 col-lg-4 mb-3">
            <div class="card h-100">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="m-0">
                  <% icon_class = case reward["item_type"] 
                      when 'dia' then 'fas fa-gem'
                      when 'icon' then 'fas fa-user-circle'
                      when 'icon_frame' then 'far fa-circle'
                      when 'card_sleeve' then 'fas fa-window-restore'
                      when 'playmat' then 'fas fa-chess-board'
                      when 'bgm' then 'fas fa-compact-disc'
                      when 'title' then 'fas fa-crown'
                      when 'chest' then 'fas fa-box'
                      when 'pack_ticket' then 'fas fa-ticket-alt'
                      when 'prebuilt_deck' then 'fas fa-layer-group'
                      else 'fas fa-question'
                    end %>
                  <i class="<%= icon_class %> mr-1"></i>
                  <%= reward["item_type"] %>
                </h6>
                <% if reward["ext"].present? && reward["ext"]["rate"].present? %>
                  <span class="badge badge-info"><%= reward["ext"]["rate"] %>%</span>
                <% end %>
              </div>
              <div class="card-body">
                <ul class="list-unstyled mb-0">
                  <li><strong>アイテムID:</strong> <%= reward["item_id"] %></li>
                  <% if ['dia', 'pack_ticket', 'card'].include?(reward["item_type"]) %>
                    <li><strong>個数:</strong> <%= reward["count"] %></li>
                  <% end %>
                  <% if reward["ext"].present? %>
                    <% if reward["ext"]["use_range"] %>
                      <li><strong>範囲:</strong> <%= reward["ext"]["min"] %> 〜 <%= reward["ext"]["max"] %></li>
                    <% end %>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <p class="card-text">報酬は設定されていません。</p>
    <% end %>
  </div>
  <div class="card-footer">
    <small class="text-muted">作成日時: <%= @chest.created_at %> / 更新日時: <%= @chest.updated_at %></small>
  </div>
</div>

<div class="d-flex gap-2">
  <%= link_to "編集", edit_chest_path(@chest.uid), class: "btn btn-primary" %>
  <%= link_to "削除", chest_path(@chest.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: "btn btn-danger" %>
  <%= link_to "一覧に戻る", chests_path, class: "btn btn-secondary" %>
</div> 