<%= form_with(model: chest, local: true) do |form| %>
  <% if chest.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(chest.errors.count, "error") %> prohibited this chest from being saved:</h2>
      <ul>
        <% chest.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="card mb-4">
    <div class="card-header">
      <h5>基本情報</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6 mb-3">
          <%= form.label :uid, class: "form-label" %>
          <%= form.number_field :uid, class: "form-control" %>
        </div>
        <div class="col-md-6 mb-3">
          <%= form.label :category, "カテゴリ", class: "form-label" %>
          <%= form.select :category, ["wood", "silver", "gold", "rainbow"], { include_blank: "カテゴリを選択してください" }, class: "form-select" %>
        </div>
      </div>
    </div>
  </div>

  <%= render 'shared/rewards', rewards: chest.rewards, param_name: 'chest[rewards]', is_random: true %>

  <div class="mb-3">
    <%= form.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 