<h1>宝箱一覧</h1>
<%= link_to "新規宝箱作成", new_chest_path, class: "btn btn-success mt-3" %> 
<div class="row mb-4">
  <% @chests.each do |chest| %>
    <div class="col-md-6 col-lg-4 mb-4">
      <div class="card h-100">
        <div class="card-header">
          <h5 class="mb-0">
            <% icon_class = case chest.category 
                when 'wood' then 'fas fa-box'
                when 'silver' then 'fas fa-box text-secondary'
                when 'gold' then 'fas fa-box text-warning'
                when 'rainbow' then 'fas fa-box text-primary'
                else 'fas fa-box'
              end %>
            <i class="<%= icon_class %> mr-2"></i>
            <%= chest.category.titleize %> 宝箱
            <small class="text-muted">(UID: <%= chest.uid %>)</small>
          </h5>
        </div>
        <div class="card-body">
          <% if chest.rewards.present? %>
            <h6>報酬概要</h6>
            <div class="row">
              <% chest.rewards.take(3).each do |reward| %>
                <div class="col-sm-4 mb-2">
                  <div class="badge bg-light text-dark p-2 w-100 text-start d-flex align-items-center">
                    <% item_rule = Item.get_rule(reward["item_type"]) %>
                    <% icon_class = case reward["item_type"] 
                        when 'dia' then 'fas fa-gem text-primary'
                        when 'icon' then 'fas fa-user-circle text-success'
                        when 'icon_frame' then 'far fa-circle text-info'
                        when 'card_sleeve' then 'fas fa-window-restore text-warning'
                        when 'playmat' then 'fas fa-chess-board text-danger'
                        when 'bgm' then 'fas fa-compact-disc text-secondary'
                        when 'title' then 'fas fa-crown text-warning'
                        when 'chest' then 'fas fa-box text-dark'
                        when 'pack_ticket' then 'fas fa-ticket-alt text-info'
                        when 'prebuilt_deck' then 'fas fa-layer-group text-success'
                        else 'fas fa-question'
                      end %>
                    <i class="<%= icon_class %> me-1"></i>
                    <small><%= item_rule["name"] %></small>

                    <%# アイテムID表示 %>
                    <% if item_rule["item_id"] %>
                      <small class="ms-auto">(ID: <%= reward["item_id"] %>)</small>
                    <% end %>

                    <%# 個数表示 %>
                    <% if item_rule["count"] %>
                      <% if item_rule["range"] && reward["ext"].present? && reward["ext"]["use_range"] %>
                        ×<%= reward["ext"]["min"] %>~<%= reward["ext"]["max"] %>
                      <% else %>
                        ×<%= reward["count"] %>
                      <% end %>
                    <% end %>
                    
                    <%# 確率表示 %>
                    <% if reward["ext"].present? && reward["ext"]["rate"].present? %>
                      <small class="ms-auto">(<%= reward["ext"]["rate"] %>%)</small>
                    <% end %>
                  </div>
                </div>
              <% end %>
              <% if chest.rewards.size > 3 %>
                <div class="col-sm-4 mb-2">
                  <div class="badge bg-secondary text-white p-2 w-100">
                    他 <%= chest.rewards.size - 3 %> 個の報酬
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-muted">報酬なし</p>
          <% end %>
        </div>
        <div class="card-footer">
          <div class="btn-group w-100">
            <%= link_to "詳細", chest_path(chest.uid), class: "btn btn-sm btn-info" %>
            <%= link_to "編集", edit_chest_path(chest.uid), class: "btn btn-sm btn-primary" %>
            <%= link_to "削除", chest_path(chest.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: "btn btn-sm btn-danger" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>