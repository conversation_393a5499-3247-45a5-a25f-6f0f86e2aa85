<%= form_with(model: mission, url: mission.persisted? ? mission_path(mission.uid) : missions_path, local: true) do |f| %>
  <% if mission.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(mission.errors.count, "error") %> prohibited this mission from being saved:</h2>
      <ul>
        <% mission.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= f.label :uid %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :category %>
    <%= f.text_field :category, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :rewards %>
    <%= f.text_area :rewards, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :achieve_cond %>
    <%= f.text_area :achieve_cond, class: 'form-control' %>
  </div>

  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
  </div>
<% end %> 