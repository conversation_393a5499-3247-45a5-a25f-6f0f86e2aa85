<h1>カード修正一覧</h1>

<div class="card">
  <div class="card-body">
    <table class="table table-striped table-bordered">
      <thead class="thead-dark">
        <tr>
          <th>ID</th>
          <th>カードID</th>
          <th>元カード名</th>
          <th>下方修正</th>
          <th>カード名</th>
          <th>種族</th>
          <th>カテゴリー</th>
          <th>役割</th>
          <th>コスト</th>
          <th>パワー</th>
          <th>レア度</th>
          <th>特殊能力</th>
          <th>スクリプト</th>
          <th>種類</th>
          <th>使用状態</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <% @card_modifiers.each do |modifier| %>
          <tr <%= "style='background-color:#00000066'".html_safe if modifier.use != 1 %>>
            <td><%= modifier.modifyKindToStr(true) %></td>
            <td><%= modifier.card_id %></td>
            <td>
                <% card = @all_cards.find { |c| c['ID'] == modifier.card_id } %>
                <%= card ? card['Name'] : '不明' %>
            </td>
            <td>
              <%= "下方修正" if modifier.is_nerf == 1 %>
            </td>
            <td>
              <%= modifier.name %>
            </td>

            <td>
              <% if modifier.group.present? && modifier.group >= 0 %>
                <%= MasterDatum.get_group(modifier.group)&.dig("Name") || '不明' %>
              <% end %>
            </td>

            <td>
              <%= modifier.category %>
            </td>
            

            <td><%= modifier.role %></td>
            <td><%= modifier.cost %></td>
            <td><%= modifier.power %></td>
            <td><%= modifier.rarity if modifier.rarity >= 0 %></td>
            <td><%= modifier.getText(0) %></td>
            <td><%= modifier.getText(1) %></td>
            <td><%= modifier.getText(2) %></td>

            <td><%= modifier.modifyKindToStr(true) %></td>
            <td>
              <% if modifier.use == 1 %>
                <%= link_to '有効', set_unuse_card_modifier_path(modifier), method: :post, class: 'btn btn-sm btn-success' %>
              <% else %>
                <%= link_to '無効', set_use_card_modifier_path(modifier), method: :post, class: 'btn btn-sm btn-secondary' %>
              <% end %>
            </td>
            <td>
              <%= link_to '詳細', card_modifier_path(modifier), class: 'btn btn-sm btn-info' %>
              <%= link_to '編集', edit_card_modifier_path(modifier), class: 'btn btn-sm btn-primary' %>
              <%= link_to '削除', card_modifier_path(modifier), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: 'btn btn-sm btn-danger' %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>

<div class="mt-3">
  <%= link_to '新規追加', new_card_modifier_path, class: 'btn btn-success' %>
</div>
