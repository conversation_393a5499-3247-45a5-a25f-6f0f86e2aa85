<%= form_with(model: card_modifier, local: true) do |form| %>
  <% if card_modifier.errors.any? %>
    <div class="alert alert-danger">
      <h5><i class="icon fas fa-ban"></i> <%= pluralize(card_modifier.errors.count, "error") %> が発生しました：</h5>
      <ul>
      <% card_modifier.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
      </ul>
    </div>
  <% end %>
  <div class="card col-6">
    <div class="card-header">
      <h3 class="card-title">基本設定</h3>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <div class="custom-control custom-checkbox mt-2">
              <%= form.check_box :use, {class: 'custom-control-input', id: 'use', checked: card_modifier.present? ? card_modifier.use == 1 : true}, 1, 0 %>
              <%= form.label :use, "有効化", for: 'use', class: 'custom-control-label' %>
            </div>
          </div>
        </div>
        
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <div class="custom-control custom-checkbox mt-4">
              <%= form.check_box :is_nerf, class: 'custom-control-input', id: 'is_nerf' %>
              <%= form.label :下方修正であればチェック, for: 'is_nerf', class: 'custom-control-label' %>
            </div>
          </div>
        </div>
      </div>

      <div class="row">

        <div class="col-md-6">
          <div class="form-group">
            <%= form.label :用途, class: 'form-label' %>
            <%= form.select :kind, {"修正予定": 0, "追加予定": 1,"修正": 2, "追加": 3, "お試し": 4 }, { selected: card_modifier.kind || 0 }, { class: 'form-control', required: true } %>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <%= form.label :対象カード, class: 'form-label' %>
            <%= form.select :card_id, options_for_select(@all_cards.map{|c| ["#{c["ID"]} #{c["Role"]}/#{c["Cost"]}/#{c["Power"]} #{c["Name"]}" , c["ID"]]}, card_modifier.card_id), {}, {class: 'form-control'} %>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <%= form.label :名前変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-tag"></i></span>
              </div>
              <%= form.text_field :name, class: 'form-control' %>
            </div>
          </div>
        </div>
      </div>

      
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <%= form.label :種族変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-users"></i></span>
              </div>
              <%= form.select :group, options_for_select([["変更なし", -1]] + (MasterDatum.get_groups_for_select()), card_modifier.group), {selected: card_modifier.group || -1}, {class: 'form-control'} %>
            </div>
          </div>
        </div>
      </div>

      
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <%= form.label :カテゴリー変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-users"></i></span>
              </div>
              <%= form.select :category, options_for_select([["変更なし", ""]] + (MasterDatum.get_categories_for_select()), card_modifier.category), {selected: card_modifier.category || ""}, {class: 'form-control'} %>
            </div>
          </div>
        </div>
      </div>

      <div class="row"> 

        <div class="col-md-3">
          <div class="form-group">
            <%= form.label :レアリティ変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-star"></i></span>
              </div>
              <%= form.select :rarity, {"変更なし": -1, "ブロンズ": 1,"シルバー": 2, "ゴールド": 3, "レジェンド": 4 }, { selected: card_modifier.rarity || -1 }, { class: 'form-control', required: true } %>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-3">
          <div class="form-group">
            <%= form.label :役割変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-chess-knight"></i></span>
              </div>
              <%= form.select :role, {"変更なし": "", "アタッカー": "a","チャージャー": "c", "ディフェンダー": "d" }, 
                           { selected: card_modifier.role || "" }, 
                           { class: 'form-control', required: true } %>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <%= form.label :コスト変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-tint"></i></span>
              </div>
              <%= form.number_field :cost, class: 'form-control' %>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <%= form.label :パワー変更, class: 'form-label' %>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-fist-raised"></i></span>
              </div>
              <%= form.number_field :power, class: 'form-control' %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 特殊能力0 -->

<% (0..2).each do |i| %>

  <div class="card">
    <div class="card-header">
      <h3 class="card-title">特殊能力<%= i %></h3>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-2">
          <div class="form-group">
            <%= form.label "特殊能力#{i}タイミング変更", class: 'form-label' %>
            <%= form.select "use_timing#{i}", {"変更なし": -1,"変更あり": 0,"削除": 1}, { selected: card_modifier.send("use_timing#{i}") }, { class: 'form-control', required: true, id: "timing#{i}_check", onChange: "skill_disable(this)"} %>
          </div>
          <div class="form-group">
            <%= form.label  "特殊能力#{i}タイミング", class: 'form-label' %>
            <%= form.select "timing#{i}", {"召喚時": "Summon", "攻撃成功時": "AttackOK", "防御成功時": "DefenceOK", "手札発動": "Hand", "戦場発動": "Field", "特性": "Any"}, { selected: card_modifier.send("timing#{i}") }, { class: 'form-control', id: "timing#{i}"} %>
          </div>
        </div>
        <div class="col-md-5">
          <div class="form-group">
            <%= form.label "特殊能力#{i}テキスト変更", class: 'form-label' %>
            <%= form.select "use_text#{i}",{"変更なし": -1,"変更あり": 0,"削除": 1}, { selected: card_modifier.send("use_text#{i}") }, { class: 'form-control', required: true, id: "text#{i}_check", onChange: "skill_disable(this)"} %>
          </div>
          <div class="form-group">
            <%= form.label "特殊能力#{i}テキスト", class: 'form-label' %>
            <%= form.text_area "text#{i}", id: "text#{i}", class: 'form-control', rows: 4 %>
          </div>
        </div>
        <div class="col-md-5">
          <div class="form-group">
            <%= form.label "特殊能力#{i}スクリプト変更", class: 'form-label' %>
            <%= form.select "use_script#{i}",{"変更なし": -1,"変更あり": 0,"削除": 1}, { selected: card_modifier.send("use_script#{i}") }, { class: 'form-control', required: true, id: "script#{i}_check", onChange: "skill_disable(this)"} %>
          </div>
          <div class="form-group">
            <%= form.label "特殊能力#{i}スクリプト", class: 'form-label' %>
            <%= form.text_area "script#{i}", id: "script#{i}", class: 'form-control', rows: 4 %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <% end %>

  <div class="text-center mb-4">
    <%= form.submit class: 'btn btn-primary btn-lg' %>
  </div>

  <script>
    function skill_disable(obj) {
      var idx = obj.selectedIndex;
      var value = obj.options[idx].value;
      var id = obj.id;
      document.getElementById(id.replace("_check","")).disabled = value != 0;
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      // ページ読み込み時に各フィールドの初期状態を設定
      for (var i = 0; i < 3; i++) {
        var timingCheck = document.getElementById("timing" + i + "_check");
        var scriptCheck = document.getElementById("script" + i + "_check");
        var textCheck = document.getElementById("text" + i + "_check");
        
        if (timingCheck) {
          var timingValue = timingCheck.options[timingCheck.selectedIndex].value;
          document.getElementById("timing" + i).disabled = timingValue != 0;
        }

        if (scriptCheck) {
          var scriptValue = scriptCheck.options[scriptCheck.selectedIndex].value;
          document.getElementById("script" + i).disabled = scriptValue != 0;
        }
        
        if (textCheck) {
          var textValue = textCheck.options[textCheck.selectedIndex].value;
          document.getElementById("text" + i).disabled = textValue != 0;
        }
      }
    });
  </script>
<% end %>
