<h1>Play Ground</h1> 

<p>ランクテーブル取得</p>
<%= MasterDatum.get_rank_rate_table(10000).inspect %>

<div class="card my-4">
  <div class="card-header">
    <h2>ConstantService.get サンプル</h2>
  </div>
  <div class="card-body">
    <h3>1. 単一値の取得</h3>
    <ul class="list-group mb-4">
      <li class="list-group-item">
        <strong>ショップ > デイリーショップカード販売数:</strong>
        <pre><%= ConstantService.get('shop.daily_shop_card_sales_count') %></pre>
      </li>
      <li class="list-group-item">
        <strong>パック > パックデフォルトレア度確率 > レジェンド:</strong>
        <pre><%= ConstantService.get('pack.pack_default_rare_probabilities.rarity_4') %></pre>
      </li>
      <li class="list-group-item">
        <strong>その他 > 有償名前変更必要ダイヤ:</strong>
        <pre><%= ConstantService.get('other.name_change_cost') %></pre>
      </li>
    </ul>

    <h3>2. オブジェクト全体の取得</h3>
    <ul class="list-group mb-4">
      <li class="list-group-item">
        <strong>ショップ > デイリーショップ確率(無料枠):</strong>
        <pre><%= ConstantService.get('shop.daily_shop_probabilities_first').to_json %></pre>
      </li>
      <li class="list-group-item">
        <strong>ホーム > 宝箱確率:</strong>
        <pre><%= ConstantService.get('home.chest_probabilities').to_json %></pre>
      </li>
      <li class="list-group-item">
        <strong>ショップセクション全体:</strong>
        <pre><%= ConstantService.get('shop').keys.join(', ') %></pre>
      </li>
    </ul>

    <h3>3. 存在しないキーの場合</h3>
    <ul class="list-group mb-4">
      <li class="list-group-item">
        <strong>存在しないトップレベルキー:</strong>
        <pre><%= ConstantService.get('non_existent_key').nil? ? "nilが返されました" : "値が返されました" %></pre>
      </li>
      <li class="list-group-item">
        <strong>存在しないネストキー:</strong>
        <pre><%= ConstantService.get('shop.non_existent_key').nil? ? "nilが返されました" : "値が返されました" %></pre>
      </li>
    </ul>

    <h3>4. シンボル/文字列キー対応</h3>
    <ul class="list-group mb-4">
      <li class="list-group-item">
        <strong>文字列キー:</strong>
        <pre><%= ConstantService.get('shop.daily_shop_dia_costs.bronze1') %></pre>
      </li>
      <li class="list-group-item">
        <strong>シンボルと同等:</strong>
        <pre><%= ConstantService.get(:'shop.daily_shop_dia_costs.bronze1') %></pre>
      </li>
    </ul>
  </div>
</div>

<div class="card my-4">
  <div class="card-header">
    <h2>パフォーマンス比較</h2>
  </div>
  <div class="card-body">
    <h3>1. 速度テスト (100回アクセス)</h3>
    <div class="card bg-light mb-3">
      <div class="card-body">
        <% tests = 100 %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ParameterSetting.settings } %>
        <% ps_settings_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ParameterSetting.get('shop.daily_shop_card_sales_count') } %>
        <% ps_get_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ConstantService.get('shop.daily_shop_card_sales_count') } %>
        <% cs_get_nested_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ConstantService.get } %>
        <% cs_get_all_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ParameterSetting.order(updated_at: :desc).first&.settings } %>
        <% db_direct_time = Time.now.to_f - start_time %>
        
        <p><strong><%= tests %>回アクセス時間比較:</strong></p>
        <ul>
          <li>ParameterSetting.settings: <%= ps_settings_time.round(4) %> 秒</li>
          <li>ParameterSetting.get: <%= ps_get_time.round(4) %> 秒</li>
          <li>ConstantService.get(ネストキー): <%= cs_get_nested_time.round(4) %> 秒</li>
          <li>ConstantService.get(全体): <%= cs_get_all_time.round(4) %> 秒</li>
          <li>DBから直接アクセス: <%= db_direct_time.round(4) %> 秒</li>
        </ul>
      </div>
    </div>
    
    <h3>2. キーのネスト深度による速度比較</h3>
    <div class="card bg-light mb-3">
      <div class="card-body">
        <% tests = 50 %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ConstantService.get('shop') } %>
        <% level1_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ConstantService.get('shop.daily_shop_dia_costs') } %>
        <% level2_time = Time.now.to_f - start_time %>
        
        <% start_time = Time.now.to_f %>
        <% tests.times { ConstantService.get('shop.daily_shop_dia_costs.bronze1') } %>
        <% level3_time = Time.now.to_f - start_time %>
        
        <p><strong>ネスト深度による速度比較 (<%= tests %>回):</strong></p>
        <ul>
          <li>1段階 (shop): <%= level1_time.round(4) %> 秒</li>
          <li>2段階 (shop.daily_shop_dia_costs): <%= level2_time.round(4) %> 秒</li>
          <li>3段階 (shop.daily_shop_dia_costs.bronze1): <%= level3_time.round(4) %> 秒</li>
          <li>3段階/1段階 比率: <%= (level3_time / level1_time).round(2) %> 倍</li>
        </ul>
      </div>
    </div>
  </div>
</div>
