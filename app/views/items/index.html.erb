<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0">アイテム一覧</h1>
      </div>
      <div class="col-sm-6 text-right">
        <%= link_to 'CSVエクスポート', export_items_path(format: :csv, item_type: params[:item_type]), class: 'btn btn-secondary' %>
        <%= link_to 'JSONエクスポート', export_items_path(format: :json, item_type: params[:item_type]), class: 'btn btn-secondary' %>
        <%= link_to '新規作成', new_item_path, class: 'btn btn-primary' %>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">

  <div class="card">
    <div class="card-header">
      <h3 class="card-title">新規作成</h3>
    </div>
    <div class="card-body">
      <%= render 'form'  %>
    </div>
  </div>

    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <%= link_to new_item_path, class: 'btn btn-primary' do %>
            <i class="fas fa-plus"></i> 新規作成
          <% end %>
          
          <div class="ml-3">
            <%= form_tag items_path, method: :get, class: 'd-flex align-items-center' do %>
              <label class="mr-2">種類:</label>
              <% Item.options_for_select.each do |item_type| %>
                <%= link_to item_type[0], items_path(item_type: item_type[1]), class: 'btn btn-secondary mr-2' %>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <div class="card-body">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>ID</th>
              <th>UID</th>
              <th>名前</th>
              <th>説明</th>
              <th>種類</th>
              <th>作成日時</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% @items.each do |item| %>
              <tr>
                <td><%= item.id %></td>
                <td><%= item.uid %></td>
                <td><%= json_lang(item.name) %></td>
                <td><%= json_lang(item.desc) %></td>
                <td><%= item.item_type_name %></td>
                <td><%= item.created_at %></td>
                <td>
                  <%= link_to edit_item_path(item.uid), class: 'btn btn-info btn-sm' do %>
                    <i class="fas fa-edit"></i> 編集
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section> 