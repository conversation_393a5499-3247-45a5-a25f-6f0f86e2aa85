<%= form_with(model: @item, url: @item.persisted? ? item_path(@item.uid) : items_path, local: true) do |f| %>
  <% if @item.errors.any? %>
    <div class="alert alert-danger">
      <ul>
        <% @item.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <%= json_lang_form f, :name, label: "アイテム名" %>
  <%= json_lang_form(f, :desc, label: "説明", as: :text_area) %>

  <div class="form-group">
    <%= f.label :uid, "UID" %>
    <%= f.number_field :uid, class: "form-control" %>
  </div>

  <div class="form-group">
    <%= f.label :item_type, "アイテムタイプ" %>
    <%= f.select :item_type, 
             Item.options_for_select,
             { include_blank: "選択してください" },
             { class: "form-select" } %>
  </div>

  <div class="form-group">
    <%= f.submit '保存', class: 'btn btn-primary' %>
    <%= link_to 'キャンセル', items_path, class: 'btn btn-secondary' %>
  </div>
<% end %> 