<h1>ユーザーのバン</h1>

<% if flash[:error] %>
  <div class="alert alert-danger">
    <%= flash[:error] %>
  </div>
<% end %>

<% if flash[:notice] %>
  <div class="alert alert-success">
    <%= flash[:notice] %>
  </div>
<% end %>

<div class="alert alert-warning">
  <strong>注意:</strong> このアクションによってユーザーはサービスにアクセスできなくなります。
  本当にバンしますか？
</div>

<%= form_with url: user_ban_path, method: :post do |form| %>
  <div class="form-group mb-3">
    <%= form.label :user_id, "ユーザーID" %>
    <%= form.text_field :user_id, class: "form-control" %>
  </div>
  
  <div class="form-group mb-3">
    <%= form.label :ban_end_at, "バン終了日時" %>
    <%= form.datetime_field :ban_end_at, class: "form-control" %>
  </div>
  
  <div class="form-group mb-3">
    <%= form.label :reason, "バン理由" %>
    <%= form.text_field :reason, class: "form-control" %>
  </div>
  
  <div class="form-group">
    <%= form.submit "バン", class: "btn btn-danger", data: { confirm: "本当にこのユーザーをバンしますか？" } %>
  </div>
<% end %>

<h2 class="mt-5">バン履歴</h2>

<% if UserBan.all.any? %>
  <table class="table table-striped mt-3">
    <thead>
      <tr>
        <th>ユーザーID</th>
        <th>ユーザー名</th>
        <th>バン理由</th>
        <th>バン終了日時</th>
        <th>バン設定日時</th>
      </tr>
    </thead>
    <tbody>
      <% UserBan.order(created_at: :desc).each do |ban| %>
        <% user = User.find_by(open_id: ban.open_id) %>
        <tr>
          <td><%= ban.open_id %></td>
          <td><%= user&.name || '(削除済みユーザー)' %></td>
          <td><%= ban.reason %></td>
          <td><%= ban.ban_end_at&.strftime('%Y-%m-%d %H:%M') %></td>
          <td><%= ban.created_at.strftime('%Y-%m-%d %H:%M') %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
<% else %>
  <p>バン履歴はありません。</p>
<% end %>


