<nav class="main-header navbar navbar-expand <%= ENV['HEADER_COLOR'].present? ? ENV['HEADER_COLOR'] : (Rails.env.development? ? "navbar-dark bg-green" : "navbar-white navbar-light") %>">
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#"><i class="fas fa-bars"></i></a>
        </li>
        <% if ENV['APP_NAME'].present? %>
        <li class="nav-item d-none d-sm-inline-block">
            <span class="nav-link d-inline-block font-weight-bold"><%= ENV['APP_NAME'] %></span>
        </li>
        <% else %>
        <li class="nav-item d-none d-sm-inline-block">
            <a href="http://neconome-dev-server.com/" class="nav-link d-inline-block" target="_blank">neconome-dev-server.com</a>
        </li>
        <% end %>
    </ul>
</nav>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css">