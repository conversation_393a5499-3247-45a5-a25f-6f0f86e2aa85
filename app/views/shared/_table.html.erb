<!-- app/views/shared/_table.html.erb -->
<% check = check || nil %>
<div class="card">
  <div class="card-header">
    <h3 class="card-title"><%= model_name %></h3>
    <% if new_path %>
      <div class="card-tools">
        <%= link_to "新規作成", new_path, class: "btn btn-primary btn-sm" %>
      </div>
    <% end %>
  </div>
  <div class="card-body">
    <table class="table table-striped table-bordered text-center">
      <thead>
      <tr>
        <% columns.each do |column| %>
          <th><%= column %></th>
        <% end %>
        <% unless check %>
          <th>操作</th>
        <% end %>
      </tr>
    </thead>
    
    <tbody>
      <% if items.present? %>
        <% items.each do |item| %>
          <tr>
            <% values.each do |value| %>
              <td><%= value.call(item) %></td>
            <% end %>
            <% unless check %>
              <td>
                <% if edit_path %>
                  <%= link_to "編集", edit_path.call(item), class: "btn btn-warning btn-sm" %>
                <% end %>
                <% if delete_path %>
                  <%= link_to "削除", delete_path.call(item), data: { turbo_method: :delete, turbo_confirm: "#{model_name}を本当に削除しますか？" },class: "btn btn-danger btn-sm", form: { style: 'display: inline' }%>
                <% end %>
              </td>
            <% end %>
          </tr>
        <% end %>
      <% else %>
        <tr>
          <td colspan="<%= columns.size + 1 %>" class="text-center"><%= "#{model_name}がありません" %></td>
        </tr>
      <% end %>
    </tbody>
    </table>
  </div>
</div>