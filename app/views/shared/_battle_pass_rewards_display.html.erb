<!-- バトルパス報酬表示（フリーとプレミアムを同時に表示） -->
<div class="battle-pass-rewards-display">
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h3 class="card-title mb-0">バトルパス報酬一覧</h3>
    </div>
    <div class="card-body" x-data="{ 
      freeRewards: window.freeRewardsStore?.rewards || [],
      premiumRewards: window.premiumRewardsStore?.rewards || [],
      
      itemTypes: <%= Item.all_options_for_select.to_json %>,
      
      init() {
        // ウィンドウストアの監視
        if (window.freeRewardsStore) {
          this.$watch('window.freeRewardsStore.rewards', (value) => {
            this.freeRewards = Array.isArray(value) ? [...value] : [];
          });
        }
        
        if (window.premiumRewardsStore) {
          this.$watch('window.premiumRewardsStore.rewards', (value) => {
            this.premiumRewards = Array.isArray(value) ? [...value] : [];
          });
        }
        
        // ストア更新のためのカスタムイベントリスナー
        window.addEventListener('rewards-updated', () => {
          this.freeRewards = window.freeRewardsStore?.rewards || [];
          this.premiumRewards = window.premiumRewardsStore?.rewards || [];
        });
      },
      
      getItemTypeName(item_type) {
        const type = this.itemTypes.find(t => t[1] === item_type);
        return type ? type[0] : item_type;
      },
      
      getItemIcon(item_type) {
        const icons = {
          'icon': '<%= Item::ITEM_ICONS['icon'] %>',
          'icon_frame': '<%= Item::ITEM_ICONS['icon_frame'] %>',
          'card_sleeve': '<%= Item::ITEM_ICONS['card_sleeve'] %>',
          'playmat': '<%= Item::ITEM_ICONS['playmat'] %>',
          'bgm': '<%= Item::ITEM_ICONS['bgm'] %>',
          'title': '<%= Item::ITEM_ICONS['title'] %>',
          'chest': '<%= Item::ITEM_ICONS['chest'] %>',
          'pack_ticket': '<%= Item::ITEM_ICONS['pack_ticket'] %>',
          'prebuilt_deck': '<%= Item::ITEM_ICONS['prebuilt_deck'] %>',
          'none': '<%= Item::ITEM_ICONS['none'] %>',
          'dia': '<%= Item::ITEM_ICONS['dia'] %>'
        };
        return icons[item_type] || '<%= Item::ITEM_ICONS['dia'] %>';
      },
      
      getItemName(type, id) {
        if (type === 'dia' || type === 'none') return '';
        
        const options = {
          'icon': <%= Item.md_options_for_select('icon').to_json %>,
          'icon_frame': <%= Item.md_options_for_select('icon_frame').to_json %>,
          'card_sleeve': <%= Item.md_options_for_select('card_sleeve').to_json %>,
          'playmat': <%= Item.md_options_for_select('playmat').to_json %>,
          'bgm': <%= Item.md_options_for_select('bgm').to_json %>,
          'title': <%= Item.md_options_for_select('title').to_json %>,
          'pack_ticket': <%= Pack.all.map { |pack| ["#{pack.name} (#{pack.id})", pack.id] }.to_json %>
        };
        
        if (!options[type]) return '';
        
        const item = options[type].find(opt => parseInt(opt[1]) === parseInt(id));
        return item ? item[0] : '';
      },
      
      // 個数を表示するアイテムの種類
      shouldShowCount(item_type) {
        return ['dia', 'pack_ticket'].includes(item_type);
      }
    }" x-init="$nextTick(() => { init() })">
      <div x-show="freeRewards.length === 0 && premiumRewards.length === 0" class="text-center py-3 text-muted">
        <i class="fas fa-gift fa-2x mb-2"></i>
        <p>報酬が設定されていません</p>
      </div>
      
      <div x-show="freeRewards.length > 0 || premiumRewards.length > 0">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead class="thead-light">
              <tr>
                <th style="width: 10%">レベル</th>
                <th style="width: 45%" class="text-center">
                  <span class="badge badge-primary">無料報酬</span>
                </th>
                <th style="width: 45%" class="text-center">
                  <span class="badge badge-success">プレミアム報酬</span>
                </th>
              </tr>
            </thead>
            <tbody>
              <template x-for="level in Math.max(freeRewards.length, premiumRewards.length)" :key="level">
                <tr>
                  <td class="text-center font-weight-bold" x-text="level"></td>
                  <!-- 無料報酬 -->
                  <td>
                    <template x-if="freeRewards[level-1]">
                      <div>
                        <i :class="getItemIcon(freeRewards[level-1].item_type)"></i>
                        <span x-text="getItemTypeName(freeRewards[level-1].item_type)"></span>
                        <template x-if="freeRewards[level-1].item_type !== 'dia' && freeRewards[level-1].item_type !== 'none'">
                          <span x-text="': ' + getItemName(freeRewards[level-1].item_type, freeRewards[level-1].item_id)"></span>
                        </template>
                        <template x-if="shouldShowCount(freeRewards[level-1].item_type) && freeRewards[level-1].count">
                          <span x-text="' (' + freeRewards[level-1].count + ')'"></span>
                        </template>
                      </div>
                    </template>
                    <template x-if="!freeRewards[level-1]">
                      <div class="text-muted">-</div>
                    </template>
                  </td>
                  <!-- プレミアム報酬 -->
                  <td>
                    <template x-if="premiumRewards[level-1]">
                      <div>
                        <i :class="getItemIcon(premiumRewards[level-1].item_type)"></i>
                        <span x-text="getItemTypeName(premiumRewards[level-1].item_type)"></span>
                        <template x-if="premiumRewards[level-1].item_type !== 'dia' && premiumRewards[level-1].item_type !== 'none'">
                          <span x-text="': ' + getItemName(premiumRewards[level-1].item_type, premiumRewards[level-1].item_id)"></span>
                        </template>
                        <template x-if="shouldShowCount(premiumRewards[level-1].item_type) && premiumRewards[level-1].count">
                          <span x-text="' (' + premiumRewards[level-1].count + ')'"></span>
                        </template>
                      </div>
                    </template>
                    <template x-if="!premiumRewards[level-1]">
                      <div class="text-muted">-</div>
                    </template>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div> 