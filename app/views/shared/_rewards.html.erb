<div x-data="{ 
  itemTypes2: <%= Item.all_options_for_select.to_json %>, 
  rewards: <%= rewards.present? ? rewards.to_json : '[]' %>,
  storeRef: '<%= local_assigns[:store_name] || "" %>',
  showCountItem: ['dia', 'pack_ticket', 'card_sand', 'main_pack_ticket', 'special_pack', 'chest'],

  iconOptions: <%= Item.md_options_for_select('icon').to_json %>,
  iconFrameOptions: <%= Item.md_options_for_select('icon_frame').to_json %>,
  cardSleeveOptions: <%= Item.md_options_for_select('card_sleeve').to_json %>,
  playmatOptions: <%= Item.md_options_for_select('playmat').to_json %>,
  bgmOptions: <%= Item.md_options_for_select('bgm').to_json %>,
  titleOptions: <%= Item.md_options_for_select('title').to_json %>,

  chestOptions: <%= Item.chest_options.to_json %>,

  itemRules: <%= Item.get_all_item_rules.to_json %>,
  cardSandOptions: <%= Item.rarity_options.to_json %>,
  cardOptions: <%= Item.md_options_cards_for_select().to_json %>,

  // 動的生成
  packTicketOptions: <%= Pack.all.map { |pack| ["#{pack.name["ja"]} (#{pack.uid})", pack.uid] }.reject { |_, uid| uid == 0 }.to_json %>,

  newReward: { item_type: 'dia', count: 100, item_id: 0, ext: { } },
  draggedIndex: null,
  
  // コンポーネント初期化時にストアを設定
  init() {
    if (this.storeRef && window[this.storeRef]) {
      // 初期値をストアに設定
      window[this.storeRef].rewards = JSON.parse(JSON.stringify(this.rewards));
      
      // 数値型の項目を整数に変換
      this.rewards.forEach(reward => {
        reward.item_id = parseInt(reward.item_id, 10) || 0;
        reward.count = parseInt(reward.count, 10) || 0;
        if (reward.ext && reward.ext.min !== undefined) {
          reward.ext.min = parseInt(reward.ext.min, 10) || 0;
        }
        if (reward.ext && reward.ext.max !== undefined) {
          reward.ext.max = parseInt(reward.ext.max, 10) || 0;
        }
      });
      
      // 監視して変更があればストアに反映
      this.$watch('rewards', (value) => {
        if (window[this.storeRef]) {
          window[this.storeRef].rewards = JSON.parse(JSON.stringify(value));
          // カスタムイベントを発火して他のコンポーネントに通知
          window.dispatchEvent(new CustomEvent('rewards-updated'));
        }
      });
    }
  },
  
  // ドラッグ開始時の処理
  dragStart(index) {
    this.draggedIndex = index;
  },
  
  // ドラッグ中に他の要素の上に来た時の処理
  dragOver(event) {
    event.preventDefault();
  },
  
  // ドロップ時の処理
  drop(index) {
    if (this.draggedIndex === null) return;
    
    // 要素の入れ替え
    const item = this.rewards[this.draggedIndex];
    this.rewards.splice(this.draggedIndex, 1);
    this.rewards.splice(index, 0, item);
    
    this.draggedIndex = null;
    
    // 報酬更新イベントを発火
    window.dispatchEvent(new CustomEvent('rewards-updated'));
  },
  
  // データをコンソールに表示する関数
  logRewardsData() {
    console.log(JSON.stringify(this.rewards, null, 2));
  },

  // アイテムタイプが変更されたときに実行される関数
  updateItemId(reward) {
    reward.ext = {};
    // 確率
    <% if local_assigns[:is_random] || false %>
      reward.ext['rate'] = 100;
    <% end %>

    // アイテムタイプに基づいて適切なオプションリストを選択
    let options = [];
    switch(reward.item_type) {
      case 'icon':
        options = this.iconOptions;
        reward.count = 1;
        break;
      case 'icon_frame':
        options = this.iconFrameOptions;
        reward.count = 1;
        break;
      case 'card_sleeve':
        options = this.cardSleeveOptions;
        reward.count = 1;
        break;
      case 'playmat':
        options = this.playmatOptions;
        reward.count = 1;
        break;
      case 'bgm':
        options = this.bgmOptions;
        reward.count = 1;
        break;
      case 'title':
        options = this.titleOptions;
        reward.count = 1;
        break;
      case 'chest':
        options = this.chestOptions;
        reward.count = 1;
        break;
      case 'pack_ticket':
        options = this.packTicketOptions;
        reward.count = 1;
        break;

      case 'dia':
        <% if local_assigns[:is_random] || false %>
          reward.count = 0;
          Object.assign(reward.ext, { use_range: false, min: 0, max: 0 });
        <% else %>
          reward.count = 0;
        <% end %>
        break;
      case 'none':
        reward.count = 0;
        break;
      case 'main_pack_ticket':
        reward.count = 1;
        break;
      case 'special_pack':
        reward.count = 1;
        break;
      case 'card_sand':
        options = this.cardSandOptions;
        reward.count = 10;
        break;
      case 'nr_card':
      case 'sh_card':
      case 'pr_card':
        options = this.cardOptions;
        reward.count = 1;
        break;
      case 'pack_point':
        options = this.packTicketOptions;
        reward.count = 1;
        break;
      default:
        reward.item_id = 0;
        break;
    }
    
    // 選択肢がある場合は最初の選択肢を設定
    if (options.length > 0) {
      reward.item_id = parseInt(options[0][1], 10) || 0;
    } else {
      reward.item_id = 0;
    }
    
    // 報酬更新イベントを発火
    window.dispatchEvent(new CustomEvent('rewards-updated'));
  },

  // アイテムタイプの名称を取得する関数
  getItemTypeName(item_type) {
    const type = this.itemTypes2.find(type => type[1] === item_type);
    return type ? type[0] : item_type;
  },

  // アイテムアイコンを取得する関数
  getItemIcon(item_type) {
    const iconMap = {
      'icon': 'fas fa-user-circle',
      'icon_frame': 'far fa-circle',
      'card_sleeve': 'fas fa-window-restore',
      'playmat': 'fas fa-chess-board',
      'bgm': 'fas fa-compact-disc',
      'title': 'fas fa-crown',
      'chest': 'fas fa-box',
      'pack_ticket': 'fas fa-ticket-alt',
      'prebuilt_deck': 'fas fa-layer-group',
      'none': 'fas fa-ban',
      'dia': 'fas fa-gem'
    };
    return iconMap[item_type] || 'fas fa-question';
  },

  // アイテム名を取得する関数
  getItemName(item_type, item_id) {
    let options = [];
    switch(item_type) {
      case 'icon':
        options = this.iconOptions;
        break;
      case 'icon_frame':
        options = this.iconFrameOptions;
        break;
      case 'card_sleeve':
        options = this.cardSleeveOptions;
        break;
      case 'playmat':
        options = this.playmatOptions;
        break;
      case 'bgm':
        options = this.bgmOptions;
        break;
      case 'title':
        options = this.titleOptions;
        break;
      case 'pack_ticket':
        options = this.packTicketOptions;
        break;
      default:
        return '';
    }
    
    const option = options.find(opt => opt[1] === item_id);
    return option ? option[0] : '';
  },

  // アイテムタイプが個数を表示するかどうかを判定する関数
  shouldShowCount(item_type) {
    const rule = this.itemRules[item_type];
    return rule && rule.count === true;
  },

  // アイテムタイプが範囲指定をサポートするかどうかを判定する関数
  supportsRange(item_type) {
    const rule = this.itemRules[item_type];
    return rule && rule.range === true;
  }
}">

  <!-- 報酬管理 -->
  <div class="card">
    <div class="card-header">
    
      <h3 class="card-title">報酬管理</h3>
      <div class="card-tools">
      <!-- データ表示ボタンを追加 -->
      <button class="btn btn-secondary btn-sm" type="button" @click="logRewardsData()">
        <i class="fas fa-bug"></i> ログ出力(Debug)
      </button>
        <button class="btn btn-primary btn-sm ml-2" type="button" @click="rewards.push({...newReward}); window.dispatchEvent(new CustomEvent('rewards-updated'));">
          <i class="fas fa-plus"></i> 報酬を追加
        </button>
      </div>
    </div>
    
    <!-- 報酬リスト -->
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th style="width: 10px"></th>
              <th style="width: 10px">#</th>
              <th style="width: 100px">報酬内容</th>
              <th style="width: 50px">確率(%)</th>
              <th style="width: 50px">操作</th>
            </tr>
          </thead>
          <tbody>
            <template x-for="(reward, index) in rewards" :key="index">
              <tr 
                :class="{'bg-light': draggedIndex === index}"
                @dragover.prevent
                @drop="drop(index)"
              >
                <td class="px-2">
                  <div class="drag-handle" draggable="true" @dragstart="dragStart(index)">
                    <i class="fas fa-grip-vertical cursor-move"></i>
                  </div>
                </td>
                <td x-text="index + 1"></td>
                <td>
                  <div class="d-flex align-items-center flex-wrap">

                    <!-- アイテムタイプ表示 -->
                    <div class="form-group mr-3 mb-0">
                      <select class="form-control form-control-sm" x-model="reward.item_type" @change="updateItemId(reward)">
                        <template x-for="type in itemTypes2" :key="type[1]">
                          <option :value="type[1]" x-text="type[0]" x-bind:selected="type[1] == reward.item_type"></option>
                        </template>
                      </select>
                    </div>

                    <template x-if="reward.item_type === 'icon'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="icon in iconOptions" :key="icon[1]">
                            <option :value="icon[1]" x-text="icon[0]" x-bind:selected="icon[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'icon_frame'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="icon_frame in iconFrameOptions" :key="icon_frame[1]">
                            <option :value="icon_frame[1]" x-text="icon_frame[0]" x-bind:selected="icon_frame[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'card_sleeve'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="card_sleeve in cardSleeveOptions" :key="card_sleeve[1]">
                            <option :value="card_sleeve[1]" x-text="card_sleeve[0]" x-bind:selected="card_sleeve[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'playmat'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="playmat in playmatOptions" :key="playmat[1]">
                            <option :value="playmat[1]" x-text="playmat[0]" x-bind:selected="playmat[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'bgm'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="bgm in bgmOptions" :key="bgm[1]">
                            <option :value="bgm[1]" x-text="bgm[0]" x-bind:selected="bgm[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'title'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="title in titleOptions" :key="title[1]">
                            <option :value="title[1]" x-text="title[0]" x-bind:selected="title[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'chest'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="chest in chestOptions" :key="chest[1]">
                            <option :value="chest[1]" x-text="chest[0]" x-bind:selected="chest[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'nr_card' || reward.item_type === 'sh_card' || reward.item_type === 'pr_card'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="card in cardOptions" :key="card[1]">
                            <option :value="card[1]" x-text="card[0]" x-bind:selected="card[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>

                    <template x-if="reward.item_type === 'pack_point'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="pack_point in packTicketOptions" :key="pack_point[1]">
                            <option :value="pack_point[1]" x-text="pack_point[0]" x-bind:selected="pack_point[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>
                    

                    <!-- 動的生成系 -->

                    
                    <template x-if="reward.item_type === 'pack_ticket'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="pack_ticket in packTicketOptions" :key="pack_ticket[1]">
                            <option :value="pack_ticket[1]" x-text="pack_ticket[0]" x-bind:selected="pack_ticket[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>


                    <template x-if="reward.item_type === 'card_sand'">
                      <div class="form-group mr-3 mb-0">
                        <select class="form-control form-control-sm" x-model.number="reward.item_id" @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          <template x-for="card_sand in cardSandOptions" :key="card_sand[1]">
                            <option :value="card_sand[1]" x-text="card_sand[0]" x-bind:selected="card_sand[1] == reward.item_id"></option>
                          </template>
                        </select>
                      </div>
                    </template>


                    <!-- 個数表示 -->
                    <template x-if="shouldShowCount(reward.item_type)">
                      <div class="form-group mb-0 d-flex align-items-center">
                        <template x-if="!(reward.item_type === 'dia' && reward.ext.use_range)">
                          <div class="d-flex align-items-center">
                            <label class="mr-2 mb-0">個数</label>
                            <input type="number" class="form-control form-control-sm" style="width: 80px" 
                              x-model.number="reward.count" min="0" 
                              @change="window.dispatchEvent(new CustomEvent('rewards-updated'))">
                          </div>
                        </template>
                        
                        <!-- 範囲指定サポート -->
                        <template x-if="supportsRange(reward.item_type) && <%= local_assigns[:is_random] || false %>">
                          <div class="ml-3 d-flex align-items-center">
                            <div class="custom-control custom-checkbox mr-2">
                              <input type="checkbox" class="custom-control-input" :id="'range_check_' + index" x-model="reward.ext.use_range"
                              @change="reward.ext.min = 0; reward.ext.max = 0; window.dispatchEvent(new CustomEvent('rewards-updated'))">
                              <label class="custom-control-label" :for="'range_check_' + index">範囲指定</label>
                            </div>
                            
                            <template x-if="reward.ext.use_range">
                              <div class="d-flex align-items-center">
                                <label class="mr-2 mb-0">最小</label>
                                <input type="number" class="form-control form-control-sm mr-2" style="width: 80px" x-model.number="reward.ext.min" min="1">
                                <label class="mr-2 mb-0">最大</label>
                                <input type="number" class="form-control form-control-sm" style="width: 80px" x-model.number="reward.ext.max" min="1">
                              </div>
                            </template>
                          </div>
                        </template>
                      </div>
                    </template>
                  </div>
                </td>
                <td>
                  <template x-if="<%= local_assigns[:is_random] || false %>">
                    <div class="form-group mb-0">
                      <input type="number" step="0.001" class="form-control form-control-sm"  x-model.number="reward.ext.rate" min="0" max="100">
                    </div>
                  </template>
                </td>
                <td>
                  <div class="btn-group">
                    <button type="button" class="btn btn-danger btn-sm" @click="rewards.splice(index, 1); window.dispatchEvent(new CustomEvent('rewards-updated'));">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
      
      <div x-show="rewards.length === 0" class="text-center py-4 text-muted">
        <i class="fas fa-gift fa-2x mb-2"></i>
        <p>報酬が設定されていません。「報酬を追加」ボタンをクリックして報酬を設定してください。</p>
      </div>
      
      <!-- 下部に報酬追加ボタンを追加 -->
      <div class="text-center mt-3">
        <button class="btn btn-primary" type="button" @click="newReward.ext = {}; rewards.push({...newReward}); updateItemId(rewards[rewards.length - 1]); window.dispatchEvent(new CustomEvent('rewards-updated'));">
          <i class="fas fa-plus"></i> 報酬を追加
        </button>
      </div>
    </div>
    
  </div>

  <!-- フォーム送信時にrewardsデータを含める -->
  <input type="hidden" name="<%= param_name %>" :value=JSON.stringify(rewards)>

  <style>
    .cursor-move {
      cursor: move;
    }
    .table td {
      vertical-align: middle;
    }
    .drag-handle {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #aaa;
    }
    .drag-handle:hover {
      color: #666;
    }
  </style>
</div>
