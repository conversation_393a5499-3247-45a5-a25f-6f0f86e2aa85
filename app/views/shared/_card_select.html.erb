<div x-data="{ 
  allCards: <%= MasterDatum.get_cards.to_json %>,
  sortedCards: [],
  selectedCards: {},  // 選択されたカードとその枚数を保持
  sortBy: 'Cost',
  nameSearchQuery: '',
  effectSearchQuery: '',
  showSelectedCardsLog: false, // ログ表示の状態を管理
  showEffects: false, // 効果表示の状態を管理
  canPremium: false, // プレミアムカードの表示状態を管理する
  columns: {
    id: true,
    name: true,
    group: true,
    rarity: true,
    role: true,
    cost: true,
    power: true,
    effect: false
  },
  init() {
    this.sortCards();
    // ローカルストレージから設定を読み込む
    const savedColumns = localStorage.getItem('cardColumnsSettings');
    if (savedColumns) {
      this.columns = JSON.parse(savedColumns);
    }
    
    // columnsの変更を監視して保存
    this.$watch('columns', (value) => {
      localStorage.setItem('cardColumnsSettings', JSON.stringify(value));
    }, { deep: true });
  },
  getTimingText(timing) {
                          const timingMap = {
                            'Summon': '召喚時',
                            'AttackOK': '攻撃成功時',
                            'DefenceOK': '防御成功時',
                            'Hand': '手札発動',
                            'Field': '戦場発動',
                            'Any': '特性'
                          };
                          return timingMap[timing] || timing;
                        },
  getGroupText(group) {
    const groupMap = {
      '1': 'ドラゴン',
      '2': 'アンドロイド',
      '3': 'エレメンタル',
      '4': 'ルミナス',
      '5': 'シェイド',
      '8': 'イノセント',
      '9': '旧神'
    };
    return groupMap[group] || group;
  },
  getRoleText(role) {
    const roleMap = {
      'a': 'アタッカー',
      'c': 'チャージャー',
      'd': 'ディフェンダー'
    };
    return roleMap[role] || role;
  },
  getRarityText(rarity) {
    const rarityMap = {
      '1': 'ノーマル',
      '2': 'レア',
      '3': 'エピック',
      '4': 'レジェンド'
    };
    return rarityMap[rarity] || rarity;
  },
  sortCards() {
    let filtered = this.allCards;
    
    // カード名での検索
    if (this.nameSearchQuery.trim() !== '') {
      const nameQuery = this.nameSearchQuery.toLowerCase();
      filtered = filtered.filter(card => 
        card.Name.toLowerCase().includes(nameQuery)
      );
    }
    
    // 効果での検索
    if (this.effectSearchQuery.trim() !== '') {
      const effectQuery = this.effectSearchQuery.toLowerCase();
      filtered = filtered.filter(card => 
        card.Text0.toLowerCase().includes(effectQuery)
      );
    }
    
    if (this.sortBy === 'ID') {
      this.sortedCards = [...filtered].sort((a, b) => a.ID - b.ID);
    } else if (this.sortBy === 'Cost') {
      this.sortedCards = [...filtered].sort((a, b) => {
        // まずRoleでソート
        if (a.Role !== b.Role) {
          return a.Role.localeCompare(b.Role);
        }
        // 次にCostでソート
        if (a.Cost !== b.Cost) {
          return a.Cost - b.Cost;
        }
        // 最後にPowerでソート
        return a.Power - b.Power;
      });
    }
  }
}">
<div class="modal fade" id="model_modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 95vw; width: 95%;">
        <div class="modal-content">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">カード一覧</h3>
              
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between mb-3">
                <div class="btn-group">
                  <button @click="sortBy = 'ID'; sortCards()" :class="{ 'active': sortBy === 'ID' }" class="btn btn-sm btn-default">ID順</button>
                  <button @click="sortBy = 'Cost'; sortCards()" :class="{ 'active': sortBy === 'Cost' }" class="btn btn-sm btn-default">コスト順</button>
                </div>
                
                <div class="d-flex">
                  <div class="input-group input-group-sm mr-2" style="width: 250px;">
                    <input 
                      type="text" 
                      x-model="nameSearchQuery" 
                      @input="sortCards()" 
                      placeholder="カード名で検索..." 
                      class="form-control"
                    >
                    <div class="input-group-append">
                      <button type="button" class="btn btn-default">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>
                  
                  <div class="input-group input-group-sm" style="width: 250px;">
                    <input 
                      type="text" 
                      x-model="effectSearchQuery" 
                      @input="sortCards()" 
                      placeholder="効果で検索..." 
                      class="form-control"
                    >
                    <div class="input-group-append">
                      <button type="button" class="btn btn-default">
                        <i class="fas fa-search"></i>
                      </button>
                    </div>
                  </div>

                  
                  <!-- チェックボックスグループ -->
                  <div class="d-flex ml-3">
                    <div class="dropdown mr-2">
                      <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="columnSettingsDropdown" data-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-columns"></i> カラム設定
                      </button>
                      <div class="dropdown-menu p-2" aria-labelledby="columnSettingsDropdown">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnId" x-model="columns.id">
                          <label class="form-check-label" for="columnId">ID</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnName" x-model="columns.name">
                          <label class="form-check-label" for="columnName">名前</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnGroup" x-model="columns.group">
                          <label class="form-check-label" for="columnGroup">種族</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnRarity" x-model="columns.rarity">
                          <label class="form-check-label" for="columnRarity">レアリティ</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnRole" x-model="columns.role">
                          <label class="form-check-label" for="columnRole">役割</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnCost" x-model="columns.cost">
                          <label class="form-check-label" for="columnCost">コスト</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnPower" x-model="columns.power">
                          <label class="form-check-label" for="columnPower">パワー</label>
                        </div>
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="columnEffect" x-model="columns.effect">
                          <label class="form-check-label" for="columnEffect">効果</label>
                        </div>
                      </div>
                    </div>
                    
                    <div class="form-check form-switch mr-3">
                      <input class="form-check-input" type="checkbox" id="showSelectedCardsLogSwitch" x-model="showSelectedCardsLog">
                      <label class="form-check-label" for="showSelectedCardsLogSwitch">
                        <i class="fas fa-list"></i> 選択したカードを表示
                      </label>
                    </div>
                  </div>
                </div>
                
              </div>
              
              <!-- メインコンテンツエリア -->
              <div class="d-flex">
                <!-- テーブルエリア -->
                <div :class="showSelectedCardsLog ? 'col-md-9 pr-2' : 'col-12'">
                  <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                      <thead>
                        <tr>
                          <th x-show="columns.id">ID</th>
                          <th x-show="columns.name">名前</th>
                          <th x-show="columns.group">種族</th>
                          <th x-show="columns.rarity">レアリティ</th>
                          <th x-show="columns.role">役割</th>
                          <th x-show="columns.cost">コスト</th>
                          <th x-show="columns.power">パワー</th>
                          <th x-show="columns.effect">効果</th>
                          <th>枚数</th>
                        </tr>
                      </thead>
                      <tbody>
                        <template x-for="card in sortedCards" :key="card.ID">
                          <tr>
                            <td x-show="columns.id" x-text="card.ID"></td>
                            <td x-show="columns.name" x-text="card.Name"></td>
                            <td x-show="columns.group" x-text="getGroupText(card.Group)"></td>
                            <td x-show="columns.rarity" x-text="getRarityText(card.Rarity)"></td>
                            <td x-show="columns.role" x-text="getRoleText(card.Role)"></td>
                            <td x-show="columns.cost" x-text="card.Cost"></td>
                            <td x-show="columns.power" x-text="card.Power"></td>
                            <td x-show="columns.effect">
                              <div x-data="{
                                getTimingText(timing) {
                                  const timingMap = {
                                    'Summon': '召喚時',
                                    'AttackOK': '攻撃成功時',
                                    'DefenceOK': '防御成功時',
                                    'Hand': '手札発動',
                                    'Field': '戦場発動',
                                    'Any': '特性'
                                  };
                                  return timingMap[timing] || timing;
                                }
                              }">
                                <div x-show="card.Timing0 && card.Text0">
                                  <span x-text="getTimingText(card.Timing0) + ': ' + card.Text0"></span>
                                </div>
                                <div x-show="card.Timing1 && card.Text1">
                                  <span x-text="getTimingText(card.Timing1) + ': ' + card.Text1"></span>
                                </div>
                                <div x-show="card.Timing2 && card.Text2">
                                  <span x-text="getTimingText(card.Timing2) + ': ' + card.Text2"></span>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="d-flex align-items-center">
                                <button 
                                  @click="selectedCards[card.ID] = (selectedCards[card.ID] || 0) - 1; if(selectedCards[card.ID] < 0) selectedCards[card.ID] = 0" 
                                  class="btn btn-sm btn-outline-secondary mr-2">
                                  <i class="fas fa-minus"></i>
                                </button>
                                <span x-text="selectedCards[card.ID] || 0"></span>
                                <button 
                                  @click="selectedCards[card.ID] = (selectedCards[card.ID] || 0) + 1" 
                                  class="btn btn-sm btn-outline-secondary ml-2">
                                  <i class="fas fa-plus"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        </template>
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <!-- 選択カードログ表示エリア（右側） -->
                <div x-show="showSelectedCardsLog" class="col-md-3 pl-2">
                  <div class="p-3 border rounded bg-light h-100">
                    <h5>選択されたカード</h5>
                    <div x-show="Object.keys(selectedCards).filter(id => selectedCards[id] > 0).length === 0" class="text-muted">
                      カードが選択されていません
                    </div>
                    <ul class="list-group">
                      <template x-for="id in Object.keys(selectedCards).filter(id => selectedCards[id] > 0)" :key="id">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                          <span>
                            <span x-text="allCards.find(card => card.ID == id)?.Name || 'Unknown'"></span>
                            <small class="text-muted ml-1" x-text="'(ID:' + id + ')'"></small>
                          </span>
                          
                          <div class="d-flex align-items-center">
                            <button 
                              @click="selectedCards[id] = selectedCards[id] - 1; if(selectedCards[id] <= 0) delete selectedCards[id]" 
                              class="btn btn-sm btn-outline-secondary mr-2">
                              <i class="fas fa-minus"></i>
                            </button>
                            <span x-text="selectedCards[id]"></span>枚
                            <button 
                              @click="selectedCards[id] = selectedCards[id] + 1" 
                              class="btn btn-sm btn-outline-secondary ml-2">
                              <i class="fas fa-plus"></i>
                            </button>
                          </div>
                        </li>
                      </template>
                    </ul>
                    <div class="mt-2">
                      <small class="text-muted">選択カード合計: <span x-text="Object.values(selectedCards).reduce((sum, count) => sum + count, 0)"></span>枚</small>
                    </div>
                    <div class="mt-2">
                      <small class="text-muted">デバッグ情報: <span x-text="JSON.stringify(selectedCards, null, 2)"></span></small>
                    </div>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
          
        </div>
    </div>
</div>
</div>


