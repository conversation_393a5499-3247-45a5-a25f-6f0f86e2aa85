<div x-data="{ showCustomDates: false }" x-init="showCustomDates = $refs.periodSelect.value == -1">
   <div class="card p-3 mb-4">
    <div class="card-header">
      <h5>期間設定</h5>
    </div>
    <div class="card-body">
      <div class="form-group mb-3">
        <%= form.label :period_id, '期間設定', class: 'form-label' %>
    <%= form.select :period_id, [['自由指定', -1]] + Period.all.collect { |p| [p.name + " (" + p.start_at.strftime("%Y/%m/%d") + "~" + p.end_at.strftime("%Y/%m/%d") + ")", p.uid] }, {}, { class: 'form-control', 'x-ref': 'periodSelect', 'x-on:change': 'console.log($event.target.value); showCustomDates = $event.target.value == -1' } %>
  </div>

  <div id="info-date-fields" x-show="showCustomDates" x-cloak>
    <div class="form-group mb-3">
      <%= form.label :start_at, '開始日時', class: 'form-label' %>
      <%= form.datetime_field :start_at, class: 'form-control' %>
    </div>

    <div class="form-group mb-3">
      <%= form.label :end_at, '終了日時', class: 'form-label' %>
        <%= form.datetime_field :end_at, class: 'form-control' %>
      </div>
    </div>
  </div>
</div>