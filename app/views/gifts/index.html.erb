<!-- app/views/gifts/index.html.erb -->
<h1>ギフト一覧</h1>
<p style="color: green"><%= notice %></p>

<% if @gifts.present? %>
  <% @gifts.each do |gift| %>
    <% gift_master = GiftMaster.find_by(uid: gift.master_uid) %>
    <div style="margin-bottom: 10px; border: 1px solid #ddd; padding: 10px;">
      <% if gift_master&.user_id.present? %>
        <strong style="color: blue;">[個人ギフト]</strong>
      <% else %>
        <strong style="color: green;">[全体ギフト]</strong>
      <% end %>
      <p>ギフトID: <%= gift.uid %></p>
      <p>アイテム種別: <%= gift.item_type %></p>
      <p>個数: <%= gift.item_count %></p>
      <p>value: <%= gift.value %></p>
      <p>説明: <%= gift.desc %></p>
      <p>期限: <%= gift.end_at %></p>

      <% if gift.state == 0 %>
        <!-- 未受取なら受け取りボタンを表示 -->
        <%= form_with url: gifts_open_path(gift.user_id), method: :post, local: true do |f| %>
          <%= hidden_field_tag :gift_uid, gift.uid %>
          <%= submit_tag 'ギフトを受け取る', class: 'btn btn-primary' %>
        <% end %>
      <% else %>
        <!-- 受取済み -->
        <span style="color: gray;">受け取り済み</span>
      <% end %>
    </div>
  <% end %>
<% else %>
  <p>ギフトはありません</p>
<% end %>
