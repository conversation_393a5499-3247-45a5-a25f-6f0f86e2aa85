<h1>データのインポート</h1>

<%= form_tag import_data_server_versions_path, multipart: true do %>
  <div class="form-group">
    <%= label_tag :file, "JSONファイルを選択" %>
    <%= file_field_tag :file, class: "form-control" %>
  </div>

  <div class="form-group">
    <h4>インポートする項目を選択</h4>
    <% ServerVersionsController::EXPORTABLE_ITEMS.each do |key, _| %>
      <div class="checkbox">
        <label>
          <%= check_box_tag "selected_items[]", key, true %>
          <%= t("activerecord.models.#{key.to_s.singularize}") %>
        </label>
      </div>
    <% end %>
  </div>

  <%= submit_tag "インポート", class: "btn btn-primary" %>
<% end %> 