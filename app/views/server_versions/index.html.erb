<% content_for :page_title, "バージョン管理" %>

<div class="row">
    <div class="col-md-6">
        <%= render 'diff_data', hash: @hash , title: "ベースバージョンとの差分" %>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-download"></i> インポート
                </h3>
            </div>
            <div class="card-body">
                <%= form_tag import_data_server_versions_path, method: :post, multipart: true, html: { class: "form-inline" } do %>
                    <div class="input-group mb-3">
                        <%= file_field_tag :file, accept: 'application/json', class: "form-control" %>
                        <div class="input-group-append">
                            <%= submit_tag "読み込み", class: "btn btn-danger" %>
                        </div>
                    </div>
                <% end %>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-save"></i> データの保存
                </h3>
            </div>
            <div class="card-body">
                <%= form_tag save_data_server_versions_path, method: :post, html: { class: "form-inline" } do %>
                    <div class="form-group">
                        <%= label_tag :title, "タイトル" %>
                        <%= text_field_tag :title, nil, class: "form-control", placeholder: "タイトルを入力してください", required: true %>
                    </div>
                    <div class="form-group">
                        <%= label_tag :description, "説明" %>
                        <%= text_area_tag :desc, nil, class: "form-control", placeholder: "説明を入力してください" %>
                    </div>
                    <div class="form-group">
                        <h4>エクスポートする項目を選択</h4>
                        <% ServerVersionsController::EXPORTABLE_ITEMS.each do |key, _| %>
                            <div class="checkbox">
                                <label>
                                    <%= check_box_tag "selected_items[]", key, true %>
                                    <%= t("activerecord.models.#{key.to_s.singularize}") %>
                                </label>
                            </div>
                        <% end %>
                    </div>
                    <%= submit_tag "保存", class: "btn btn-success btn-sm" %>
                <% end %>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list"></i> サーバーバージョン一覧
                </h3>
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>UID</th>
                            <th>タイトル</th>
                            <th>作成日時</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% @server_versions.each do |version| %>
                            <tr>
                                <td><%= version.uid == ServerSetting.version_id ? content_tag(:strong, version.uid, style: "color: red;") : version.uid %></td>
                                <td><%= version.title %></td>
                                <td><%= version.created_at %></td>
                                <td>
                                    <%= link_to '詳細', server_version_path(version.uid), class: 'btn btn-info btn-sm' %>
                                </td>
                                <td>
                                    <%= form_tag select_server_version_path(version.uid), method: :post, class: 'form-inline' do %>
                                        <%= submit_tag '選択', class: 'btn btn-success btn-sm' %>
                                    <% end %>
                                </td>
                            </tr>
                        <% end %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
