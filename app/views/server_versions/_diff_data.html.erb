<!-- app/views/server_versions/_diff_data_card.html.erb -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-hashtag"></i> <%= title %>
        </h3>
    </div>
    <div class="card-body">
        <h4>追加データ</h4>
        <table class="table">
            <thead>
                <tr>
                    <th>キー</th>
                    <th>値</th>
                </tr>
            </thead>
            <tbody>
                <% hash[:add].each do |value| %>
                    <tr>
                        <td><%= value[:key] %></td>
                        <td><pre><code><%=  JSON.pretty_generate(value[:value]) %></code></pre></td>
                    </tr>
                <% end %>
            </tbody>
        </table>

        <h4>変更データ</h4>
        <table class="table">
            <thead>
                <tr>
                    <th>キー</th>
                    <th>値</th>
                </tr>
            </thead>
            <tbody>
                <% hash[:change].each do |value| %>
                    <tr>
                        <td><%= value[:key] %></td>
                        <td><pre><code><%=  JSON.pretty_generate(value[:value]) %></code></pre></td>
                    </tr>
                <% end %>
            </tbody>
        </table>

        <h4>削除データ</h4>
        <table class="table">
            <thead>
                <tr>
                    <th>キー</th>
                    <th>値</th>
                </tr>
            </thead>
            <tbody>
                <% hash[:delete].each do |value| %>
                    <tr>
                        <td><%= value[:key] %></td>
                        <td><pre><code><%=  JSON.pretty_generate(value[:value]) %></code></pre></td>
                    </tr>
                <% end %>
            </tbody>
        </table>
    </div>
</div>