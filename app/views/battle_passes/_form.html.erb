<%= form_with(model: battle_pass, url: battle_pass.persisted? ? battle_pass_path(battle_pass.uid) : battle_passes_path, local: true) do |f| %>
  <% if battle_pass.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(battle_pass.errors.count, "error") %> prohibited this battle pass from being saved:</h2>
      <ul>
        <% battle_pass.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Alpine.jsのストアを設定 -->
  <div x-data="{}" x-init="
    window.premiumRewardsStore = { 
      rewards: <%= @battle_pass.premium_rewards.present? ? @battle_pass.premium_rewards.to_json : '[]' %> 
    };
    window.freeRewardsStore = { 
      rewards: <%= @battle_pass.free_rewards.present? ? @battle_pass.free_rewards.to_json : '[]' %> 
    };
  "></div>

  <div class="form-group">
    <%= f.label :uid %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :season %>
    <%= f.number_field :season, class: 'form-control' %>
  </div>

  <div class="row">
    <!-- 左側にプレミアム報酬編集 -->
    <div class="col-md-8">
      <div class="form-group">
        <%= f.label :premium_rewards %>
        <%= render 'shared/rewards', rewards: @battle_pass.premium_rewards, param_name: 'battle_pass[premium_rewards]', store_name: 'premiumRewardsStore' %>
      </div>

      <div class="form-group">
        <%= f.label :free_rewards %>
        <%= render 'shared/rewards', rewards: @battle_pass.free_rewards, param_name: 'battle_pass[free_rewards]', store_name: 'freeRewardsStore' %>
      </div>
    </div>
    
    <!-- 右側に報酬プレビュー（フリーとプレミアムを同時表示） -->
    <div class="col-md-4">
      <%= render 'shared/battle_pass_rewards_display' %>
    </div>
  </div>

  <div class="card p-3 mb-4">
    <%= render 'shared/period', form: f %>
  </div>

  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
  </div>
<% end %>

<script>
function toggleDateFields(value) {
  const dateFields = document.getElementById('date-fields');
  if (value && value !== '') {
    dateFields.style.display = 'none';
  } else {
    dateFields.style.display = 'block';
  }
}

// 初期表示時にも実行
document.addEventListener('DOMContentLoaded', function() {
  const periodSelect = document.querySelector('select[name="battle_pass[period_id]"]');
  if (periodSelect) {
    toggleDateFields(periodSelect.value);
  }
});
</script> 