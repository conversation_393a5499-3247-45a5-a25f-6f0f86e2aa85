<h1>Battle Pass Details</h1>

<div class="card">
  <div class="card-body">
    <h5 class="card-title">UID: <%= @battle_pass.uid %></h5>
    
    <div class="row">
      <div class="col-md-6">
        <p>
          <strong>Season:</strong>
          <%= @battle_pass.season %>
        </p>

        <p>
          <strong>Start Date:</strong>
          <%= @battle_pass.start_at&.strftime('%Y-%m-%d %H:%M') %>
        </p>

        <p>
          <strong>End Date:</strong>
          <%= @battle_pass.end_at&.strftime('%Y-%m-%d %H:%M') %>
        </p>

        <p>
          <strong>Period:</strong>
          <%= @battle_pass.period&.name %>
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Alpine.jsのストアを設定 -->
<div x-data="{}" x-init="
  window.premiumRewardsStore = { 
    rewards: <%= @battle_pass.premium_rewards.present? ? @battle_pass.premium_rewards.to_json : '[]' %> 
  };
  window.freeRewardsStore = { 
    rewards: <%= @battle_pass.free_rewards.present? ? @battle_pass.free_rewards.to_json : '[]' %> 
  };
"></div>

<!-- バトルパス報酬表示 -->
<div class="mt-3">
  <%= render 'shared/battle_pass_rewards_display' %>
</div>

<div class="mt-3">
  <%= link_to 'Edit', edit_battle_pass_path(@battle_pass.uid), class: 'btn btn-warning' %>
  <%= link_to 'Back', battle_passes_path, class: 'btn btn-secondary' %>
</div> 