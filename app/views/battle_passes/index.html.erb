<h1 class="mb-6">バトルパス一覧</h1>

<%= render partial: "shared/table", locals: { 
    items: @battle_passes, 
    columns: ["UID", "シーズン",  "開始日", "終了日", "期間"], 
    values: [
      ->(item) { item.uid },
      ->(item) { item.season },
      ->(item) { item.period_start_at },
      ->(item) { item.period_end_at },
      ->(item) { item.period&.name || '(自由指定)' }
    ],
    model_name: "バトルパス一覧",
    edit_path: ->(item) { edit_battle_pass_path(item.uid) },
    delete_path: ->(item) { battle_pass_path(item.uid) },
    new_path: new_battle_pass_path
  } %>