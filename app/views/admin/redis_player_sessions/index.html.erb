<% content_for :title, "Redis Player Sessions - Admin Dashboard" %>

<div class="redis-player-sessions-dashboard">
  <div class="header">
    <h1>🎮 Redis Player Sessions Dashboard</h1>
    <p class="subtitle">高速処理・低負荷のRedis-onlyアーキテクチャ</p>
  </div>

  <!-- 統計情報 -->
  <div class="stats-grid">
    <div class="stat-card">
      <h3>📊 Total Active Players</h3>
      <div class="stat-number"><%= @stats[:total_active] %></div>
    </div>
    
    <div class="stat-card">
      <h3>⚔️ Battle Modes</h3>
      <% @stats[:by_battle_mode].each do |mode, count| %>
        <div class="stat-item">
          <span class="mode-name"><%= mode.capitalize %></span>
          <span class="mode-count"><%= count %></span>
        </div>
      <% end %>
    </div>
    
    <div class="stat-card">
      <h3>🔗 Active Channels</h3>
      <div class="stat-item">
        <span class="channel-name">Rank Match</span>
        <span class="channel-count"><%= @stats[:by_channel][:rank_match] %></span>
      </div>
      <div class="stat-item">
        <span class="channel-name">Free Match</span>
        <span class="channel-count"><%= @stats[:by_channel][:free_match] %></span>
      </div>
      <div class="stat-item">
        <span class="channel-name">Room Match</span>
        <span class="channel-count"><%= @stats[:by_channel][:room_match] %></span>
      </div>
    </div>
    
    <div class="stat-card">
      <h3>📈 Player Status</h3>
      <% @stats[:by_status].each do |status, count| %>
        <div class="stat-item">
          <span class="status-name"><%= status.capitalize %></span>
          <span class="status-count"><%= count %></span>
        </div>
      <% end %>
    </div>
  </div>

  <!-- アクション -->
  <div class="actions">
    <%= link_to "🧹 Cleanup Expired Sessions", 
        cleanup_admin_redis_player_sessions_path, 
        method: :post, 
        class: "btn btn-warning",
        confirm: "Are you sure you want to cleanup expired sessions?" %>
    
    <%= link_to "📊 Realtime Stats", 
        realtime_stats_admin_redis_player_sessions_path, 
        method: :post, 
        class: "btn btn-info",
        remote: true %>
        
    <%= link_to "🔄 Refresh", 
        admin_redis_player_sessions_path, 
        class: "btn btn-primary" %>
  </div>

  <!-- プレイヤーセッション一覧 -->
  <div class="sessions-table">
    <h2>🎯 Active Player Sessions</h2>
    
    <% if @sessions.any? %>
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Player ID</th>
            <th>Status</th>
            <th>Battle Mode</th>
            <th>Channels</th>
            <th>Connected At</th>
            <th>Last Activity</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <% @sessions.each do |player_id, session| %>
            <tr>
              <td>
                <strong><%= player_id %></strong>
                <% if session[:rank] %>
                  <br><small>Rank: <%= session[:rank] %></small>
                <% end %>
              </td>
              
              <td>
                <span class="status-badge status-<%= session[:status] %>">
                  <%= session[:status]&.capitalize %>
                </span>
              </td>
              
              <td>
                <% if session[:battle_mode] %>
                  <span class="battle-mode-badge mode-<%= session[:battle_mode] %>">
                    <%= session[:battle_mode]&.capitalize %>
                  </span>
                <% else %>
                  <span class="text-muted">None</span>
                <% end %>
              </td>
              
              <td>
                <div class="channel-indicators">
                  <% if session[:rank_match_channel] %>
                    <span class="channel-badge rank">Rank</span>
                  <% end %>
                  <% if session[:free_match_channel] %>
                    <span class="channel-badge free">Free</span>
                  <% end %>
                  <% if session[:room_match_channel] %>
                    <span class="channel-badge room">Room</span>
                  <% end %>
                </div>
              </td>
              
              <td>
                <% if session[:connected_at] %>
                  <%= Time.at(session[:connected_at]).strftime("%H:%M:%S") %>
                <% else %>
                  <span class="text-muted">Unknown</span>
                <% end %>
              </td>
              
              <td>
                <% if session[:last_activity] %>
                  <%= Time.at(session[:last_activity]).strftime("%H:%M:%S") %>
                  <br><small class="text-muted">
                    <%= time_ago_in_words(Time.at(session[:last_activity])) %> ago
                  </small>
                <% else %>
                  <span class="text-muted">Unknown</span>
                <% end %>
              </td>
              
              <td>
                <%= link_to "👁️ View", 
                    admin_redis_player_session_path(player_id), 
                    class: "btn btn-sm btn-outline-primary" %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="empty-state">
        <h3>😴 No Active Players</h3>
        <p>No player sessions found in Redis.</p>
      </div>
    <% end %>
  </div>
</div>

<style>
.redis-player-sessions-dashboard {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.subtitle {
  color: #666;
  font-style: italic;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
}

.stat-card h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #495057;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  color: #007bff;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-matching { background: #ffc107; color: #000; }
.status-matched { background: #28a745; color: #fff; }
.status-in_room { background: #17a2b8; color: #fff; }
.status-connected { background: #6c757d; color: #fff; }

.battle-mode-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.mode-rank { background: #dc3545; color: #fff; }
.mode-free { background: #28a745; color: #fff; }
.mode-room { background: #007bff; color: #fff; }

.channel-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  margin-right: 4px;
}

.channel-badge.rank { background: #dc3545; color: #fff; }
.channel-badge.free { background: #28a745; color: #fff; }
.channel-badge.room { background: #007bff; color: #fff; }

.actions {
  margin-bottom: 30px;
}

.btn {
  margin-right: 10px;
  margin-bottom: 10px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
