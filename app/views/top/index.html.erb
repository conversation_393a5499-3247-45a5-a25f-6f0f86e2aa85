<% content_for :page_title, "ダッシュボード" %>

<div class="row">
  <!-- 総対戦人数 -->
  <div class="col-lg-3 col-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3><%= @all_room_count * 2 %></h3>
        <p>総対戦人数</p>
      </div>
      <div class="icon">
        <i class="fas fa-users"></i>
      </div>
    </div>
  </div>

  <!-- ランクマッチ対戦人数 -->
  <div class="col-lg-3 col-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3><%= @rank_room_count * 2 %></h3>
        <p>ランクマッチ対戦人数</p>
      </div>
      <div class="icon">
        <i class="fas fa-trophy"></i>
      </div>
    </div>
  </div>

  <!-- フリー対戦人数 -->
  <div class="col-lg-3 col-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3><%= @free_room_count * 2 %></h3>
        <p>フリー対戦人数</p>
      </div>
      <div class="icon">
        <i class="fas fa-gamepad"></i>
      </div>
    </div>
  </div>

  <!-- ランクマッチ待機人数 -->
  <div class="col-lg-3 col-6">
    <div class="small-box bg-danger">
      <div class="inner">
        <h3><%= @rank_wait_user_count %></h3>
        <p>ランクマッチ待機人数</p>
      </div>
      <div class="icon">
        <i class="fas fa-clock"></i>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- 追加の統計情報カード -->
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-chart-pie mr-1"></i>
          対戦状況
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-sm-6">
            <div class="description-block border-right">
              <span class="description-percentage text-success"><i class="fas fa-caret-up"></i> <%= ((@rank_room_count.to_f / @all_room_count.to_f) * 100).round(1) if @all_room_count > 0 %>%</span>
              <h5 class="description-header"><%= @rank_room_count %></h5>
              <span class="description-text">ランクマッチルーム</span>
            </div>
          </div>
          <div class="col-sm-6">
            <div class="description-block">
              <span class="description-percentage text-warning"><i class="fas fa-caret-left"></i> <%= ((@free_room_count.to_f / @all_room_count.to_f) * 100).round(1) if @all_room_count > 0 %>%</span>
              <h5 class="description-header"><%= @free_room_count %></h5>
              <span class="description-text">フリールーム</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-users mr-1"></i>
          プレイヤー状況
        </h3>
      </div>
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center border-bottom mb-3">
          <p class="text-success text-xl">
            <i class="fas fa-play"></i>
          </p>
          <p class="d-flex flex-column text-right">
            <span class="font-weight-bold">
              <i class="fas fa-arrow-up text-success"></i> <%= @all_room_count * 2 %>
            </span>
            <span class="text-muted">対戦中プレイヤー</span>
          </p>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <p class="text-warning text-xl">
            <i class="fas fa-pause"></i>
          </p>
          <p class="d-flex flex-column text-right">
            <span class="font-weight-bold">
              <i class="fas fa-arrow-up text-warning"></i> <%= @rank_wait_user_count %>
            </span>
            <span class="text-muted">待機中プレイヤー</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-star mr-1"></i>
          ランクマッチ レーティング情報
        </h3>
      </div>
      <div class="card-body">
        <% if @rank_room_rates.any? %>
          <div class="row">
            <div class="col-md-6">
              <h5>現在の対戦（平均レート順）</h5>
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>対戦</th>
                      <th>プレイヤー1</th>
                      <th>プレイヤー2</th>
                      <th>レート差</th>
                    </tr>
                  </thead>
                  <tbody>
                    <% @rank_room_rates.each_with_index do |room, index| %>
                      <tr>
                        <td>対戦 <%= index + 1 %></td>
                        <td>
                          <% if room[:user_0_open_id].present? %>
                            <%= link_to user_page_show_path(room[:user_0_open_id]), class: "text-decoration-none" do %>
                              <span class="badge badge-primary"><%= room[:user_0_name] %></span>
                              <small class="text-muted d-block">(<%= room[:rate_0] %>)</small>
                            <% end %>
                          <% else %>
                            <span class="badge badge-primary"><%= room[:user_0_name] %></span>
                            <small class="text-muted d-block">(<%= room[:rate_0] %>)</small>
                          <% end %>
                        </td>
                        <td>
                          <% if room[:user_1_open_id].present? %>
                            <%= link_to user_page_show_path(room[:user_1_open_id]), class: "text-decoration-none" do %>
                              <span class="badge badge-primary"><%= room[:user_1_name] %></span>
                              <small class="text-muted d-block">(<%= room[:rate_1] %>)</small>
                            <% end %>
                          <% else %>
                            <span class="badge badge-primary"><%= room[:user_1_name] %></span>
                            <small class="text-muted d-block">(<%= room[:rate_1] %>)</small>
                          <% end %>
                        </td>
                        <td>
                          <% rate_diff = (room[:rate_0] - room[:rate_1]).abs %>
                          <span class="badge <%= rate_diff >= 1500 ? 'badge-danger' : (rate_diff >= 700 ? 'badge-warning' : 'badge-success') %>">
                            <%= rate_diff %>
                          </span>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-md-6">
              <h5>レーティング統計</h5>
              <div class="info-box mb-3">
                <span class="info-box-icon bg-info"><i class="fas fa-chart-line"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">平均レーティング</span>
                  <span class="info-box-number"><%= @rank_rates.any? ? (@rank_rates.sum.to_f / @rank_rates.length).round(1) : 0 %></span>
                </div>
              </div>
              <div class="info-box mb-3">
                <span class="info-box-icon bg-success"><i class="fas fa-arrow-up"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">最高レーティング</span>
                  <span class="info-box-number"><%= @rank_rates.any? ? @rank_rates.max : 0 %></span>
                </div>
              </div>
              <div class="info-box mb-3">
                <span class="info-box-icon bg-warning"><i class="fas fa-arrow-down"></i></span>
                <div class="info-box-content">
                  <span class="info-box-text">最低レーティング</span>
                  <span class="info-box-number"><%= @rank_rates.any? ? @rank_rates.min : 0 %></span>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            現在ランクマッチの対戦が行われていません。
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>