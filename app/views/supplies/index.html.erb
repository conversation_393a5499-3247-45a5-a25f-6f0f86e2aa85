<div class="content-header">
  <div class="container-fluid">
    <div class="row mb-2">
      <div class="col-sm-6">
        <h1 class="m-0">サプライ一覧</h1>
      </div>
      <div class="col-sm-6 text-right">
        <%= link_to '新規作成', new_supply_path, class: 'btn btn-primary' %>
      </div>
    </div>
  </div>
</div>

<section class="content">
  <div class="container-fluid">
    <div class="card">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          
          <div class="ml-3">
            <%= form_tag supplies_path, method: :get, class: 'd-flex align-items-center' do %>
              <label class="mr-2">種類:</label>
              <%= link_to 'すべて', supplies_path, 
                  class: "btn #{params[:category].blank? ? 'btn-primary' : 'btn-info'} mr-2" %>
              <% Supply.options_for_select.each do |category| %>
                <%= link_to category[0], supplies_path(category: category[1]), 
                    class: "btn #{params[:category] == category[1] ? 'btn-primary' : 'btn-secondary'} mr-2" %>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>

      <div class="card-body">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>UID</th>
              <th>カテゴリー</th>
              <th>ダイヤコスト</th>
              <th>報酬</th>
              <th>期間</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% @supplies.each do |supply| %>
              <tr>
                <td><%= supply.uid %></td>
                <td><i class="<%= Item.item_icon(supply.category) %>"></i></td>
                <td><%= supply.dia_cost %></td>
                <td><%= Item.rewards_to_s(supply.rewards).html_safe %></td>
                <td><%= Period.get_period_str(supply) %></td>
                <td>
                  <%= link_to supply_path(supply.uid), class: 'btn btn-info btn-sm' do %>
                    <i class="fas fa-eye"></i> 詳細
                  <% end %>
                  <%= link_to edit_supply_path(supply.uid), class: 'btn btn-warning btn-sm' do %>
                    <i class="fas fa-edit"></i> 編集
                  <% end %>
                  <%= link_to supply_path(supply.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" },class: "btn btn-danger btn-sm" do %>
                    <i class="fas fa-trash"></i> 削除
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section> 