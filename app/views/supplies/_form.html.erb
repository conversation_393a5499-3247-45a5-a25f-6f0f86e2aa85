<%= form_with(model: supply, url: supply.persisted? ? supply_path(supply.uid) : supplies_path, local: true) do |f| %>
  <% if supply.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(supply.errors.count, "error") %> prohibited this supply from being saved:</h2>
      <ul>
        <% supply.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= f.label :uid %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>


  <div class="form-group">
    <%= f.label :category, "カテゴリ" %>
    <%= f.select :category, 
             Item.options_for_select,
             { include_blank: "選択してください" },
             { class: "form-select" } %>
  </div>

  <div class="form-group">
    <%= f.label :dia_cost %>
    <%= f.number_field :dia_cost, class: 'form-control' %>
  </div>

  <%= render 'shared/rewards', rewards: supply.rewards, param_name: 'supply[rewards]' %>

  <%= render 'shared/period', form: f %>
  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
    <% unless supply.persisted? %>
      <%= f.submit '連続作成', name: 'continuous_create', class: 'btn btn-success ms-2' %>
    <% end %>
  </div>
<% end %> 