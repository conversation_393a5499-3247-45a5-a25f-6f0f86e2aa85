<div class="container-fluid">
  <h1 class="mb-4"><%= @title %></h1>

  <%= render partial: "shared/table", locals: {
    items: @users,
    columns: ["プレイヤー", "部屋", "状態", "更新時間"],
    values: [
      ->(user) { user[0] },
      ->(user) { user[1][:metadata]&.dig(:room_id) || "---" },
      ->(user) { user[1][:status] },
      ->(user) { user[1][:updated_at].strftime("%Y年%m月%d日 %H時%M分%S秒") }
    ],
    model_name: @title,
    edit_path: nil,
    delete_path: nil,
    new_path: nil
  } %>

  <%= link_to "戻る", user_statuses_path, class: "btn btn-secondary" %>
</div>
