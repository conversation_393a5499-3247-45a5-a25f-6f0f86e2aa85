<div class="container-fluid">
  <h1 class="mb-4"><%= @title %></h1>

  <div class="card">
    <div class="card-header">
      <h5 class="mb-0"><%= @title %> (<%= @count %> users) - Redis-based</h5>
    </div>
    <div class="card-body">
      <% if @users.any? %>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>プレイヤーID</th>
                <th>ステータス</th>
                <th>部屋ID</th>
                <th>レート/ランク</th>
                <th>更新時間</th>
                <th>アクション</th>
              </tr>
            </thead>
            <tbody>
              <% @users.each do |user_id, data| %>
                <tr>
                  <td><%= user_id %></td>
                  <td>
                    <span class="badge bg-<%= data[:status] == 'matching' ? 'warning' : data[:status] == 'matched' ? 'success' : 'info' %>">
                      <%= data[:status] %>
                    </span>
                  </td>
                  <td><%= data[:metadata]['room_id'] || '---' %></td>
                  <td><%= data[:metadata]['rank'] || data[:metadata]['rate'] || 0 %></td>
                  <td>
                    <small><%= data[:updated_at].strftime("%Y年%m月%d日 %H時%M分%S秒") %></small>
                  </td>
                  <td>
                    <%= link_to "削除", remove_user_statuses_path(user_id: user_id),
                        method: :delete,
                        class: "btn btn-sm btn-outline-danger",
                        confirm: "ユーザー #{user_id} のステータスを削除しますか？" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="alert alert-info">
          <i class="fas fa-info-circle"></i>
          該当するユーザーが見つかりません。
        </div>
      <% end %>
    </div>
  </div>

  <%= link_to "戻る", user_statuses_path, class: "btn btn-secondary" %>
</div>
