<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold"><%= @title %></h1>
    <a href="<%= user_statuses_path %>" class="text-blue-500 hover:underline">← 概要に戻る</a>
  </div>

  <div class="mb-6">
    <div class="flex space-x-2">
      <a href="<%= all_user_statuses_path %>" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">全て</a>
      <a href="<%= matching_user_statuses_path %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">マッチを探しています</a>
      <a href="<%= matched_user_statuses_path %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">ペアリング</a>
      <a href="<%= in_room_user_statuses_path %>" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">部屋の中</a>
    </div>
  </div>

  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">全ユーザーステータス (<%= @count %> users) - Redis-based</h5>
    </div>
    <div class="card-body">
      <% if @statuses.any? %>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>プレイヤーID</th>
                <th>ステータス</th>
                <th>チャンネルタイプ</th>
                <th>メタデータ</th>
                <th>更新時間</th>
                <th>アクション</th>
              </tr>
            </thead>
            <tbody>
              <% @statuses.each do |user_id, data| %>
                <tr>
                  <td><%= user_id %></td>
                  <td>
                    <span class="badge bg-<%= data[:status] == 'matching' ? 'warning' : data[:status] == 'matched' ? 'success' : 'info' %>">
                      <%= data[:status] %>
                    </span>
                  </td>
                  <td>
                    <small><%= data[:metadata]['channel_type'] || '---' %></small>
                  </td>
                  <td>
                    <small>
                      <% if data[:metadata]['room_id'] %>
                        Room: <%= data[:metadata]['room_id'] %><br>
                      <% end %>
                      <% if data[:metadata]['rank'] || data[:metadata]['rate'] %>
                        Rate: <%= data[:metadata]['rank'] || data[:metadata]['rate'] %><br>
                      <% end %>
                      <% if data[:metadata]['battle_mode'] %>
                        Mode: <%= data[:metadata]['battle_mode'] %>
                      <% end %>
                    </small>
                  </td>
                  <td>
                    <small><%= data[:updated_at].strftime("%Y年%m月%d日 %H時%M分%S秒") %></small>
                  </td>
                  <td>
                    <%= link_to "削除", remove_user_statuses_path(user_id: user_id),
                        method: :delete,
                        class: "btn btn-sm btn-outline-danger",
                        confirm: "ユーザー #{user_id} のステータスを削除しますか？" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="alert alert-info">
          <i class="fas fa-info-circle"></i>
          ユーザーが見つかりません。
        </div>
      <% end %>
    </div>
  </div>
</div>
