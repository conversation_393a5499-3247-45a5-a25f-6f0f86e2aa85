<%= form_with(model: battle_record, url: battle_record.persisted? ? battle_record_path(battle_record.uid) : battle_records_path, local: true) do |f| %>
  <% if battle_record.errors.any? %>
    <div class="alert alert-danger">
      <h2><%= pluralize(battle_record.errors.count, "error") %> prohibited this battle record from being saved:</h2>
      <ul>
        <% battle_record.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group">
    <%= f.label :uid %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :category %>
    <%= f.text_field :category, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :user_id_1, 'Player 1' %>
    <%= f.text_field :user_id_1, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :user_id_2, 'Player 2' %>
    <%= f.text_field :user_id_2, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :date %>
    <%= f.datetime_field :date, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :winner_id, 'Winner' %>
    <%= f.text_field :winner_id, class: 'form-control' %>
  </div>

  <div class="form-group">
    <%= f.label :reason %>
    <%= f.text_field :reason, class: 'form-control' %>
  </div>

  <div class="actions">
    <%= f.submit class: 'btn btn-primary' %>
  </div>
<% end %> 