<h1>Battle Records</h1>

<table class="table">
  <thead>
    <tr>
      <th>UID</th>
      <th>Category</th>
      <th>Date</th>
      <th>Players</th>
      <th>Winner</th>
      <th>Actions</th>
    </tr>
  </thead>

  <tbody>
    <% @battle_records.each do |battle_record| %>
      <tr>
        <td><%= battle_record.uid %></td>
        <td><%= battle_record.category %></td>
        <td><%= battle_record.date&.strftime('%Y-%m-%d %H:%M') %></td>
        <td>
          <%= battle_record.user1&.name %> vs <%= battle_record.user2&.name %>
        </td>
        <td><%= battle_record.winner&.name %></td>
        <td>
          <%= link_to '詳細', battle_record_path(battle_record.uid), class: 'btn btn-info btn-sm' %>
          <%= link_to '編集', edit_battle_record_path(battle_record.uid), class: 'btn btn-warning btn-sm' %>
          <%= link_to '削除', battle_record_path(battle_record.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: 'btn btn-danger btn-sm' %>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>

<%= link_to 'New Battle Record', new_battle_record_path, class: 'btn btn-primary' %> 