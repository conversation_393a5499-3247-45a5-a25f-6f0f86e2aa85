<div class="container my-4">
  <h1 class="mb-4">課金アイテム管理</h1>

  <%= link_to "新しい課金アイテムを作成", new_charging_item_path, class: "btn btn-primary mb-3" %>

  <%= render partial: "shared/table", locals: {
      items: @charging_items,
      columns: ["UID", "名前", "カテゴリ", "プラットフォームキー", "期間", "購入上限"],
      values: [
        ->(item) { item.uid },
        ->(item) { json_lang(item.name) },
        ->(item) { item.category },
        ->(item) { 
          keys = []
          keys << "iOS: #{item.ios_key}" if item.ios_key.present?
          keys << "Android: #{item.android_key}" if item.android_key.present?
          keys << "Steam: #{item.steam_key}" if item.steam_key.present?
          keys.join("<br>").html_safe
        },
        ->(item) { 
          if item.start_at.present? && item.end_at.present?
            "#{item.start_at.strftime('%Y/%m/%d')} - #{item.end_at.strftime('%Y/%m/%d')}"
          elsif item.start_at.present?
            "#{item.start_at.strftime('%Y/%m/%d')} から"
          elsif item.end_at.present?
            "#{item.end_at.strftime('%Y/%m/%d')} まで"
          else
            "無期限"
          end
        },
        ->(item) { item.max_count.present? ? item.max_count : "無制限" },
      ],
      model_name: "課金アイテム一覧",
      edit_path: ->(item) { edit_charging_item_path(item.uid) },
      delete_path: ->(item) { charging_item_path(item.uid) },
      new_path: new_charging_item_path
    } %>
</div>
