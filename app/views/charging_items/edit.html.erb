<div class="container my-4">
  <h1 class="mb-4">課金アイテム編集</h1>

  <%= form_with model: @charging_item, url: charging_item_path(@charging_item.uid), local: true do |f| %>
    <div class="form-group mb-3">
      <%= f.label :uid, 'UID', class: 'form-label' %>
      <%= f.number_field :uid, class: 'form-control' %>
    </div>
    
    <div class="card p-4 mb-4">
      <%= json_lang_form(f, :name, label: "名前") %>
      <%= json_lang_form(f, :desc, label: "説明", as: :text_area) %>
    </div>

    <div class="card p-4 mb-4">
      <h5>プラットフォーム情報</h5>
      <div class="form-group mb-3">
        <%= f.label :category, 'カテゴリ', class: 'form-label' %>
        <div>
          <div class="form-check form-check-inline">
            <%= f.radio_button :category, 'dia', class: 'form-check-input' %>
            <%= f.label :category_dia, 'ダイヤ', class: 'form-check-label' %>
          </div>
          <div class="form-check form-check-inline">
            <%= f.radio_button :category, 'bundle', class: 'form-check-input' %>
            <%= f.label :category_bundle, 'バンドル', class: 'form-check-label' %>
          </div>
        </div>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :ios_key, 'iOSキー', class: 'form-label' %>
        <%= f.text_field :ios_key, class: 'form-control' %>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :android_key, 'Androidキー', class: 'form-label' %>
        <%= f.text_field :android_key, class: 'form-control' %>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :steam_key, 'Steamキー', class: 'form-label' %>
        <%= f.text_field :steam_key, class: 'form-control' %>
      </div>
    </div>
    
    <div class="card p-4 mb-4">
      <h5>販売期間と制限</h5>
      <div class="form-group mb-3">
        <%= f.label :start_at, '開始日時', class: 'form-label' %>
        <%= f.datetime_field :start_at, class: 'form-control' %>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :end_at, '終了日時', class: 'form-label' %>
        <%= f.datetime_field :end_at, class: 'form-control' %>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :period_id, '期間ID', class: 'form-label' %>
        <%= f.number_field :period_id, class: 'form-control' %>
      </div>
      
      <div class="form-group mb-3">
        <%= f.label :max_count, '最大購入回数', class: 'form-label' %>
        <%= f.number_field :max_count, class: 'form-control' %>
      </div>
    </div>

    <div class="card p-4 mb-4">
      <h5>報酬情報</h5>
      <%= render 'shared/rewards', rewards: @charging_item.rewards, param_name: 'charging_item[rewards]' %>
    </div>

    <%= f.submit '更新', class: 'btn btn-primary' %>
    <%= link_to '戻る', charging_items_path, class: 'btn btn-secondary ms-2' %>
  <% end %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const otherCategoryRadio = document.getElementById('other-category-edit');
    const customCategoryInput = document.getElementById('other-category-input-edit');
    const customCategoryField = document.getElementById('custom-category-edit');
    const categoryRadios = document.querySelectorAll('input[name="charging_item[category]"]');
    
    // 初期値の設定（既存のカテゴリがdia/bundle以外ならその他を選択）
    const currentCategory = '<%= @charging_item.category %>';
    if (currentCategory && currentCategory !== 'dia' && currentCategory !== 'bundle') {
      otherCategoryRadio.checked = true;
      customCategoryInput.style.display = 'block';
      customCategoryField.value = currentCategory;
    }
    
    // ラジオボタンの変更を監視
    categoryRadios.forEach(function(radio) {
      radio.addEventListener('change', function() {
        if (otherCategoryRadio.checked) {
          customCategoryInput.style.display = 'block';
        } else {
          customCategoryInput.style.display = 'none';
        }
      });
    });
  });
</script>
