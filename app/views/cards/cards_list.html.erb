<%# タイトルをページのタイトルとして設定 %>
<% content_for :page_title do %>
  <%= @title %>
<% end %>

<%# このページ専用のスタイル設定 %>
<style>
  .cards-list-page {
    font-size: 0.85rem;
  }
  .cards-list-page h3 {
    font-size: 1.5rem;
  }
  .cards-list-page .table {
    font-size: 0.8rem;
  }
  .cards-list-page .badge {
    font-size: 0.75rem;
  }
  .cards-list-page small {
    font-size: 0.75rem;
  }
</style>

<div class="card cards-list-page">
  <div class="card-header">
    <h3 class="card-title"><%= @title %></h3>
  </div>
  <div class="card-body">
    <% if @cards.present? %>
      <% # 役割の優先度を定義 (C->A->Dの順) %>
      <% role_priority = { "C" => 0, "A" => 1, "D" => 2 } %>
      <% # カードを役割、コスト、パワーでソート %>
      <% sorted_cards = @cards.sort_by do |card|
        [
          role_priority[card["Role"].to_s.upcase] || 999,
          card["Cost"].to_i,
          card["Power"].to_i
        ]
      end %>

      <div class="table-responsive">
        <table class="table table-striped table-bordered">
          <thead class="thead-dark">
            <tr>
              <th style="width: 50px;">ID</th>
              <th style="width: 150px;">カード名</th>
              <th style="width: 100px;">種族</th>
              <th style="width: 130px;">役割</th>
              <th style="width: 80px;">コスト</th>
              <th style="width: 80px;">パワー</th>
              <th style="width: 80px;">レア度</th>
              <th style="width: 100px;">ステータス</th>
              <th style="width: 150px;">能力0</th>
              <th style="width: 150px;">能力1</th>
              <th style="width: 150px;">能力2</th>
              <th style="width: 150px;">スクリプト0</th>
              <th style="width: 150px;">スクリプト1</th>
              <th style="width: 150px;">スクリプト2</th>
            </tr>
          </thead>
          <tbody>
            <% sorted_cards.each do |card| %>
              <% 
                # 修正済みカードの表示スタイルを設定
                row_class = ""
                if card["IsModified"]
                  if card["IsNerf"]
                    row_class = "table-warning" # 下方修正は黄色背景
                  else
                    row_class = "table-info" # 通常修正は青色背景
                  end
                end
              %>
              <tr class="<%= row_class %>">
                <td><%= card["ID"] %></td>
                <td><%= card["Name"] %></td>
                <td>
                  <% if card["Group"].present? %>
                    <%= MasterDatum.get_group(card["Group"])&.dig("Name") || '不明' %>
                  <% end %>
                </td>
                <td>
                  <% role_map = {"C" => "チャージャー", "A" => "アタッカー", "D" => "ディフェンダー"} %>
                  <%= role_map[card["Role"].to_s.upcase] || card["Role"] %>
                </td>
                <td><%= card["Cost"] %></td>
                <td><%= card["Power"] %></td>
                <td>
                  <% rarity_map = {1 => "ブロンズ", 2 => "シルバー", 3 => "ゴールド", 4 => "レジェンド"} %>
                  <%= rarity_map[card["Rarity"]] || card["Rarity"] %>
                </td>
                <td>
                  <% if card["IsModified"] %>
                    <span class="badge badge-<%= card["IsNerf"] ? "warning" : "info" %>">
                      <%= card["IsNerf"] ? "下方修正" : "修正済み" %>
                    </span>
                  <% else %>
                  <% end %>
                </td>
                <td>
                  <% if card["Text0"].present? %>
                    <small>
                      <strong><%= card["Timing0"] %>:</strong> <%= card["Text0"] %>
                    </small>
                  <% end %>
                </td>
                <td>
                  <% if card["Text1"].present? %>
                    <small>
                      <strong><%= card["Timing1"] %>:</strong> <%= card["Text1"] %>
                    </small>
                  <% end %>
                </td>
                <td>
                  <% if card["Text2"].present? %>
                    <small>
                      <strong><%= card["Timing2"] %>:</strong> <%= card["Text2"] %>
                    </small>
                  <% end %>
                </td>
                <td>
                  <% if card["Script0"].present? %>
                    <details>
                      <summary>表示</summary>
                      <div class="script-content"><%= card["Script0"] %></div>
                    </details>
                  <% end %>
                </td>
                <td>
                  <% if card["Script1"].present? %>
                    <details>
                      <summary>表示</summary>
                      <div class="script-content"><%= card["Script1"] %></div>
                    </details>
                  <% end %>
                </td>
                <td>
                  <% if card["Script2"].present? %>
                    <details>
                      <summary>表示</summary>
                      <div class="script-content"><%= card["Script2"] %></div>
                    </details>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <!-- 凡例 -->
      <div class="mt-3">
        <h5>凡例：</h5>
        <span class="badge badge-info mr-2">修正済み</span>
        <span class="badge badge-warning mr-2">下方修正</span>
        <span class="badge badge-secondary">未修正</span>
      </div>

      <!-- スクリプト表示用のCSS -->
      <style>
        .script-content {
          max-width: 150px;
          word-break: break-all;
          white-space: pre-wrap;
          font-size: 0.75rem;
        }
        details summary {
          cursor: pointer;
          color: #007bff;
        }
        details summary:hover {
          text-decoration: underline;
        }
        th, td {
          white-space: normal !important;
          vertical-align: middle !important;
        }
      </style>
    <% else %>
      <div class="alert alert-info">
        カードデータがありません。
      </div>
    <% end %>
  </div>
</div> 