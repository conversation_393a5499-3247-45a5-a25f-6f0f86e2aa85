
<h1 class="mb-6">期間一覧</h1>


<div class="alert alert-info alert-dismissible">
<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
<h5><i class="icon fas fa-info"></i> 注意事項</h5>
<ul class="mb-0">
  <li>期間は重複して設定することができます</li>
  <li>アクティブな期間のみがゲーム内で有効になります</li>
  <li>全て日本時間を基準としています。例えば、2025-02-26 19:00:00 +0900 であれば、日本時間で2/26 19:00:00 になります。</li>
</ul>
</div>

<%= render partial: "shared/table", locals: {
    items: @periods,
    columns: ["UID", "期間名", "開始日", "終了日", "永続", "アクティブ"],
    values: [
      ->(item) { item.uid },
      ->(item) { item.name},
      ->(item) { item.start_at },
      ->(item) { item.end_at },
      ->(item) { bool_icon(item.is_persistent) },
      ->(item) { bool_icon(item.is_active) }
    ],
    model_name: "期間一覧",
    edit_path: ->(item) { edit_period_path(item.uid) },
    delete_path: ->(item) { period_path(item.uid) },
    new_path: new_period_path
  } %>

  <%= month_calendar(events: @periods, attribute: :start_at, end_attribute: :end_at) do |date, periods| %>
    <%= date.day %>
    <% periods.each do |period| %>
        <br>
        <span class="badge bg-primary me-1"><%= period.name %></span>
    <% end %>
  <% end %>