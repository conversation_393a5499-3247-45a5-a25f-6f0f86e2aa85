<%= form_with(model: period, url: period.persisted? ? period_path(period.uid) : periods_path) do |form| %>
  <% if period.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(period.errors.count, "error") %> prohibited this period from being saved:</h4>
      <ul>
        <% period.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="card p-4 mb-4">
    <%= form.label :uid, "UID" %>
    <%= form.number_field :uid %>
    <%= form.label :name, "期間名" %>
    <%= form.text_field :name %>
    <%= form.label :description, "説明" %>
    <%= form.text_area :description %>
    <%= form.label :start_at, "開始日" %>
    <%= form.datetime_field :start_at %>
    <%= form.label :end_at, "終了日" %>
    <%= form.datetime_field :end_at %>
    <%= form.label :is_persistent, "永続" %>
    <%= form.check_box :is_persistent %>
    <%= form.label :is_active, "アクティブ" %>
    <%= form.check_box :is_active %>
  </div>

  <div class="actions">
    <%= form.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %>
