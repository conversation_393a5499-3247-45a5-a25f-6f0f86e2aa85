<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">バナー一覧</h3>
          <div class="card-tools">
            <%= link_to '新規バナー作成', new_banner_path, class: 'btn btn-primary' %>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>UID</th>
                  <th>画像</th>
                  <th>タイトル</th>
                  <th>表示タイプ</th>
                  <th>表示UID</th>
                  <th>開始日時</th>
                  <th>終了日時</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <% @banners.each do |banner| %>
                  <tr>
                    <td><%= banner.uid %></td>
                    <td>
                      <% if banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]).present? %>
                        <img src="<%= banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]) %>" alt="<%= banner.title %>" class="img-thumbnail" style="max-width: 100px;">
                      <% else %>
                        <span class="text-muted">画像なし</span>
                      <% end %>
                    </td>
                    <td><%= banner.title %></td>
                    <td><%= banner.view_type %></td>
                    <td><%= banner.view_uid %></td>
                    <td><%= banner.start_at&.strftime('%Y/%m/%d %H:%M') %></td>
                    <td><%= banner.end_at&.strftime('%Y/%m/%d %H:%M') %></td>
                    <td>
                      <% if banner.uid.present? %>
                        <%= link_to '編集', edit_banner_path(banner.uid), class: 'btn btn-sm btn-info' %>
                        <%= link_to '削除', banner_path(banner.uid), data: { turbo_method: :delete, turbo_confirm: '本当に削除しますか？' }, class: 'btn btn-sm btn-danger' %>
                      <% else %>
                        <span class="text-muted">操作不可</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 