<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">バナー詳細</h3>
          <div class="card-tools">
            <%= link_to '編集', edit_banner_path(@banner.uid), class: 'btn btn-info' %>
            <%= link_to '一覧に戻る', banners_path, class: 'btn btn-secondary' %>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>UID</label>
                <p class="form-control-static"><%= @banner.uid %></p>
              </div>

              <div class="form-group">
                <label>タイトル</label>
                <p class="form-control-static"><%= @banner.title %></p>
              </div>

              <div class="form-group">
                <label>表示タイプ</label>
                <p class="form-control-static"><%= @banner.view_type %></p>
              </div>

              <div class="form-group">
                <label>表示UID</label>
                <p class="form-control-static"><%= @banner.view_uid %></p>
              </div>
            </div>

            <div class="col-md-6">
              <div class="form-group">
                <label>バナー画像</label>
                <div>
                  <% if @banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]).present? %>
                    <img src="<%= @banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]) %>" alt="<%= @banner.title %>" class="img-thumbnail" style="max-width: 300px;">
                  <% else %>
                    <span class="text-muted">画像なし</span>
                  <% end %>
                </div>
              </div>

              <div class="form-group">
                <label>開始日時</label>
                <p class="form-control-static"><%= @banner.start_at&.strftime('%Y/%m/%d %H:%M') %></p>
              </div>

              <div class="form-group">
                <label>終了日時</label>
                <p class="form-control-static"><%= @banner.end_at&.strftime('%Y/%m/%d %H:%M') %></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
