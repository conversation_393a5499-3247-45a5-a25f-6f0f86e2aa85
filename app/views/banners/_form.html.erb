<%= form_with(model: banner, url: banner.persisted? ? banner_path(banner.uid) : banners_path) do |form| %>
  <% if banner.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(banner.errors.count, "error") %> prohibited this banner from being saved:</h4>
      <ul>
        <% banner.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="card p-4 mb-4">
    <div class="form-group mb-3">
      <%= form.label :uid, 'UID', class: 'form-label' %>
      <%= form.number_field :uid, class: 'form-control' %>
    </div>

    <div class="form-group mb-3">
      <%= form.label :banner_image, 'バナー画像', class: 'form-label' %>
      <%= form.file_field :banner_image, class: 'form-control', accept: 'image/*' %>
      <small class="form-text text-muted">バナー画像をアップロードしてください</small>
      <% if banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]).present? %>
        <div class="mt-2">
          <img src="<%= banner.banner_image_url(host: @image_url_options[:host], port: @image_url_options[:port]) %>" alt="<%= banner.title %>" class="img-thumbnail" style="max-width: 200px;">
          <div class="form-check mt-2">
            <%= form.check_box :remove_image, class: 'form-check-input' %>
            <%= form.label :remove_image, '画像を削除する', class: 'form-check-label' %>
          </div>
        </div>
      <% end %>
    </div>

    <div class="form-group mb-3">
      <%= form.label :title, 'タイトル', class: 'form-label' %>
      <%= form.text_field :title, class: 'form-control' %>
    </div>

    <div class="form-group mb-3">
      <%= form.label :view_type, '表示タイプ', class: 'form-label' %>
      <%= form.select :view_type, ['info', 'shop', 'rank_battle', 'free_battle', 'event_battle', ], { include_blank: '選択してください' }, class: 'form-control' %>
    </div>

    <div class="form-group mb-3">
      <%= form.label :view_uid, '表示UID', class: 'form-label' %>
      <%= form.number_field :view_uid, class: 'form-control' %>
      <small class="form-text text-muted">表示タイプに対応するコンテンツのUIDを入力してください</small>
    </div>

    <div class="form-group mb-3">
      <%= form.label :period_id, '期間設定', class: 'form-label' %>
      <%= form.select :period_id, Period.all.collect { |p| [p.name + " (" + p.start_at.strftime("%Y/%m/%d") + "~" + p.end_at.strftime("%Y/%m/%d") + ")", p.id] }, { prompt: '自由指定' }, { class: 'form-control', onchange: 'toggleBannerDateFields(this.value)' } %>
    </div>

    <div id="banner-date-fields" style="display: block;">
      <div class="form-group mb-3">
        <%= form.label :start_at, '開始日時', class: 'form-label' %>
        <%= form.datetime_field :start_at, class: 'form-control' %>
      </div>

      <div class="form-group mb-3">
        <%= form.label :end_at, '終了日時', class: 'form-label' %>
        <%= form.datetime_field :end_at, class: 'form-control' %>
      </div>
    </div>
  </div>

  <div class="actions">
    <%= form.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %>

<script>
function toggleBannerDateFields(periodId) {
  const dateFields = document.getElementById('banner-date-fields');
  if (periodId === '') {
    dateFields.style.display = 'block';
  } else {
    dateFields.style.display = 'none';
  }
}
</script> 