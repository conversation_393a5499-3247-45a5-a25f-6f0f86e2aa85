<% content_for :page_title, "#{@user.name} のバン設定" %>

<div class="card">
  <div class="card-header">
    <h3 class="card-title">ユーザーバン設定</h3>
  </div>
  <div class="card-body">
    <% if flash[:error] %>
      <div class="alert alert-danger">
        <%= flash[:error] %>
      </div>
    <% end %>
    
    <% if flash[:notice] %>
      <div class="alert alert-success">
        <%= flash[:notice] %>
      </div>
    <% end %>
    
    <div class="alert alert-warning">
      <strong>注意:</strong> このアクションによってユーザーはサービスにアクセスできなくなります。
      本当にバンしますか？
    </div>
    
    <div class="user-info mb-4">
      <p><strong>ユーザーID:</strong> <%= @user.open_id %></p>
      <p><strong>ユーザー名:</strong> <%= @user.name %></p>
      <p><strong>現在の状態:</strong> 
        <% if @user.ban_end_at && @user.ban_end_at > Time.current %>
          <span class="badge bg-danger">バン中（<%= @user.ban_end_at.strftime('%Y-%m-%d %H:%M') %>まで）</span>
        <% else %>
          <span class="badge bg-success">アクティブ</span>
        <% end %>
      </p>
    </div>
    
    <%= form_with url: user_page_ban_update_path(@user.open_id), method: :post do |form| %>
      <div class="form-group mb-3">
        <%= form.label :ban_end_at, "バン終了日時" %>
        <%= form.datetime_field :ban_end_at, class: "form-control", value: @user.ban_end_at&.strftime('%Y-%m-%dT%H:%M') %>
      </div>
      
      <div class="form-group mb-3">
        <%= form.label :reason, "バン理由" %>
        <%= form.text_field :reason, class: "form-control" %>
      </div>
      
      <div class="form-group">
        <%= form.submit "バン状態を更新", class: "btn btn-danger", data: { confirm: "本当にこのユーザーのバン状態を更新しますか？" } %>
        <%= link_to 'キャンセル', user_page_show_path(@user.open_id), class: 'btn btn-secondary ml-2' %>
      </div>
    <% end %>
    
    <h4 class="mt-5">バン履歴</h4>
    
    <% if @ban_history.any? %>
      <table class="table table-striped mt-3">
        <thead>
          <tr>
            <th>バン理由</th>
            <th>バン終了日時</th>
            <th>設定日時</th>
          </tr>
        </thead>
        <tbody>
          <% @ban_history.each do |ban| %>
            <tr>
              <td><%= ban.reason %></td>
              <td><%= ban.ban_end_at&.strftime('%Y-%m-%d %H:%M') %></td>
              <td><%= ban.created_at.strftime('%Y-%m-%d %H:%M') %></td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <p>このユーザーのバン履歴はありません。</p>
    <% end %>
  </div>
</div>

<div class="mt-3">
  <%= link_to '戻る', user_page_show_path(@user.open_id), class: 'btn btn-primary' %>
</div>
