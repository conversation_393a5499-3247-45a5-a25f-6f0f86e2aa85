<!-- app/views/user_page/search.html.erb -->
<% content_for :page_title, "ユーザー検索" %>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザー検索</h3>
        <div class="card-tools">
            <%= link_to user_page_new_path, class: "btn btn-primary btn-sm" do %>
                <i class="fas fa-plus"></i> 新規追加
            <% end %>
        </div>
    </div>
    <div class="card-body">
        <%= form_with(url: user_page_find_path, method: :get, local: true) do |f| %>
        <div class="form-group">
            <%= f.label :query, "ユーザー名 or OpenID" %>
            <%= f.text_field :query, class: "form-control", placeholder: "入力してください", value: params[:query] %>
        </div>
        <div class="form-group">
            <%= f.submit "検索", class: "btn btn-primary btn-sm", name: nil %>
        </div>
        <% end %>

        <% if params[:query].present? %>
        <h4>検索結果</h4>
        <% if @users.any? %>
        <table class="table">
            <thead>
                <tr>
                    <th>オープンID</th>
                    <th>ユーザー名</th>
                    <th>作成順</th>
                    <th>経験値</th>
                    <th>レート</th>
                    <th>勝利数</th>
                    <th>ログイン日数</th>
                    <th>最終ログイン</th>
                    <th>作成日</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <% @users.each do |user| %>
                <tr>
                    <td><%= user.open_id %></td>
                    <td><%= user.name %></td>
                    <td><%= user.id %></td>
                    <td><%= user.exp %></td>
                    <td><%= user.rate %></td>
                    <td><%= user.wins %></td>
                    <td><%= user.login_days %></td>
                    <td><%= user.last_login_at&.strftime('%Y-%m-%d %H:%M:%S') %></td>
                    <td><%= user.created_at.strftime('%Y-%m-%d %H:%M:%S') %></td>
                    <td>
                        <%= link_to '詳細', user_page_show_path(user.open_id), class: "btn btn-info btn-sm" %>
                    </td>
                </tr>
                <% end %>
            </tbody>
        </table>
        <% else %>
        <p>該当するユーザーが見つかりませんでした。</p>
        <% end %>
        <% end %>
    </div>
</div>