<% content_for :page_title, "ユーザー管理" %>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザー追加</h3>
    </div>
    <div class="card-body">
        <%= form_with(model: @user, url: user_page_create_path, local: true) do |f| %>
        <div class="form-group">
            <%= f.label :name, "名前" %>
            <%= f.text_field :name, class: "form-control", placeholder: "名前を入力してください" %>
        </div>
        <div class="form-group">
            <%= f.submit "ユーザーを追加", class: "btn btn-success btn-sm" %>
        </div>
        <% end %>
    </div>
</div>
