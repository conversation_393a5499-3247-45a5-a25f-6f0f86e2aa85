<% content_for :page_title, "ユーザースコアの編集" %>

<%= form_tag ({controller: "user_page", action: "score_update"}) do %>
  <div class="form-group">
    <%= label_tag :user_id, "ユーザーID" %>
    <%= text_field :user_score, :user_id, value: @user.open_id , :disabled=> true %>
  </div>
  <div class="form-group">
    <%= label_tag :season, "シーズン" %>
    <%= text_field :user_score, :season, value: 1 %>
  </div>
  <div class="form-group">
    <%= label_tag :category, "カテゴリ" %>
    <%= select :user_score, :category, UserScore.options_for_select, { prompt: "選択してください" } %>
  </div>
  <div class="form-group">
    <%= label_tag :score, "スコア" %>
    <%= text_field :user_score, :score, value: 0 %>
  </div>
  <%= submit_tag "更新" %>
<% end %>