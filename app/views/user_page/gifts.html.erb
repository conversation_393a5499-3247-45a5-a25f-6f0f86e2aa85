<!-- app/views/user_page/gifts.html.erb -->
<% content_for :page_title, "#{@user.name} のギフト一覧" %>

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="card-title m-0">ギフト一覧</h3>
        <%= link_to new_gift_master_path(user_id: @user.open_id), class: 'btn btn-primary' do %>
            <i class="fas fa-plus"></i> ギフト新規作成
        <% end %>
    </div>
    <div class="card-body">
        <ul class="nav nav-tabs" id="giftTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link active" id="not-received-tab" data-toggle="tab" href="#not-received" role="tab" aria-controls="not-received" aria-selected="true">
                    未獲得のギフト <span class="badge badge-primary"><%= @not_received_gifts.count %></span>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="received-tab" data-toggle="tab" href="#received" role="tab" aria-controls="received" aria-selected="false">
                    獲得済みのギフト <span class="badge badge-success"><%= @received_gifts.count %></span>
                </a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="expired-tab" data-toggle="tab" href="#expired" role="tab" aria-controls="expired" aria-selected="false">
                    期限切れのギフト <span class="badge badge-secondary"><%= @expired_gifts.count %></span>
                </a>
            </li>
        </ul>
        <div class="tab-content" id="giftTabsContent">
            <!-- 未獲得のギフト -->
            <div class="tab-pane fade show active" id="not-received" role="tabpanel" aria-labelledby="not-received-tab">
                <% if @not_received_gifts.any? %>
                    <div class="table-responsive mt-3">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>名前</th>
                                    <th>説明</th>
                                    <th>報酬</th>
                                    <th>期限</th>
                                    <th>作成日</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% @not_received_gifts.each do |gift| %>
                                    <tr>
                                        <td><%= gift.name&.dig('ja') || '不明なギフト' %></td>
                                        <td><%= gift.desc&.dig('ja') || '詳細不明' %></td>
                                        <td>
                                            <% if gift.rewards.present? %>
                                                <ul class="list-unstyled mb-0">
                                                    <% gift.rewards.each do |reward| %>
                                                        <li><%= "#{reward['name'] || '不明'}: #{reward['value'] || reward['count'] || 0}" %></li>
                                                    <% end %>
                                                </ul>
                                            <% else %>
                                                <span class="text-muted">報酬なし</span>
                                            <% end %>
                                        </td>
                                        <td><%= gift.end_at&.strftime('%Y-%m-%d %H:%M') || '無期限' %></td>
                                        <td><%= gift.created_at.strftime('%Y-%m-%d %H:%M') %></td>
                                    </tr>
                                <% end %>
                            </tbody>
                        </table>
                    </div>
                <% else %>
                    <div class="alert alert-info mt-3">
                        未獲得のギフトはありません。
                    </div>
                <% end %>
            </div>
            
            <!-- 獲得済みのギフト -->
            <div class="tab-pane fade" id="received" role="tabpanel" aria-labelledby="received-tab">
                <% if @received_gifts.any? %>
                    <div class="table-responsive mt-3">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>名前</th>
                                    <th>説明</th>
                                    <th>報酬</th>
                                    <th>獲得日時</th>
                                    <th>作成日</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% @received_gifts.each do |gift| %>
                                    <tr>
                                        <td><%= gift.name&.dig('ja') || '不明なギフト' %></td>
                                        <td><%= gift.desc&.dig('ja') || '詳細不明' %></td>
                                        <td>
                                            <% if gift.rewards.present? %>
                                                <ul class="list-unstyled mb-0">
                                                    <% gift.rewards.each do |reward| %>
                                                        <li><%= "#{reward['name'] || '不明'}: #{reward['value'] || reward['count'] || 0}" %></li>
                                                    <% end %>
                                                </ul>
                                            <% else %>
                                                <span class="text-muted">報酬なし</span>
                                            <% end %>
                                        </td>
                                        <td><%= gift.open_at&.strftime('%Y-%m-%d %H:%M') || '-' %></td>
                                        <td><%= gift.created_at.strftime('%Y-%m-%d %H:%M') %></td>
                                    </tr>
                                <% end %>
                            </tbody>
                        </table>
                    </div>
                <% else %>
                    <div class="alert alert-info mt-3">
                        獲得済みのギフトはありません。
                    </div>
                <% end %>
            </div>
            
            <!-- 期限切れのギフト -->
            <div class="tab-pane fade" id="expired" role="tabpanel" aria-labelledby="expired-tab">
                <% if @expired_gifts.any? %>
                    <div class="table-responsive mt-3">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>名前</th>
                                    <th>説明</th>
                                    <th>報酬</th>
                                    <th>状態</th>
                                    <th>期限切れ日時</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% @expired_gifts.each do |gift| %>
                                    <tr>
                                        <td><%= gift.name&.dig('ja') || '不明なギフト' %></td>
                                        <td><%= gift.desc&.dig('ja') || '詳細不明' %></td>
                                        <td>
                                            <% if gift.rewards.present? %>
                                                <ul class="list-unstyled mb-0">
                                                    <% gift.rewards.each do |reward| %>
                                                        <li><%= "#{reward['name'] || '不明'}: #{reward['value'] || reward['count'] || 0}" %></li>
                                                    <% end %>
                                                </ul>
                                            <% else %>
                                                <span class="text-muted">報酬なし</span>
                                            <% end %>
                                        </td>
                                        <td>
                                            <% if gift.state == 1 %>
                                                <span class="badge badge-success">獲得済み</span>
                                            <% else %>
                                                <span class="badge badge-danger">未獲得</span>
                                            <% end %>
                                        </td>
                                        <td><%= gift.end_at&.strftime('%Y-%m-%d %H:%M') || '-' %></td>
                                    </tr>
                                <% end %>
                            </tbody>
                        </table>
                    </div>
                <% else %>
                    <div class="alert alert-info mt-3">
                        期限切れのギフトはありません。
                    </div>
                <% end %>
            </div>
        </div>
        
        <div class="mt-3">
            <%= link_to '戻る', user_page_show_path(@user.open_id), class: 'btn btn-secondary' %>
        </div>
    </div>
</div> 