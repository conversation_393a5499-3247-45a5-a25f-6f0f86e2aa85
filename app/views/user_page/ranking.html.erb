<p style="color: green"><%= notice %></p>

<h1 class="mb-6">ユーザーランキング</h1>

<div class="ml-3">
  <%= form_tag user_page_ranking_path, method: :get, class: 'form-inline' do %>
    <div class="form-group">
      <label class="mr-2">カテゴリー:</label>
      <%= select_tag :category,
          options_for_select(UserScore.options_for_select, params[:category]),
          class: 'form-control',
          onchange: 'this.form.submit()' %>
    </div>
  <% end %>
</div>
<%= render partial: "shared/table", locals: { 
    items: @ranking_users, 
    columns: ["ID", "ユーザー名", "スコア"], 
    values: [
      ->(item) { item[:open_id] },
      ->(item) { item[:name] },
      ->(item) { item[:score] }
    ],
    model_name: "ユーザーランキング",
    edit_path:->(item) { user_page_show_path(item[:open_id]) },
    delete_path: nil,
    new_path: nil
  } %>