<p style="color: green"><%= notice %></p>

<h1 class="mb-6">ユーザーランキング</h1>

<div class="ml-3">
  <!-- カテゴリータブ -->
  <ul class="nav nav-tabs" id="rankingTabs" role="tablist">
    <% @categories.each_with_index do |category, index| %>
      <li class="nav-item" role="presentation">
        <a class="nav-link <%= 'active' if index == 0 %>" 
           id="<%= category %>-tab" 
           data-toggle="tab" 
           href="#<%= category %>" 
           role="tab" 
           aria-controls="<%= category %>" 
           aria-selected="<%= index == 0 ? 'true' : 'false' %>">
          <%= UserScore::CATEGORY_TYPES.find { |type| type[:value] == category.to_s }&.dig(:name) || category.to_s.capitalize %>
        </a>
      </li>
    <% end %>
  </ul>

  <!-- タブコンテンツ -->
  <div class="tab-content" id="rankingTabContent">
    <% @categories.each_with_index do |category, index| %>
      <div class="tab-pane fade <%= 'show active' if index == 0 %>" 
           id="<%= category %>" 
           role="tabpanel" 
           aria-labelledby="<%= category %>-tab">
        <div class="mt-3">
          <%= render partial: "shared/table", locals: { 
              items: @ranking_users[category][:other_users], 
              columns: ["順位", "ID", "ユーザー名", "スコア"], 
              values: [
                ->(item) { item[:rank] },
                ->(item) { item[:open_id] },
                ->(item) { item[:name] },
                ->(item) { item[:score] }
              ],
              model_name: "#{UserScore::CATEGORY_TYPES.find { |type| type[:value] == category.to_s }&.dig(:name) || category.to_s.capitalize}ランキング",
              edit_path:->(item) { user_page_show_path(item[:open_id]) },
              delete_path: nil,
              new_path: nil
            } %>
        </div>
      </div>
    <% end %>
  </div>
</div>