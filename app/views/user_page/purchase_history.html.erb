<!-- app/views/user_page/purchase_history.html.erb -->
<% content_for :page_title, "#{@user.name} の課金履歴" %>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">課金履歴 - <%= @user.name %> (<%= @user.open_id %>)</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <%= link_to "← ユーザー詳細に戻る", user_page_show_path(@user.open_id), class: "btn btn-secondary" %>
            </div>
            <div class="col-md-6 text-right">
                <h4 class="text-primary">
                    <strong>合計課金額: ¥<%= number_with_delimiter(@purchase_data[:total_purchase_amount]) %></strong>
                </h4>
            </div>
        </div>
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h4 class="card-title">購入アイテム詳細</h4>
    </div>
    <div class="card-body">
        <% if @purchase_data[:purchase_items].empty? %>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 課金履歴がありません。
            </div>
        <% else %>
            <table class="table table-bordered table-striped">
                <thead class="thead-dark">
                    <tr>
                        <th style="width: 25%">商品キー</th>
                        <th style="width: 15%">購入数</th>
                        <th style="width: 20%">単価</th>
                        <th style="width: 20%">小計</th>
                        <th style="width: 20%">商品説明</th>
                    </tr>
                </thead>
                <tbody>
                    <% @purchase_data[:purchase_items].sort_by { |item| -item[:total_price] }.each do |item| %>
                        <tr>
                            <td><code><%= item[:product_key] %></code></td>
                            <td class="text-center">
                                <span class="badge badge-primary"><%= item[:count] %>個</span>
                            </td>
                            <td class="text-right">¥<%= number_with_delimiter(item[:unit_price]) %></td>
                            <td class="text-right">
                                <strong>¥<%= number_with_delimiter(item[:total_price]) %></strong>
                            </td>
                            <td>
                                <%= item[:product_key] %>
                            </td>
                        </tr>
                    <% end %>
                </tbody>
                <tfoot class="thead-light">
                    <tr>
                        <th colspan="3">合計</th>
                        <th class="text-right">
                            <strong class="text-primary">¥<%= number_with_delimiter(@purchase_data[:total_purchase_amount]) %></strong>
                        </th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        <% end %>
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h4 class="card-title">プラットフォーム別課金情報</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h5>iOS課金履歴</h5>
                <% ios_count = @user.iap_ios.count %>
                <% if ios_count > 0 %>
                    <p>総課金回数: <strong><%= ios_count %>回</strong></p>
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>購入日</th>
                                <th>数量</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% @user.iap_ios.order(buy_date: :desc).limit(10).each do |ios_purchase| %>
                                <tr>
                                    <td><code><%= ios_purchase.product %></code></td>
                                    <td><%= ios_purchase.buy_date&.strftime('%Y-%m-%d') %></td>
                                    <td><%= ios_purchase.quantity || 1 %></td>
                                </tr>
                            <% end %>
                        </tbody>
                    </table>
                    <% if ios_count > 10 %>
                        <p class="text-muted"><small>※最新10件を表示</small></p>
                    <% end %>
                <% else %>
                    <p class="text-muted">iOS課金履歴がありません。</p>
                <% end %>
            </div>
            <div class="col-md-6">
                <h5>Android課金履歴</h5>
                <% android_count = @user.iap_and.count %>
                <% if android_count > 0 %>
                    <p>総課金回数: <strong><%= android_count %>回</strong></p>
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>購入日</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% @user.iap_and.order(buy_date: :desc).limit(10).each do |android_purchase| %>
                                <tr>
                                    <td><code><%= android_purchase.product %></code></td>
                                    <td><%= android_purchase.buy_date&.strftime('%Y-%m-%d') %></td>
                                </tr>
                            <% end %>
                        </tbody>
                    </table>
                    <% if android_count > 10 %>
                        <p class="text-muted"><small>※最新10件を表示</small></p>
                    <% end %>
                <% else %>
                    <p class="text-muted">Android課金履歴がありません。</p>
                <% end %>
            </div>
        </div>
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h4 class="card-title">購入履歴詳細</h4>
    </div>
    <div class="card-body">
        <% if @purchase_data[:purchase_details].empty? %>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> 課金履歴がありません。
            </div>
        <% else %>
            <table class="table table-bordered table-striped">
                <thead class="thead-dark">
                    <tr>
                        <th style="width: 20%">購入日時</th>
                        <th style="width: 20%">商品キー</th>
                        <th style="width: 10%">数量</th>
                        <th style="width: 15%">単価</th>
                        <th style="width: 15%">金額</th>
                        <th style="width: 10%">プラットフォーム</th>
                    </tr>
                </thead>
                <tbody>
                    <% @purchase_data[:purchase_details].each do |detail| %>
                        <tr>
                            <td>
                                <% if detail[:buy_date] %>
                                    <%= detail[:buy_date].strftime('%Y-%m-%d %H:%M') %>
                                <% else %>
                                    <span class="text-muted">不明</span>
                                <% end %>
                            </td>
                            <td><code><%= detail[:product_key] %></code></td>
                            <td class="text-center"><%= detail[:quantity] %></td>
                            <td class="text-right">¥<%= number_with_delimiter(detail[:unit_price]) %></td>
                            <td class="text-right"><strong>¥<%= number_with_delimiter(detail[:total_price]) %></strong></td>
                            <td>
                                <span class="badge <%= detail[:platform] == 'iOS' ? 'badge-info' : 'badge-success' %>">
                                    <%= detail[:platform] %>
                                </span>
                            </td>
                        </tr>
                    <% end %>
                </tbody>
            </table>
        <% end %>
    </div>
</div> 