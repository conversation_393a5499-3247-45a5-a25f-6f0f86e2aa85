<!-- app/views/user_page/box.html.erb -->
<% content_for :page_title, "#{@user.name} のボックス" %>

<div class="card" x-data="{
    cards: <%= @user.box.map { |card_id, card_data| 
        card = MasterDatum.get_card(card_id.to_i)
        {
            id: card_id.to_i,
            name: card.present? ? card['Name'] : '不明なカード',
            normal: card_data['nr'] || 0,
            shine: card_data['sh'] || 0,
            premium: card_data['pr'] || 0,
            rarity: card.present? ? card['Rarity'] : 0,
            role: card.present? ? card['Role'] : 0,
            total: (card_data['nr'] || 0) + (card_data['sh'] || 0) + (card_data['pr'] || 0)
        }
    }.to_json %>,
    sortKey: 'id',
    sortDir: 'asc',
    sortBy(key) {
        if (this.sortKey === key) {
            this.sortDir = this.sortDir === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortKey = key;
            this.sortDir = 'asc';
        }
        
        this.cards.sort((a, b) => {
            if (this.sortDir === 'asc') {
                return a[this.sortKey] > b[this.sortKey] ? 1 : -1;
            } else {
                return a[this.sortKey] < b[this.sortKey] ? 1 : -1;
            }
        });
    },
    getSortIcon(key) {
        if (this.sortKey === key) {
            return this.sortDir === 'asc' ? '↑' : '↓';
        }
        return '';
    },
    getRarityName(rarity) {
        const rarityMap = {
            1: 'ブロンズ',
            2: 'シルバー', 
            3: 'ゴールド', 
            4: 'レジェンド'
        };
        return rarityMap[rarity] || '不明';
    },
    getRoleName(role) {
        const roleMap = {
            'a': 'アタッカー', 
            'd': 'ディフェンダー', 
            'c': 'チャージャー'
        };
        return roleMap[role] || role;
    },
    getRaritySummary() {
        const summary = {
            1: { normal: 0, shine: 0, premium: 0 },
            2: { normal: 0, shine: 0, premium: 0 },
            3: { normal: 0, shine: 0, premium: 0 },
            4: { normal: 0, shine: 0, premium: 0 },
            total: { normal: 0, shine: 0, premium: 0 }
        };
        
        this.cards.forEach(card => {
            // レアリティごとに集計
            if (summary[card.rarity]) {
                summary[card.rarity].normal += card.normal;
                summary[card.rarity].shine += card.shine;
                summary[card.rarity].premium += card.premium;
            }
            
            // 全体の集計
            summary.total.normal += card.normal;
            summary.total.shine += card.shine;
            summary.total.premium += card.premium;
        });
        
        return summary;
    }
}">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h3 class="card-title">ボックス</h3>
        <div>
            <%= link_to 'ボックス編集', '#', class: "btn btn-primary", data: { toggle: "modal", target: "#box_edit_modal" } %>
            <%= link_to 'ユーザー情報に戻る', user_page_show_path(@user.open_id), class: "btn btn-secondary" %>
        </div>
    </div>
    <div class="card-body">
        <% if @user.box.present? %>
            <!-- レアリティ別集計テーブル -->
            <div class="mb-4">
                <h4>レアリティ別カード枚数</h4>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr class="table-primary">
                                <th>レアリティ</th>
                                <th>通常</th>
                                <th>シャイン</th>
                                <th>プレミアム</th>
                                <th class="table-info">合計</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="rarity in [4, 3, 2, 1]" :key="rarity">
                                <tr>
                                    <td x-text="getRarityName(rarity)"></td>
                                    <td x-text="getRaritySummary()[rarity].normal"></td>
                                    <td x-text="getRaritySummary()[rarity].shine"></td>
                                    <td x-text="getRaritySummary()[rarity].premium"></td>
                                    <td class="table-info" x-text="getRaritySummary()[rarity].normal + getRaritySummary()[rarity].shine + getRaritySummary()[rarity].premium"></td>
                                </tr>
                            </template>
                            <tr>
                                <td class="font-weight-bold">合計</td>
                                <td x-text="getRaritySummary().total.normal"></td>
                                <td x-text="getRaritySummary().total.shine"></td>
                                <td x-text="getRaritySummary().total.premium"></td>
                                <td x-text="getRaritySummary().total.normal + getRaritySummary().total.shine + getRaritySummary().total.premium"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mb-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary" x-on:click="sortBy('id')">
                        ID順 <span x-text="getSortIcon('id')"></span>
                    </button>
                    <button class="btn btn-outline-secondary" x-on:click="sortBy('name')">
                        名前順 <span x-text="getSortIcon('name')"></span>
                    </button>
                    <button class="btn btn-outline-secondary" x-on:click="sortBy('rarity')">
                        レアリティ順 <span x-text="getSortIcon('rarity')"></span>
                    </button>
                    <button class="btn btn-outline-secondary" x-on:click="sortBy('role')">
                        役割順 <span x-text="getSortIcon('role')"></span>
                    </button>
                    <button class="btn btn-outline-secondary" x-on:click="sortBy('total')">
                        枚数順 <span x-text="getSortIcon('total')"></span>
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="cursor: pointer" x-on:click="sortBy('id')">
                                カードID <span x-text="getSortIcon('id')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('name')">
                                カード名 <span x-text="getSortIcon('name')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('rarity')">
                                レアリティ <span x-text="getSortIcon('rarity')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('role')">
                                役割 <span x-text="getSortIcon('role')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('normal')">
                                通常 <span x-text="getSortIcon('normal')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('shine')">
                                シャイン <span x-text="getSortIcon('shine')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('premium')">
                                プレミアム <span x-text="getSortIcon('premium')"></span>
                            </th>
                            <th style="cursor: pointer" x-on:click="sortBy('total')">
                                合計 <span x-text="getSortIcon('total')"></span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="card in cards" :key="card.id">
                            <tr>
                                <td x-text="card.id"></td>
                                <td x-text="card.name"></td>
                                <td x-text="getRarityName(card.rarity)"></td>
                                <td x-text="getRoleName(card.role)"></td>
                                <td x-text="card.normal"></td>
                                <td x-text="card.shine"></td>
                                <td x-text="card.premium"></td>
                                <td x-text="card.total"></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        <% else %>
            <p class="alert alert-info">ボックスにカードはありません</p>
        <% end %>
    </div>
</div>

<!-- ボックス編集モーダル -->
<div class="modal fade" id="box_edit_modal" tabindex="-1" role="dialog" aria-labelledby="boxEditModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="boxEditModalLabel">ボックス編集</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <%= form_with url: user_page_box_update_path(@user.open_id), method: :patch, local: true do |f| %>
                    <div class="form-group">
                        <%= f.label :card_id, 'カードID' %>
                        <%= f.select :card_id, options_for_select(MasterDatum.get_cards.map { |c| ["#{c['ID']} - #{c['Name']}", c['ID']] }), {}, class: 'form-control card-select' %>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <%= f.label :nr, '通常枚数' %>
                            <%= f.number_field :nr, class: 'form-control', min: 0 %>
                        </div>
                        <div class="form-group col-md-4">
                            <%= f.label :sh, 'シャイン枚数' %>
                            <%= f.number_field :sh, class: 'form-control', min: 0 %>
                        </div>
                        <div class="form-group col-md-4">
                            <%= f.label :pr, 'プレミアム枚数' %>
                            <%= f.number_field :pr, class: 'form-control', min: 0 %>
                        </div>
                    </div>
                    <div class="mt-3">
                        <%= f.submit 'カード更新', class: 'btn btn-primary' %>
                    </div>
                <% end %>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">閉じる</button>
            </div>
        </div>
    </div>
</div> 