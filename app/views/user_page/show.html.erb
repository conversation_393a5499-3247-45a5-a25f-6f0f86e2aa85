<!-- app/views/user_page/show.html.erb -->
<% content_for :page_title, "#{@user.name} の情報" %>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザー情報</h3>
    </div>
    <div class="card-body">
        <p><strong>ID:</strong> <%= @user.open_id %></p>
        <p><strong>名前:</strong> <%= @user.name %></p>
        <p><strong>ログイン日数:</strong> <%= @user.login_days %></p>
        <p><strong>登録日:</strong> <%= @user.created_at.strftime('%Y-%m-%d %H:%M:%S') %></p>
        <p><strong>現在の状態:</strong> 
        <% if @user.ban_end_at && @user.ban_end_at > Time.current %>
            <span class="badge bg-danger">バン中（<%= @user.ban_end_at.strftime('%Y-%m-%d %H:%M') %>まで）</span>
        <% else %>
            <span class="badge bg-success">アクティブ</span>
        <% end %>
        </p>
        <p><strong>サブスク状況:</strong><br>
            pack_pass:<%= @user.user_save_data.dig("shop", "passes", "pack_pass") %><br>
            analysis_pass:<%= @user.user_save_data.dig("shop", "passes", "analysis_pass") %><br>
        </p>
        <p><strong>アカウント状況:</strong><br>
            <%= @user.enable_account ? "有効" : "無効" %>
        </p>
        <p><strong>引継ぎコード:</strong><br>
            <%= @user.user_save_data.dig("transfer_code", "code") %><br>
            <%= @user.user_save_data.dig("transfer_code", "limit_date") %>まで
        </p>
        <div class="mt-3 d-flex justify-content-between">
            <div>
                <%= link_to '編集', user_page_edit_path(@user.open_id), method: :get, class: "btn btn-primary" %>
                <%= link_to 'ユーザーデータ表示', '#', class: "btn btn-primary", data: { toggle: "modal", target: "#model_modal" } %>
                <%= link_to 'ボックス', user_page_box_path(@user.open_id), class: "btn btn-info" %>
                <%= link_to '所持ギフト一覧', user_page_gifts_path(@user.open_id), class: "btn btn-info" %>
            </div>
            <div>
                <%= button_to '引継ぎコード生成', user_page_generate_transfer_code_path(@user.open_id), method: :get, class: "btn btn-warning", data: { confirm: "引継ぎコードを生成しますか？", disable_with: "生成中..." } %>
                <%= link_to 'ユーザーバン', user_page_ban_path(@user.open_id), method: :get, class: "btn btn-warning" %>
                <%
=begin%>
 <% if @user.enable_account %>
                    <%= button_to 'アカウント無効化', user_page_enable_account_update_path(@user.open_id), params: { enable_account: false }, method: :post, class: "btn btn-secondary btn-sm", data: { confirm: "本当にアカウントを無効化しますか？" } %>
                <% else %>
                    <%= button_to 'アカウント有効化', user_page_enable_account_update_path(@user.open_id), params: { enable_account: true }, method: :post, class: "btn btn-success btn-sm", data: { confirm: "アカウントを有効化しますか？" } %>
                <% end %> 
<%
=end%>
            </div>
        </div>
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h3 class="card-title">ダイヤ情報</h3>
    </div>
    <div class="card-body">
        <table class="table table-sm">
            <tr>
                <th>無料ダイヤ</th>
                <td class="text-right"><strong><%= @user.free_dia %></strong></td>
            </tr>
            <tr>
                <th>iOSダイヤ</th>
                <td class="text-right"><%= @user.ios_dia %></td>
            </tr>
            <tr>
                <th>Androidダイヤ</th>
                <td class="text-right"><%= @user.android_dia %></td>
            </tr>
            <tr>
                <th>Steamダイヤ</th>
                <td class="text-right"><%= @user.steam_dia %></td>
            </tr>
            <tr class="table-light">
                <th>有償ダイヤ合計</th>
                <td class="text-right text-primary"><strong><%= @user.ios_dia + @user.android_dia + @user.steam_dia %></strong></td>
            </tr>
            <tr class="table-info">
                <th>ダイヤ総合計</th>
                <td class="text-right"><strong><%= @user.free_dia + @user.ios_dia + @user.android_dia + @user.steam_dia %></strong></td>
            </tr>
        </table>
    </div>
</div>

<%= render partial: "shared/model_modal", locals: { title: "ユーザーデータ表示", content: @user.attributes } %>
<%= render partial: "shared/selection_modal", locals: { title: "ユーザー削除", content: "本当に削除しますか？", action: user_page_edit_path(@user.open_id) } %>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザースコア</h3>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th style="width: 10%">シーズン</th>
                    <th style="width: 10%">カテゴリー</th>
                    <th style="width: 40%">スコア</th>
                </tr>
            </thead>
            <tbody>
                <% @user_score.each do |score| %>
                    <tr>
                        <td><%= score.season %></td>
                        <td><%= score.user_score_name %></td>
                        <td><%= score.score %></td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= link_to '編集', user_page_score_edit_path(@user.open_id), method: :get, class: "btn btn-primary" %>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3 class="card-title">ユーザー行動ログ</h3>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th style="width: 10%">アクション</th>
                    <th style="width: 40%">生データ</th>
                    <th style="width: 30%">データ</th>
                    <th style="width: 20%">日時</th>
                </tr>
            </thead>
            <tbody>
                <% @user_logs.each do |log| %>
                    <tr>
                        <td><i class="<%= log.value[:icon] %>"></i> <%= log.value[:name] %></td>
                        <td><%= log.data %></td>
                        <td><%= show_log_data(log) %></td>
                        <td><%= log.created_at.strftime('%Y-%m-%d %H:%M:%S') %>  (<%= time_ago_in_words(log.created_at) %>前)</td>
                    </tr>
                <% end %>
            </tbody>
        </table>
        <%= paginate @user_logs %>
    </div>
</div>
