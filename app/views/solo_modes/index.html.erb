<h1>Solo Modes</h1>

<table class="table">
  <thead>
    <tr>
      <th>UID</th>
      <th>Level</th>
      <th>Title</th>
      <th>Actions</th>
    </tr>
  </thead>

  <tbody>
    <% @solo_modes.each do |solo_mode| %>
      <tr>
        <td><%= solo_mode.uid %></td>
        <td><%= solo_mode.level %></td>
        <td><%= solo_mode.title %></td>
        <td>
          <%= link_to 'Show', solo_mode_path(solo_mode.uid), class: 'btn btn-info btn-sm' %>
          <%= link_to 'Edit', edit_solo_mode_path(solo_mode.uid), class: 'btn btn-warning btn-sm' %>
          <%= link_to 'Delete', solo_mode_path(solo_mode.uid), data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" }, class: 'btn btn-danger btn-sm' %>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>

<%= link_to 'New Solo Mode', new_solo_mode_path, class: 'btn btn-primary' %> 