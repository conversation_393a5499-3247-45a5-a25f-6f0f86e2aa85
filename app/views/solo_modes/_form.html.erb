<%= form_with(model: solo_mode, url: solo_mode.persisted? ? solo_mode_path(solo_mode.uid) : solo_modes_path, local: true) do |f| %>
  <% if solo_mode.errors.any? %>
    <div class="alert alert-danger">
      <h4><%= pluralize(solo_mode.errors.count, "error") %> prohibited this solo_mode from being saved:</h4>
      <ul>
        <% solo_mode.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-group mb-3">
    <%= f.label :uid, 'UID', class: 'form-label' %>
    <%= f.number_field :uid, class: 'form-control' %>
  </div>

  <div class="form-group mb-3">
    <%= f.label :level, 'レベル', class: 'form-label' %>
    <%= f.number_field :level, class: 'form-control' %>
  </div>

  <div class="form-group mb-3">
    <%= f.label :missions, 'ミッション', class: 'form-label' %>
    <%= f.text_area :missions, class: 'form-control', placeholder: '{"mission1": "value1", "mission2": "value2"}' %>
  </div>

  <div class="form-group mb-3">
    <%= f.label :title, 'タイトル', class: 'form-label' %>
    <%= f.text_area :title, class: 'form-control', placeholder: '{"ja": "日本語タイトル", "en": "English Title"}' %>
  </div>

  <div class="form-group mb-3">
    <%= f.label :enemy_decks, '敵デッキ', class: 'form-label' %>
    <%= f.text_area :enemy_decks, class: 'form-control', placeholder: '{"deck1": "value1", "deck2": "value2"}' %>
  </div>

  <div class="actions">
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>
<% end %> 