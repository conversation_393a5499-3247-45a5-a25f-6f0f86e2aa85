<h1>パック一覧</h1>
<%= link_to "新しいパックを作成", new_pack_path, class: "btn btn-primary mb-3" %>

<table class="table">
  <thead>
    <tr>
      <th>名前</th>
      <th>UID</th>
      <th>カード内容</th>
      <th>販売期間</th>
      <th>操作</th>
    </tr>
  </thead>
  <tbody>
    <% @packs.each do |pack| %>
      <tr>
        <td><%= json_lang(pack.name) %></td>
        <td><%= pack.uid %></td>
        <td><%= pack.card_data&.length || 0 %></td>
        <td><%= pack.start_at %> ~ <%= pack.end_at %></td>
        <td>
          <%= link_to "編集", edit_pack_path(pack.uid), class: "btn btn-warning" %>
          <%= form_with model: pack, method: :delete, url: pack_path(pack.uid), local: true do |f| %>
            <%= f.submit "削除", data: { turbo_method: :delete, turbo_confirm: "本当に削除しますか？" },class: "btn btn-danger" %>
          <% end %>
        </td>
      </tr>
    <% end %>
  </tbody>
</table>

<%= render 'shared/card_select' %>
