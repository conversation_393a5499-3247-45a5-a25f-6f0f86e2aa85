<div class="form-group">
  <%= f.label :uid, "UID" %>
  <%= f.number_field :uid, class: "form-control" %>
</div>

<div class="form-group">
  <%= f.label :sort_order, "表示順" %>
  <%= f.number_field :sort_order, class: "form-control" %>
</div>

<div class="form-group">
  <%= json_lang_form(f, :name, label: "パック名") %>
  <%= json_lang_form(f, :desc, label: "パック説明") %>
</div>


<div class="card p-3 mb-4">
  <%= render 'shared/period', form: f %>
</div>

<div class="card p-3 mb-4">
  <h5>レアリティ排出確率</h5>
  <%= f.fields_for :rarity_prob, pack.rarity_prob || {} do |rp| %>
    <div class="d-flex flex-wrap">
      <div class="d-flex align-items-center me-4 mb-2">
        <strong class="me-2">レジェンド</strong>
        <label class="me-2 mb-0">確率(%)</label>
        <%= rp.number_field :legend4, value: pack.rarity_prob&.dig("legend4") || 2, step: 0.01, class: "form-control w-auto" %>
      </div>
      <div class="d-flex align-items-center me-4 mb-2">
        <strong class="me-2">ゴールド</strong>
        <label class="me-2 mb-0">確率(%)</label>
        <%= rp.number_field :gold3, value: pack.rarity_prob&.dig("gold3") || 8, step: 0.01, class: "form-control w-auto" %>
      </div>
      <div class="d-flex align-items-center me-4 mb-2">
        <strong class="me-2">シルバー</strong>
        <label class="me-2 mb-0">確率(%)</label>
        <%= rp.number_field :silver2, value: pack.rarity_prob&.dig("silver2") || 35, step: 0.01, class: "form-control w-auto" %>
      </div>
      <div class="d-flex align-items-center me-4 mb-2">
        <strong class="me-2">ブロンズ</strong>
        <label class="me-2 mb-0">確率(%)</label>
        <%= rp.number_field :bronze1, value: pack.rarity_prob&.dig("bronze1") || 55, step: 0.01, class: "form-control w-auto" %>
      </div>
    </div>
  <% end %>

  <h5>Edition排出率</h5>
  <%= f.fields_for :edition_prob, pack.edition_prob || {} do |ep| %>
  <div class="d-flex flex-wrap">
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">Normal</strong>
      <label class="me-2 mb-0">確率(%)</label>
      <%= ep.number_field "normal", value: pack.edition_prob&.dig("normal") || 94, step: 0.01, class: "form-control w-auto" %>
    </div>
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">Shine</strong>
      <label class="me-2 mb-0">確率(%)</label>
      <%= ep.number_field "shine", value: pack.edition_prob&.dig("shine") || 5, step: 0.01, class: "form-control w-auto" %>
    </div>
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">Premium</strong>
      <label class="me-2 mb-0">確率(%)</label>
        <%= ep.number_field "premium", value: pack.edition_prob&.dig("premium") || 1, step: 0.01, class: "form-control w-auto" %>
      </div>
    </div>
  <% end %>

  <h5>交換ポイント</h5>
  <%= f.fields_for :change_pts, pack.change_pts || {} do |cp| %>
  <div class="d-flex flex-wrap">
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">レジェンド</strong>
      <label class="me-2 mb-0">ポイント</label>
      <%= cp.number_field "legend4", value: pack.change_pts&.dig("legend4") || 400, class: "form-control w-auto" %>
    </div>
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">ゴールド</strong>
      <label class="me-2 mb-0">ポイント</label>
      <%= cp.number_field "gold3", value: pack.change_pts&.dig("gold3") || 300, class: "form-control w-auto" %>
    </div>
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">シルバー</strong>
      <label class="me-2 mb-0">ポイント</label>
      <%= cp.number_field "silver2", value: pack.change_pts&.dig("silver2") || 200, class: "form-control w-auto" %>
    </div>
    <div class="d-flex align-items-center me-4 mb-2">
      <strong class="me-2">ブロンズ</strong>
      <label class="me-2 mb-0">ポイント</label>
      <%= cp.number_field "bronze1", value: pack.change_pts&.dig("bronze1") || 100, class: "form-control w-auto" %>
    </div>
  </div>
  <% end %>

  <div class="d-flex flex-wrap align-items-center mt-3">
    <div class="d-flex align-items-center me-4 mb-2">
      <label class="me-2 mb-0">1パックに含まれるカード数：</label>
      <%= f.number_field :cards_per_pack, class: "form-control w-auto" %>
    </div>

    <div class="d-flex align-items-center me-4 mb-2">
      <label class="me-2 mb-0">1パックあたりのダイヤ消費量：</label>
      <%= f.number_field :dia_cost, class: "form-control w-auto" %>
    </div>

    <div class="d-flex align-items-center mb-2">
      <label class="me-2 mb-0">1パックごとのスキン排出確率(%)</label>
      <%= f.number_field :per_skin_prob, step: 0.001, class: "form-control w-auto" %>
    </div>
  </div>
  
  <div class="d-flex align-items-center mb-2">
    <label class="me-2 mb-0">メインカードのID：</label>
    <%= f.number_field :main_card_id, class: "form-control w-auto" %>
  </div>
  
  <h5 class="mt-4">設定オプション</h5>
  <div class="card p-3 mb-3">
    <div class="d-flex flex-wrap">
      <div class="form-check form-switch me-5 mb-2">
        <%= f.check_box :is_special, class: "form-check-input", id: "is_special_switch" %>
        <label class="form-check-label" for="is_special_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にすると交換ポイントがたまりません">特殊パック</label>
      </div>

      <div class="form-check form-switch me-5 mb-2">
        <%= f.check_box :is_visible, class: "form-check-input", id: "is_visible_switch" %>
        <label class="form-check-label" for="is_visible_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にするとパックが無条件で表示されます。無効にするとダイヤ購入も有効でなくチケットを持っていない人がいるときに表示しないようにします">表示する</label>
      </div>
      
      <div class="form-check form-switch me-5 mb-2">
        <%= f.check_box :enable_dia, class: "form-check-input", id: "enable_dia_switch" %>
        <label class="form-check-label" for="enable_dia_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にするとダイヤでの購入が可能になります。">ダイヤ購入を有効</label>
      </div>
      
      <div class="form-check form-switch me-5 mb-2">
        <%= f.check_box :enable_ticket, class: "form-check-input", id: "enable_ticket_switch" %>
        <label class="form-check-label" for="enable_ticket_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にするとチケットでの購入が可能になります。">チケット購入を有効</label>
      </div>

      <div class="form-check form-switch mb-2">
        <%= f.check_box :is_main, class: "form-check-input", id: "is_main_switch" %>
        <label class="form-check-label" for="is_main_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にするとこのパックをメインパックとして扱います。">メインパック</label>
      </div>

      <div class="form-check form-switch mb-2">
        <%= f.check_box :enable_pts, class: "form-check-input", id: "enable_pts_switch" %>
        <label class="form-check-label" for="enable_pts_switch" data-bs-toggle="tooltip" data-bs-placement="top" title="有効にすると交換ポイントがたまります。">交換ポイントを有効</label>
      </div>
    </div>
  </div>
</div>

<!-- ツールチップを初期化するためのJavaScript -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Bootstrapのツールチップを初期化
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  });
</script>
