<h1>パック作成</h1>

<% if @pack.errors.any? %>
  <div class="alert alert-danger">
    <ul>
      <% @pack.errors.full_messages.each do |msg| %>
        <li><%= msg %></li>
      <% end %>
    </ul>
  </div>
<% end %>

<%= form_with model: @pack, local: true, html: { id: "pack-form", multipart: true } do |f| %>
  <!-- レアリティ排出確率などの基本情報フォーム -->
  <div class="card p-3 mb-4">
    <%= render 'form', pack: @pack, f: f %>
  </div>
  <!-- 保存ボタン -->
  <div class="d-flex justify-content-between mt-4">
    <%= link_to "戻る", packs_path, class: "btn btn-secondary" %>
    <%= f.submit "保存", class: "btn btn-primary" %>
  </div>

  <!-- カードデータ追加フォーム -->
  <%= render 'card_form', pack: @pack, f: f%>
  
  <!-- スキンデータ追加フォーム -->
  <%= render 'skin_form', pack: @pack %>

<% end %>