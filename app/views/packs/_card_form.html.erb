<div class="d-flex gap-4">
    <!-- 左: カード一覧 -->
    <div class="w-50">
      <div class="card p-3 mb-4">
        <h3 class="mb-3">カード一覧 (Shiftで複数選択可能)</h3>
        <ul class="list-group" id="cardList">
          <% @all_cards.each do |card| %>
            <li class="list-group-item">
              <% all_card_id = card["ID"].to_s %>
              <% checked_cards = @checked_cards %>
              <label class="d-flex align-items-center w-100 mb-0" style="cursor: pointer;">
                <% if checked_cards.include?(all_card_id) %>
                  <%= check_box_tag "card_data[]", card["ID"], true, class: "me-2 card-checkbox" %>
                <% else %>
                  <%= check_box_tag "card_data[]", card["ID"], class: "me-2 card-checkbox" %>
                <% end %>
                <div>
                  <strong><%= card["ID"] %> <%= card["Name"] %></strong>
                  <span class="text-muted ms-2">
                    <%= {"C"=>"チャージャー","A"=>"アタッカー","D"=>"ディフェンダー"}[card["Role"].to_s.upcase] || "不明" %>/<%= card["Cost"] %>/<%= card["Power"] %>
                    レアリティ: <%= card["Rarity"] %>
                  </span>
                </div>
              </label>
            </li>
          <% end %>
        </ul>
      </div>
    </div>

    <!-- 右: レアリティ別に選択カードを表示 -->
    <div class="w-50">
      <div class="card p-3 mb-4">
        <div class="card-header bg-success text-white">
          <strong>選択したカード (レアリティ別)</strong>
        </div>
        <div class="card-body">
          <% [4,3,2,1].each do |rarity| %>
            <% rarity_name = case rarity
                when 4 then "Legend"
                when 3 then "Gold"
                when 2 then "Silver"
                when 1 then "Bronze"
                else "Unknown"
                end %>
            <h5>Rarity: <%= rarity %> (<%= rarity_name %>)</h5>
            <ul class="list-group mb-3">
              <% for i in 0...@all_cards.size %>
                <% all_card = @all_cards[i] %>
                <% card_id_str = all_card["ID"].to_s %>
                <% if all_card["Rarity"].to_i == rarity && @checked_cards.include?(card_id_str) %>
                  <li class="list-group-item">
                    <div>
                      <strong><%= all_card["ID"] %> <%= all_card["Name"] %></strong>
                      <span class="text-muted ms-2">
                        <%= {"C"=>"チャージャー","A"=>"アタッカー","D"=>"ディフェンダー"}[all_card["Role"].to_s.upcase] || "不明" %>/<%= all_card["Cost"] %>/<%= all_card["Power"] %>
                        レアリティ: <%= all_card["Rarity"] %>
                      </span>
                    </div>
                  </li>
                <% end %>
              <% end %>
            </ul>
          <% end %>
        </div>
      </div>
    </div>
</div>

<script>
(function() {
  let isInitialized = false;

  function initializeCardSelection() {
    if (isInitialized) return;
    isInitialized = true;

    const checkboxes = document.querySelectorAll('.card-checkbox');
    let lastChecked = null;

    console.log('チェックボックス数:', checkboxes.length);

    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('click', function(e) {
        console.log('クリックイベント発生:', {
          shiftKey: e.shiftKey,
          lastChecked: lastChecked ? lastChecked.value : null,
          current: this.value
        });

        if (e.shiftKey && lastChecked) {
          const start = Array.from(checkboxes).indexOf(lastChecked);
          const end = Array.from(checkboxes).indexOf(this);
          
          console.log('範囲選択:', {
            startIndex: start,
            endIndex: end,
            selectedCount: Math.abs(end - start) + 1
          });

          const startIndex = Math.min(start, end);
          const endIndex = Math.max(start, end);
          
          for (let i = startIndex; i <= endIndex; i++) {
            checkboxes[i].checked = this.checked;
          }
        }
        lastChecked = this;
      });
    });
  }

  // Turboのイベントを監視
  document.addEventListener('turbo:load', initializeCardSelection);
})();
</script>