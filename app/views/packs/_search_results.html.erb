<% #役割の優先度を定義 (C->A->Dの順) %>
<% role_priority = { "C" => 0, "A" => 1, "D" => 2 } %>
<% #ソート処理: role優先度 -> cost昇順 -> power昇順 %>
<% sorted_cards = cards.sort_by do |card|
  [
    role_priority[card["Role"].to_s.upcase],
    card["Cost"].to_i,
    card["Power"].to_i
  ]
end %>

<% if sorted_cards.any? %>
  <ul class="list-group">
    <% sorted_cards.each do |card| %>
      <li class="list-group-item d-flex justify-content-between align-items-center">
        <div>
          <input type="checkbox"
            class="card-checkbox me-2"
            onchange="toggleCardSelection(this)"
            data-card-id="<%= card["ID"] %>"
            data-card-name="<%= card["Name"] %>"
            data-card-cost="<%= card["Cost"] %>"
            data-card-power="<%= card["Power"] %>"
            data-card-role="<%= {"C"=>"チャージャー","A"=>"アタッカー","D"=>"ディフェンダー"}[card["Role"].to_s.upcase] || "不明" %>"
            data-rarity="<%= card["Rarity"].to_s %>"
            <%= selected_cards.include?(card["ID"].to_s) ? 'checked' : '' %>
          >

          <strong><%= card["Name"] %></strong>
          <span class="text-muted ms-2">
            コスト: <%= card["Cost"] %> /
            パワー: <%= card["Power"] %> /
            役割: <%= {"C"=>"チャージャー","A"=>"アタッカー","D"=>"ディフェンダー"}[card["Role"].to_s.upcase] || "不明" %> /
            レアリティ: <%= card["Rarity"] %>
          </span>
        </div>
      </li>
    <% end %>
  </ul>
<% else %>
  <p>カードがありません。</p>
<% end %>
