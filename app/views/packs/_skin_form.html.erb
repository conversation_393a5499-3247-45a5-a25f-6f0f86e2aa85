<div class="d-flex gap-4">
  <!-- 左: スキン一覧 -->
  <div class="w-50">
    <div class="card p-3 mb-4">
      <h3 class="mb-3">スキン一覧</h3>
      <ul class="list-group">
        <% @all_skins.each do |skin| %>
          <li class="list-group-item">
            <% skin_id = skin["ID"].to_s %>
            <% checked_skins = @checked_skins || [] %>
            <% is_checked = checked_skins.any? { |s| s["id"].to_s == skin_id } %>
            <div class="d-flex align-items-center">
              <%= check_box_tag "skin_data[]", skin_id, is_checked, class: "me-2 skin-checkbox", id: "skin_#{skin_id}" %>
              <strong><%= skin["Name"] %></strong>
              <div class="ms-auto">
                <label for="skin_prob_<%= skin_id %>" class="me-2">確率(%)</label>
                <% prob_value = is_checked ? (checked_skins.find { |s| s["id"].to_s == skin_id }["probability"] || 50) : 50 %>
                <%= number_field_tag "skin_prob[#{skin_id}]", prob_value, min: 0, max: 100, step: 0.1, class: "form-control form-control-sm skin-prob", id: "skin_prob_#{skin_id}", style: "width: 80px;" %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    </div>
  </div>

  <!-- 右: 選択したスキンの表示 -->
  <div class="w-50">
    <div class="card p-3 mb-4">
      <div class="card-header bg-info text-white">
        <strong>選択したスキン</strong>
      </div>
      <div class="card-body">
        <table class="table table-sm">
          <thead>
            <tr>
              <th>ID</th>
              <th>スキン名</th>
              <th>確率(%)</th>
            </tr>
          </thead>
          <tbody id="selected-skins">
            <% if @checked_skins.present? %>
              <% @checked_skins.each do |skin_data| %>
                <% skin_id = skin_data["id"].to_s %>
                <% skin = @all_skins.find { |s| s["ID"].to_s == skin_id } %>
                <% if skin.present? %>
                  <tr>
                    <td><%= skin_id %></td>
                    <td><%= skin["Name"] %></td>
                    <td><%= skin_data["probability"] %></td>
                  </tr>
                <% end %>
              <% end %>
            <% else %>
              <tr>
                <td colspan="3" class="text-center">スキンが選択されていません</td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // チェックボックスの状態に応じて確率入力欄の有効・無効を切り替える
    const skinCheckboxes = document.querySelectorAll('.skin-checkbox');
    skinCheckboxes.forEach(checkbox => {
      const skinId = checkbox.value;
      const probField = document.getElementById(`skin_prob_${skinId}`);
      
      // 初期状態の設定
      probField.disabled = !checkbox.checked;
      
      // チェックボックスの変更イベント
      checkbox.addEventListener('change', function() {
        probField.disabled = !this.checked;
      });
    });
  });
</script> 