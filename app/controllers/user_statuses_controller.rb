class UserStatusesController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @stats = UserStatusService.get_stats
    @matching_users = UserStatusService.get_users_by_status('matching')
    @matched_users = UserStatusService.get_users_by_status('matched')
    @in_room_users = UserStatusService.get_users_by_status('in_room')
  end

  def matching
    @users = UserStatusService.get_users_by_status('matching')
    @title = "ユーザーはマッチング相手を探している"
    render :user_list
  end

  def matched
    @users = UserStatusService.get_users_by_status('matched')
    @title = "ペアリングされたユーザー"
    render :user_list
  end

  def in_room
    @users = UserStatusService.get_users_by_status('in_room')
    @title = "ユーザーは部屋の中にいます"
    render :user_list
  end

  def all
    @statuses = UserStatusService.get_all_statuses
    @title = "すべてのユーザー"
  end

  def remove
    user_id = params[:user_id]
    if user_id.present? && UserStatusService.remove_status(user_id)
      redirect_to user_statuses_path, notice: "ユーザーステータスが削除されました#{user_id}"
    else
      redirect_to user_statuses_path, alert: "ユーザーステータスを削除できません#{user_id}"
    end
  end
end
