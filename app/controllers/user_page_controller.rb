class UserPageController < ApplicationController
  before_action :set_user, only: %i[ show score_edit score_update box_update box gifts purchase_history ]

  def new
    @user = User.new
  end

  def edit
    @user = User.find_by(open_id: params[:open_id])
  end
  
  def update
    @user = User.find_by(open_id: params[:open_id])

    if @user.update(user_params)
      redirect_to user_page_show_path(@user.open_id), notice: "ユーザー情報が更新されました。"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def find
    if params[:query].present?
      @users = User.where('name LIKE ?', "%#{params[:query]}%").order(id: :desc)
      if @users.blank?
        # 数値以外の文字を削除してからopen_idで検索
        normalized_query = params[:query].gsub(/[^0-9]/, '')
        @users = User.where(open_id: normalized_query).order(id: :desc)
      end
    
    else
      @users = []
    end
  end

  def ranking
    @categories = [:rate, :atk, :def, :win, :damage]
    @ranking_users = {}
    
    @categories.each do |category|
      @ranking_users[category] = {other_users: [], self_user: nil}

      if category == :rate
        # rateはユーザーテーブルから直接取得
        User.by_rate.limit(200).each_with_index do |user, index|
          @ranking_users[category][:other_users] << {
            open_id: user.open_id,
            name: user.name,
            score: user.rate || 0,
            icon: user.profile_settings["icon"],
            icon_frame: user.profile_settings["icon_frame"],
            title: user.profile_settings["title"],
            rank: index + 1,
          }
        end
      elsif category == :win
        # winsはユーザーテーブルから直接取得
        User.by_wins.limit(200).each_with_index do |user, index|
          @ranking_users[category][:other_users] << {
            open_id: user.open_id,
            name: user.name,
            score: user.wins || 0,
            icon: user.profile_settings["icon"],
            icon_frame: user.profile_settings["icon_frame"],
            title: user.profile_settings["title"],
            rank: index + 1,
          }
        end
      else
        # 他のカテゴリーは従来通りuser_scoresから取得
        UserScore.ranking(category).includes(:user).limit(200).each_with_index do |record, index|
          @ranking_users[category][:other_users] << {
            open_id: record.user.open_id,
            name: record.user.name,
            score: record.score || 0,
            icon: record.user.profile_settings["icon"],
            icon_frame: record.user.profile_settings["icon_frame"],
            title: record.user.profile_settings["title"],
            rank: index + 1,
          }
        end
      end
    end
    @ranking_users[:field] = "global"
    
    respond_to do |format|
      format.html
      format.json { render json: { rankings: @ranking_users } }
    end
  end

  def score_edit
  end

  def score_update
    #　既存のスコアを取得
    before_score = @user.user_scores.find_by(season: params[:user_score][:season], category: params[:user_score][:category])
    if before_score.nil?
      before_score = UserScore.new(season: params[:user_score][:season], category: params[:user_score][:category], user_id: @user.open_id)
    end

    #　スコアを更新
    before_score.score = params[:user_score][:score]
    before_score.save!

    redirect_to user_page_show_path(@user.open_id), notice: "スコアが更新されました。"
  end

  # ボックス(所持カード)の更新処理
  def box_update
    card_id = params[:card_id].to_s
    
    # 現在のボックスデータ取得
    box = @user.box || {}
    
    # カードエントリーの初期化(存在しない場合)
    box[card_id] ||= { "nr" => 0, "sh" => 0, "pr" => 0 }
    
    # 入力された所持枚数を設定
    box[card_id]["nr"] = params[:nr].to_i
    box[card_id]["sh"] = params[:sh].to_i
    box[card_id]["pr"] = params[:pr].to_i
    
    # すべての枚数が0の場合、カードエントリーを削除
    if box[card_id]["nr"] == 0 && box[card_id]["sh"] == 0 && box[card_id]["pr"] == 0
      box.delete(card_id)
    end
    
    # ボックスデータを更新
    @user.box = box
    
    if @user.save
      redirect_to user_page_box_path(@user.open_id), notice: "カード情報が更新されました。"
    else
      redirect_to user_page_box_path(@user.open_id), alert: "カード情報の更新に失敗しました。"
    end
  end

  def findmulti
  end

  def analytics
    @user = User.find_by(open_id: params[:open_id])
    @user_score = @user.user_scores
    @user_logs = @user.user_logs.order(created_at: :desc).page(params[:page]).per(30)
  end

  def show
    @user_score = @user.user_scores
    @user_logs = @user.user_logs.order(created_at: :desc).page(params[:page]).per(30)
    @total_purchase_amount = UserService.get_total_purchase_amount(@user)
  end

  def showMulti
  end

  def create
    respond_to do |format|
      result = UserService.create_user_with_log(user_params, request)
      
      if result[:success]
        @user = result[:user]
        format.html { redirect_to user_page_show_path(@user.open_id), notice: "User was successfully created." }
        format.json { render :show, status: :created, location: @user }
      else
        @user = User.new(user_params)
        @user.errors.add(:base, result[:errors]) if result[:errors].present?
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @user.errors, status: :unprocessable_entity }
      end
    end
  end

  def box
    # ボックス(所持カード)一覧を表示するアクション
  end

  def gifts
    # ギフト一覧を表示するアクション
    @not_received_gifts = Gift.where(user_id: @user.open_id, state: 0)
                            .where('end_at IS NULL OR end_at >= ?', Time.current)
                            .order(created_at: :desc)
    @received_gifts = Gift.where(user_id: @user.open_id, state: 1)
                        .where('end_at IS NULL OR end_at >= ?', Time.current)
                        .order(open_at: :desc)
    @expired_gifts = Gift.where(user_id: @user.open_id)
                       .where('end_at < ?', Time.current)
                       .order(end_at: :desc)
  end

  def purchase_history
    # 課金履歴を表示するアクション
    @purchase_data = UserService.get_purchase_history(@user)
  end

  def ban_update
    @user = User.find_by(open_id: params[:open_id])
    
    if @user.nil?
      flash[:error] = "ユーザーが見つかりません。"
      redirect_to user_page_find_path
      return
    end
    
    ban_end_at = params[:ban_end_at]
    reason = params[:reason]
    
    @user.ban_end_at = ban_end_at
    
    ActiveRecord::Base.transaction do
      if @user.save
        # バン履歴を記録
        UserBan.create(open_id: @user.open_id, ban_end_at: ban_end_at, reason: reason)
        flash[:notice] = "ユーザー #{@user.name} のバン状態を更新しました。"
        redirect_to user_page_show_path(@user.open_id)
      else
        flash[:error] = "バン状態の更新に失敗しました。"
        render :ban
      end
    end
  end
  
  def ban
    @user = User.find_by(open_id: params[:open_id])
    
    if @user.nil?
      flash[:error] = "ユーザーが見つかりません。"
      redirect_to user_page_find_path
      return
    end
    
    # 過去のバン履歴を取得
    @ban_history = UserBan.where(open_id: @user.open_id).order(created_at: :desc)
  end

  def generate_transfer_code
    @user = User.find_by(open_id: params[:open_id])
    
    if @user.nil?
      flash[:error] = "ユーザーが見つかりません。"
      redirect_to user_page_find_path
      return
    end
    
    result = UserService.get_transfer_code(@user)
    
    if result[:error_type]
      flash[:error] = result[:error]
    else
      flash[:notice] = "引継ぎコード: #{result[:code]} (有効期限: #{result[:limit_date].strftime('%Y-%m-%d %H:%M')})"
    end
    
    redirect_to user_page_show_path(@user.open_id)
  end

  def enable_account_update
    @user = User.find_by(open_id: params[:open_id])
    @user.enable_account = true
    @user.save
    flash[:notice] = "アカウントを有効化しました。"
    redirect_to user_page_show_path(@user.open_id)
  end
  
  def disable_account_update
    @user = User.find_by(open_id: params[:open_id])
    @user.enable_account = false
    @user.save
    flash[:notice] = "アカウントを無効化しました。"
    redirect_to user_page_show_path(@user.open_id)
  end
  
  private
    # Use callbacks to share common setup or constraints between actions.
  def set_user
    @user = User.find_by(open_id: params[:open_id])
  end

  # Only allow a list of trusted parameters through.
  def user_params
    params.require(:user).permit(:name, :password, :password_confirmation, :free_dia, :rate, :login_days)
  end
end
