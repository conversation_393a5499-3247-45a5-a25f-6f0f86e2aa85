class MissionsController < ApplicationController
  before_action :set_mission, only: [:show, :edit, :update, :destroy]

  def index
    @missions = Mission.all
  end

  def show
  end

  def new
    @mission = Mission.new
  end

  def edit
  end

  def create
    @mission = Mission.new(mission_params)

    if @mission.save
      redirect_to mission_path(@mission.uid), notice: 'Mission was successfully created.'
    else
      render :new
    end
  end

  def update
    if @mission.update(mission_params)
      redirect_to mission_path(@mission.uid), notice: 'Mission was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @mission.destroy
    redirect_to missions_url, notice: 'Mission was successfully destroyed.'
  end

  private

  def set_mission
    @mission = Mission.find_by!(uid: params[:uid])
  end

  def mission_params
    params.require(:mission).permit(:uid, :category, :rewards, :achieve_cond)
  end
end 