class ChestsController < ApplicationController
  before_action :authenticate_master_user!
  before_action :set_chest, only: %i[ show edit update destroy ]

  def index
    @chests = Chest.all.order(uid: :asc)
  end

  def show
  end

  def new
    @chest = Chest.new
  end

  def edit
  end

  def create
    @chest = Chest.new(chest_params)

    respond_to do |format|
      if @chest.save
        format.html { redirect_to chest_url(@chest.uid), notice: "宝箱が正常に作成されました。" }
        format.json { render :show, status: :created, location: chest_url(@chest.uid) }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @chest.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @chest.update(chest_params)
        format.html { redirect_to chest_url(@chest.uid), notice: "宝箱が正常に更新されました。" }
        format.json { render :show, status: :ok, location: chest_url(@chest.uid) }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @chest.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @chest.destroy!

    respond_to do |format|
      format.html { redirect_to chests_url, notice: "宝箱が正常に削除されました。", status: :see_other }
      format.json { head :no_content }
    end
  end

  private
    def set_chest
      @chest = Chest.find_by!(uid: params[:uid])
    end

    def chest_params
      chest_param = params.require(:chest).permit(
        :uid, 
        :category, 
        :rewards
      )
      
      # rewardsパラメータがJSON文字列で渡ってきた場合はパース
      if chest_param[:rewards].is_a?(String)
        chest_param[:rewards] = JSON.parse(chest_param[:rewards])
      end
      
      chest_param
    end
end 