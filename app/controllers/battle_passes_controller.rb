class BattlePassesController < ApplicationController
  before_action :set_battle_pass, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  
  def index
    @battle_passes = BattlePass.all.order(uid: :desc)
  end

  def show
  end

  def new
    @battle_pass = BattlePass.new
  end

  def edit
  end

  def create
    @battle_pass = BattlePass.new(battle_pass_params)

    if @battle_pass.save
      redirect_to battle_pass_path(@battle_pass.uid), notice: 'Battle pass was successfully created.'
    else
      render :new
    end
  end

  def update
    if @battle_pass.update(battle_pass_params)
      redirect_to battle_pass_path(@battle_pass.uid), notice: 'Battle pass was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @battle_pass.destroy
    redirect_to battle_passes_url, notice: 'Battle pass was successfully destroyed.'
  end

  private

  def set_battle_pass
    @battle_pass = BattlePass.find_by!(uid: params[:uid])
  end

  def battle_pass_params
    shop_param = params.require(:battle_pass).permit(:uid, :premium_rewards, :free_rewards, :start_at, :end_at, :period_id, :season)

    shop_param[:premium_rewards] = JSON.parse(shop_param[:premium_rewards])
    shop_param[:free_rewards] = JSON.parse(shop_param[:free_rewards])

    shop_param
  end
end