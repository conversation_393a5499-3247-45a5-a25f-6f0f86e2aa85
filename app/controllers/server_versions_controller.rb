require 'hashdiff'

class ServerVersionsController < ApplicationController
  before_action :authenticate_master_user!
  
  # エクスポート可能な項目の定義
  EXPORTABLE_ITEMS = {
    chests: Chest,
    battle_passes: BattlePass,
    login_bonuses: LoginBonus,
    items: Item,
    gift_masters: GiftMaster,
    packs: Pack,
    charging_items: ChargingItem,
    shop_bundles: ShopBundle,
    supplies: Supply,
    card_modifiers: CardModifier,
    langs: Lang,
    periods: Period,
    master_data: MasterDatum
  }

  def index
    # 新しい順で表示
    @server_versions = ServerVersion.all.order(created_at: :desc)

    # ベースバージョンを取得
    base_version = ServerSetting.version
    last_version_hash = {}
    if base_version
      last_version_hash = JSON.parse(base_version.data)
    end

    @hash = get_diff_data(last_version_hash, create_hash)
    
  end

  def show
    @server_version = ServerVersion.find_by!(uid: params[:uid])
    @hash = get_diff_data(create_hash, JSON.parse(@server_version.data))
  end

  def get_diff_data(target_hash, current_hash)
    # 元のハッシュのコピーを作成（ディープコピー）
    target_copy = target_hash.deep_dup
    current_copy = current_hash.deep_dup
    
    # コピーしたハッシュからマスターデータを除外
    target_copy.delete("master_data")
    current_copy.delete("master_data")

    # マスターデータを除外したコピーで差分計算
    diffdata = Hashdiff.diff(target_copy, current_copy)
    diff_summary = {add: [], delete: [], change: []}
    diffdata.each do |change|
      case change[0]
      when "~"
        diff_summary[:change] << {:key => "#{change[1]}", :value => change[3]}
      when "+"
        diff_summary[:add] << {:key => "#{change[1]}", :value => change[2]}
      when "-"
        diff_summary[:delete] << {:key => "#{change[1]}", :value => change[2]}
      end
    end

    return diff_summary
  end

  def import
  end

  def back_up
    # hash = create_hash
    # ServerVersion.create(title: "バックアップ", desc: "バックアップ", data: hash.to_json, uid: 0)
  end

  def select
    ServerSetting.current.update(version_id: params[:uid])
    redirect_to server_versions_path, notice: "バージョンを選択しました"
  end

  def save_data
    hash = create_hash(params[:selected_items])
    
    # 最大のuidを探す
    max_uid = ServerVersion.maximum(:uid)
    if max_uid
      uid = max_uid + 1
    else
      uid = 0
    end
    ServerVersion.create(title: params[:title], desc: params[:desc], data: hash.to_json, uid: uid)
    redirect_to server_versions_path, notice: 'データをエクスポートしました。'
  end

  def import_data
    # ファイルを読み込む
    file = File.read(params[:file])
    json = JSON.parse(file)
    # JSONファイルのキーを元にインポートする項目を決定
    selected_items = json.keys
    ActiveRecord::Base.transaction do
      # 選択された項目のみを処理
      # 項目の処理
      selected_items.each do |item|
        model = EXPORTABLE_ITEMS[item.to_sym]
        model.delete_all if model
        
        json[item]&.each do |data|
          model.create(data)
        end
      end

      # コメントアウトされたコード - 必要に応じて削除または修正
      # selected_items.each do |item|
      #   case item
      #   when 'packs'
      #     # パックに関連するテーブルを先に削除
      #     Edition.delete_all
      #     PackRarityProbability.delete_all
      #     Pack.delete_all
      #     
      #     # パックデータのインポート
      #     json["packs"]&.each do |pack|
      #       pack_editions = pack.delete("editions")
      #       pack_rarity_probabilities = pack.delete("pack_rarity_probabilities")
      #       
      #       new_pack = Pack.create(pack)
      #       
      #       if pack_editions.present?
      #         pack_editions.each do |edition|
      #           edition["pack_id"] = new_pack.uid
      #           Edition.create(edition)
      #         end
      #       end
      #       
      #       if pack_rarity_probabilities.present?
      #         pack_rarity_probabilities.each do |probability|
      #           probability["pack_id"] = new_pack.uid
      #           PackRarityProbability.create(probability)
      #         end
      #       end
      #     end
      #   else
      #     # その他の項目の処理
      #     model = EXPORTABLE_ITEMS[item.to_sym]
      #     Rails.logger.info "model.count: #{model.count}"
      #     model.delete_all if model
      #     
      #     Rails.logger.info "model.count: #{model.count}"
      #     json[item]&.each do |data|
      #       model.create(data)
      #       Rails.logger.info "model.count: #{model.count}"
      #     end
      #   end
      # end
    end

    redirect_to server_versions_path, notice: 'データをインポートしました。'
  end

  # データを全てhashにして返す
  def create_hash(selected_items = nil)
    json = {}
    
    # 選択された項目のみを処理
    items_to_process = selected_items || EXPORTABLE_ITEMS.keys.map(&:to_s)
    
    items_to_process.each do |item|
      model = EXPORTABLE_ITEMS[item.to_sym]
      if model
        json[item] = generate_json_data(model.all, ["id", "created_at", "updated_at"])
      end
    end
    
    return json
  end

  def export_data
    @server_version = ServerVersion.find_by!(uid: params[:uid])
    data = @server_version.data
    # JSONデータをパースして整形する
    json_data = JSON.parse(data)
    formatted_data = JSON.pretty_generate(json_data)
    timestamp = Time.now.strftime('%Y%m%d%H%M%S')
    filename = "#{@server_version.title}_#{timestamp}.json"
    
    send_data formatted_data, filename: filename, type: 'application/json'
  end

end 