class CardModifiersController < ApplicationController
  before_action :set_card_modifier, only: [:show, :edit, :update, :destroy, :set_use, :set_unuse]
  before_action :authenticate_master_user!

  def index
    @card_modifiers = CardModifier.all
    @all_cards = MasterDatum.get_cards
    @all_groups = MasterDatum.get_groups
  end

  def set_use
    @card_modifier.use = 1
    @card_modifier.save
    redirect_to card_modifiers_path, notice: "カード修正が有効になりました"
  end

  def set_unuse
    @card_modifier.use = 0
    @card_modifier.save
    redirect_to card_modifiers_path, notice: "カード修正が無効になりました"
  end

  def show
  end

  def new
    @card_modifier = CardModifier.new
    @card_modifier.use = 1
    @card_modifier.use_text0 = -1
    @card_modifier.use_text1 = -1
    @card_modifier.use_text2 = -1
    @card_modifier.use_script0 = -1
    @card_modifier.use_script1 = -1
    @card_modifier.use_script2 = -1
    @card_modifier.use_timing0 = -1
    @card_modifier.use_timing1 = -1
    @card_modifier.use_timing2 = -1
    
    @all_cards = MasterDatum.get_cards
    @all_groups = MasterDatum.get_groups
  end

  def edit
    @all_cards = MasterDatum.get_cards
    @all_groups = MasterDatum.get_groups
  end

  def create
    @card_modifier = CardModifier.new(card_modifier_params)
    
    # 最新のuidを取得して+1するか、レコードが存在しない場合は1を設定
    max_uid = CardModifier.maximum(:uid)
    @card_modifier.uid = max_uid.nil? ? 1 : max_uid + 1
    
    if @card_modifier.save!
      redirect_to card_modifiers_path, notice: "カード修正が更新されました"
    end
  end

  def update
    @card_modifier = CardModifier.find(params[:uid])
    if @card_modifier.update(card_modifier_params)
      redirect_to card_modifiers_path, notice: "カード修正が更新されました"
    else
      render :edit
    end
  end

  def destroy
    @card_modifier = CardModifier.find(params[:uid])
    @card_modifier.destroy
    redirect_to card_modifiers_path, notice: "カード修正が削除されました"
  end

  private
    def set_card_modifier
      @card_modifier = CardModifier.find(params[:uid])
    end

    def card_modifier_params
      params.require(:card_modifier).permit(
        :card_id, :is_nerf, :name, :group, :category, :role, :cost, :power, :timing0, :text0, :script0, :timing1, :text1, :script1, :timing2, :text2, :script2, :use, :kind, :rarity,
        :use_text0, :use_text1, :use_text2, :use_script0, :use_script1, :use_script2, :use_timing0, :use_timing1, :use_timing2,
      )
    end 
    
    def checkOver0(num)
      return num > 0 ? num : nil
    end

    def new_card
      @card_modifier = CardModifier.new(use: true, kind: 1 )
    end


    def edit_cards
      Cardmodifier.where(use: true, kind: 2).order(:card_id)
    end

    def new_cards
      Cardmodifier.where(use: true, kind: 3).order(:card_id)
    end
end
