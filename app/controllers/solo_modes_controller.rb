class SoloModesController < ApplicationController
  before_action :set_solo_mode, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  def index
    @solo_modes = SoloMode.all
  end

  def show
  end

  def new
    @solo_mode = SoloMode.new
  end

  def edit
  end

  def create
    @solo_mode = SoloMode.new(solo_mode_params)

    if @solo_mode.save
      redirect_to solo_mode_path(@solo_mode.uid), notice: 'Solo mode was successfully created.'
    else
      render :new
    end
  end

  def update
    if @solo_mode.update(solo_mode_params)
      redirect_to solo_mode_path(@solo_mode.uid), notice: 'Solo mode was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @solo_mode.destroy
    redirect_to solo_modes_url, notice: 'Solo mode was successfully destroyed.'
  end

  private

  def set_solo_mode
    @solo_mode = SoloMode.find_by!(uid: params[:uid])
  end

  def solo_mode_params
    params.require(:solo_mode).permit(:uid, :level, missions: {}, title: {}, enemy_decks: {})
  end
end 