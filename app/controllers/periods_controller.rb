class PeriodsController < ApplicationController
    before_action :authenticate_master_user!
    before_action :set_period, only: %i[ show edit update destroy ]
    
    def index
        @periods = Period.order(:start_at)
    end
    
    def show
    end
    
    def new
        @period = Period.new
    end
    
    def edit
    end
    
    def create
        @period = Period.new(period_params)
    
        if @period.save
            redirect_to periods_url, notice: "期間が正常に追加されました。"
        else
            render :new, status: :unprocessable_entity
        end
    end
    
    def update
        if @period.update(period_params)
            redirect_to periods_url, notice: "期間が正常に更新されました。"
        else
            render :edit, status: :unprocessable_entity
        end
    end
    
    def destroy
        @period.destroy
        redirect_to periods_url, notice: "期間が正常に削除されました。"
    end
    
    private
        def set_period
            @period = Period.find_by!(uid: params[:uid])
        end
    
        def period_params
            params.require(:period).permit(:uid, :name, :description, :start_at, :end_at, :parent_id, :is_persistent, :is_active)
        end
end
