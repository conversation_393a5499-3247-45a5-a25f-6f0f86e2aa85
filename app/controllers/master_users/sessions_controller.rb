# frozen_string_literal: true

class MasterUsers::SessionsController < Devise::SessionsController
  # before_action :configure_sign_in_params, only: [:create]
  before_action :check_allowed_email, only: [:create]

  # GET /resource/sign_in
  # def new
  #   super
  # end

  # POST /resource/sign_in
  # def create
  #   super
  # end

  # DELETE /resource/sign_out
  # def destroy
  #   super
  # end

  # protected

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_sign_in_params
  #   devise_parameter_sanitizer.permit(:sign_in, keys: [:attribute])
  # end

  def create
    super do |resource|
      if resource.persisted?
        cookies.signed[:admin_id] = {
          value: resource.id,
          httponly: true
        }
        Rails.logger.info "Set admin cookie for user #{resource.id}"
      end
    end
  end
  
  def destroy
    Rails.logger.info "Removing admin cookie for user #{current_master_user&.id}"
    cookies.delete(:admin_id)
    super
  end

  protected

  def check_allowed_email
    user = MasterUser.find_by(email: params[:master_user][:email])
    if user.nil? || !MasterUser::ALLOWED_EMAILS.include?(user.email)
      flash[:alert] = "このメールアドレスでは管理画面にアクセスできません。"
      redirect_to new_master_user_session_path
    end
  end
end
