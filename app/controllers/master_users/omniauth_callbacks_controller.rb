# frozen_string_literal: true

class MasterUsers::OmniauthCallbacksController < Devise::OmniauthCallbacksController
  # You should configure your model like this:
  # devise :omniauthable, omniauth_providers: [:twitter]

  # You should also create an action method in this controller like this:
  # def twitter
  # end

  # More info at:
  # https://github.com/heartcombo/devise#omniauth

  # GET|POST /resource/auth/twitter
  # def passthru
  #   super
  # end

  # GET|POST /users/auth/twitter/callback
  # def failure
  #   super
  # end

  # protected

  # The path used when OmniAuth fails
  # def after_omniauth_failure_path_for(scope)
  #   super(scope)
  # end

  
  def google_oauth2
    callback_from(:google_oauth2)
  end

  def callback_from(provider)
    provider = provider.to_s
    auth = request.env['omniauth.auth']
    
    # メールアドレスが許可リストに含まれているか確認
    unless MasterUser::ALLOWED_EMAILS.include?(auth.info.email)
      flash[:alert] = "このメールアドレスでは管理画面にアクセスできません。"
      redirect_to new_master_user_session_path
      return
    end
    
    @user = MasterUser.from_omniauth(auth)

    if @user.persisted?
      flash[:notice] = I18n.t('devise.omniauth_callbacks.success', kind: provider.capitalize)
      sign_in_and_redirect @user
    else
      session["devise.#{provider}_data"] = auth
      redirect_to new_master_user_registration_url
    end
  end
end
