module Api
    class ContactsController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def create
            begin
                player_id = params[:player_id]
                app_version = params[:app_version]
                device_model = params[:device_model]
                os_version = params[:os_version]
                content = params[:content]
                date = params[:date]
                content_detail = params[:content_detail]
                email = params[:email]
                if player_id.blank? || app_version.blank? || device_model.blank? ||
                    os_version.blank? || content.blank? || date.blank? || content_detail.blank? || email.blank?
                    raise StandardError.new("invalid_parameters")
                end
                ContactService.create_contact(player_id, app_version, device_model, os_version, content, date, content_detail, email)
                render json: { success: true } # TODO 成功したとき何が必要か要必要必要
            rescue StandardError => e
                ErrorService.handle_exception(e, Api::ContactsController, "create")
            end
        end
    end
end