module Api
    class GiftsController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index
            GiftService.check_and_create_gifts(current_user) # ギフトを新しく作成
            result = GiftService.get_gifts(current_user,user_lang)
            render json: result
        end

        def gift_open
            result = GiftService.open_gift(current_user, params[:uid],user_lang)
            render json: result
        end

        def open_all
            result = GiftService.open_all_gifts(current_user,user_lang)
            render json: result
        end

    end
end