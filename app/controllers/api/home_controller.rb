module Api
    class HomeController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index
            begin
                chests = ChestService.fetch_and_update_user_chests(current_user)
                banners = BannerService.fetch_and_update_user_banners(host: request.host, port: request.port)
                gift_num = current_user.gifts.where(state: 0).count
                render json: {
                    user: UserService.get_res(current_user),
                    chests: chests,
                    banners: banners,
                    gift_num: gift_num
                }
            rescue => e
                ErrorService.handle_exception(e, Api::HomeController, "index")
            end
        end
    end
end