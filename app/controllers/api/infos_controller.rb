module Api
  class InfosController < ApplicationController
    skip_before_action :verify_authenticity_token

    def index
      
      @infos = Rails.cache.fetch('infos/index', expires_in: 1.minutes) do
        Info.within_valid_period.map do |info|
          {
            uid: info.uid,
            title: info.title[user_lang],
            title_img: info.image_url(host: request.host, port: request.port),
            tag: info.category || "info",
            start_at: info.period_start_at,
            end_at: info.period_end_at,
          }
        end
      end

      render json: { infos: @infos }
    end

    def show
      begin
        @info = Info.find_by(uid: params[:uid])
        if @info.nil?
          raise StandardError.new("info_not_found")
        end
        render json: {info: {
          uid: @info.uid,
          title: @info.title[user_lang],
          title_img: @info.image_url(host: request.host, port: request.port),
          # desc: Info.rich_text_to_markdown(@info.content,host: request.host, port: request.port), # TODO cloudstorage対応
          desc: @info.desc[user_lang],
          start_at: @info.period_start_at,
          end_at: @info.period_end_at,
          tag: @info.category || "info",
        }}
      rescue => e
        render json: ErrorService.handle_exception(e, Api::InfosController, "show")
      end
    end
  end

end