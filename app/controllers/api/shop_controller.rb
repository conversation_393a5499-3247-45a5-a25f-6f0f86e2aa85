module Api
    class ShopController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index
            result = ShopService.get_shop_index(current_user,user_lang,os)
            render json: result
        end

        def buy_daily_shop
            if params[:uid].present?
                result = DailyShopService.buy_daily_shop(current_user,user_lang,os,params[:uid])
            else
                result = {error_type: "uid_required",error: "uidが必要です"}
            end
            render json: result
        end

        def update_daily_shop
            result = DailyShopService.update_daily_shop(current_user,os)
            render json: result
        end

        def buy_dia_bundle
            if params[:uid].present?
                result = ShopBundleService.buy_dia_bundle(current_user,os,user_lang,params[:uid])
            else
                result = {error_type: "uid_required",error: "uidが必要です"}
            end
            render json: result
        end

        def buy_cash_bundle
            if params[:receipt].present?
                result = ShopBundleService.buy_cash_bundle(current_user,os,user_lang,params[:receipt])
            else
                result = {error_type: "receipt_required",error: "receiptが必要です"}
            end
            render json: result
        end

        def buy_supply
            if params[:category].present? && params[:uid].present?
                result = SupplyService.buy_supply(current_user,user_lang,os,params[:category],params[:uid])
            else
                result = {error_type: "category_and_uid_required",error: "カテゴリーとuidが必要です"}
            end
            render json: result
        end

        def buy_card
            if params[:card_id].present? && params[:count].present? && params[:category].present?
                result = CardChangeService.buy_card(current_user,user_lang,os,params[:card_id],params[:count],params[:category],params[:pack_id])
            else
                result = {error_type: "card_id_and_count_required",error: "card_idと購入枚数が必要です"}
            end
            render json: result
        end

        def receive_charged_rewards
            if params[:uid].present?
                result = ShopBonusService.receive_charged_rewards(current_user,user_lang,params[:uid])
            else
                result = {error_type: "uid_required",error: "uidが必要です"}
            end
            render json: result
        end

        def receive_consumed_rewards
            if params[:uid].present?
                result = ShopBonusService.receive_consumed_rewards(current_user,user_lang,params[:uid])
            else
                result = {error_type: "uid_required",error: "uidが必要です"}
            end
            render json: result
        end

        def buy_pass
            if params[:category].present?
                result = ShopPassService.buy_pass(current_user,user_lang,os,params[:category])
            else
                result = {error_type: "category_required",error: "カテゴリーが必要です"}
            end
            render json: result
        end

        def buy_dia
            if params[:uid].present?
                result = DiaChargeService.buy_dia(current_user,os,params[:uid])
            else
                result = {error_type: "uid_required",error: "購入するダイアを指定してください"}
            end
            render json: result
        end

        def iap_ios
            result = IapIosService.iap_ios(current_user,params)
            response = IapService.get_response(current_user,result[:success],result[:rewards],result[:error_type],result[:category])
            response = response.merge({user:UserService.get_res(current_user)})
            render json: response
        end

        def iap_android
            result = IapAndroidService.iap_android(current_user,params)
            response = IapService.get_response(current_user,result[:success],result[:rewards],result[:error_type],result[:category])
            response = response.merge({user:UserService.get_res(current_user)})
            render json: response
        end

    end
end
