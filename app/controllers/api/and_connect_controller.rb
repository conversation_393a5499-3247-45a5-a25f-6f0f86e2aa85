class Api::AndConnectController < ApplicationController
    skip_before_action :verify_authenticity_token, only: [:handle_rtdn] # csrfチェックをスキップ
    # googleのS2S通知を受け取る用でクライアントからは呼ばれない
    def handle_rtdn
        result = IapAndroidService.handle_rtdn(params)
        render json: result
    rescue => e
        Rails.logger.error(category: "and_connect_controller.handle_rtdn", error: e.message)
        render json: { success: false, message: "エラーが発生しました" }
        # render json: { success: false, message: "エラーが発生しました" }, status: :internal_server_error httpsをエラーにしたいとき
    end
end