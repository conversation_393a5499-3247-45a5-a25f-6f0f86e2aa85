class Api::IosConnectController < ApplicationController
    skip_before_action :verify_authenticity_token, only: [:handle_s2s_notification] # csrfチェックをスキップ
    # appleのS2S通知を受け取る用でクライアントからは呼ばれない
    def handle_s2s_notification
        result = IapIosService.handle_s2s_notification(params)
        render json: result
    rescue => e
        render json: { success: false, message: "エラーが発生しました" }, status: :internal_server_error
    end
end