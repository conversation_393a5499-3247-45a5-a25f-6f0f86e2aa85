module Api
    class BattlePassesController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token
        def index
            result = BattlePassService.get_battle_pass(current_user)
            render json: result
        end

        def receive
            result = BattlePassService.receive_single_battle_pass(current_user, params[:category])
            render json: result
        end

        def receive_all
            result = BattlePassService.receive_all_battle_pass(current_user)
            render json: result
        end
    end
end