module Api
  class RankingsController < ApplicationController
    before_action :authenticate_user!
    before_action :check_ban
    skip_before_action :verify_authenticity_token
    
    def index
      @categories = [:rate, :atk, :def, :win, :damage]
      @ranking_users = Rails.cache.fetch("rankings/index", expires_in: 1.minute) do
        ranking_users = {}
        @categories.each do |category|
          ranking_users[category] = {other_users: [], self_user: nil}

          if category == :rate
            # rateはユーザーテーブルから直接取得
            User.by_rate.limit(200).each_with_index do |user, index|
              ranking_users[category][:other_users] << {
                open_id: user.open_id,
                name: user.name,
                score: user.rate || 0,
                icon: user.profile_settings["icon"],
                icon_frame: user.profile_settings["icon_frame"],
                title: user.profile_settings["title"],
                rank: index + 1,
              }
            end
          elsif category == :win
            # winsはユーザーテーブルから直接取得
            User.by_wins.limit(200).each_with_index do |user, index|
              ranking_users[category][:other_users] << {
                open_id: user.open_id,
                name: user.name,
                score: user.wins || 0,
                icon: user.profile_settings["icon"],
                icon_frame: user.profile_settings["icon_frame"],
                title: user.profile_settings["title"],
                rank: index + 1,
              }
            end
          else
            # 他のカテゴリーは従来通りuser_scoresから取得
            UserScore.ranking(category).includes(:user).limit(200).each_with_index do |record, index|
              ranking_users[category][:other_users] << {
                open_id: record.user.open_id,
                name: record.user.name,
                score: record.score || 0,
                icon: record.user.profile_settings["icon"],
                icon_frame: record.user.profile_settings["icon_frame"],
                title: record.user.profile_settings["title"],
                rank: index + 1,
              }
            end
          end
        end
        ranking_users[:field] = "global"
        ranking_users
      end

      # ユーザーの順位を取得
      @categories.each do |category|
        if category == :rate
          score = current_user.rate
          # そのユーザーのレートより高いレートを持つユーザーの数を数える
          higher_users_count = User.where("rate > ?", score).count || 0
        elsif category == :win
          score = current_user.wins
          # そのユーザーの勝利数より多い勝利数を持つユーザーの数を数える
          higher_users_count = User.where("wins > ?", score).count || 0
        else
          score = UserScore.find_by(user_id: current_user.open_id, category: category)
          if score.present?
            # そのユーザーのスコアより高いスコアを持つユーザーの数を数える
            higher_users_count = UserScore.where("score > ?", score.score).count || 0
          else
            higher_users_count = 0
          end
        end
        rank = higher_users_count + 1
        @ranking_users[category][:self_user] = {
          open_id: current_user.open_id,
          name: current_user.name,
          score: score || 0,
          icon: current_user.profile_settings["icon"],
          icon_frame: current_user.profile_settings["icon_frame"],
          title: current_user.profile_settings["title"],
          rank: rank,
        }
      end

      render json: {rankings: @ranking_users}
    end
  end
end 