#TODO 実際にクライアントとのやり取りできるか確認する
module Api
    class IapsController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def check_ios
            result = IapIosService.check_for_ios(current_user,params[:receipt])
            if result[:error_type].present?
                render json: {error_type: result[:error_type], error: result[:error]}
            else
                render json: result[:rewards]
            end
        end

        def check_android
            result = IapAndroidService.check_for_and(current_user,params[:receipt])
            if result[:error_type].present?
                render json: {error_type: result[:error_type], error: result[:error]}
            else
                render json: result[:rewards]
            end
        end

        # TODO テスト用のみ
        def test_buy
            iap_data = IapIos.new(
                platform: 0,
                product: params[:product_key],
                open_id: current_user.open_id,
                transaction_data: -1,
                buy_date: Time.now,
                quantity: 1
            )
            if params[:product_key] == "pack_pass" || params[:product_key] == "analysis_pass"
                result = IapService.subscription_action(current_user,params[:product_key],Time.now + 1.minutes) # サブスク1分継続
                current_user.save!
                response = IapService.get_response(current_user,result[:success],result[:rewards],result[:error_type],result[:category])
                response = response.merge({user:UserService.get_res(current_user)})
                render json: response
            else
                result = IapService.bought_process(current_user,params[:product_key],"ios")
                current_user.save!
                response = IapService.get_response(current_user,result[:success],result[:rewards],result[:error_type],result[:category])
                response = response.merge({user:UserService.get_res(current_user)})
                render json: response
            end
        rescue => e
            result = ErrorService.handle_exception(e, "Api::IapsController", "test_buy")
            render json: IapService.get_response(current_user,false,[],result[:error_type],params[:product_key])
        end

    end
end