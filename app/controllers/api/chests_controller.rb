module Api
  class ChestsController < ApplicationController
    before_action :authenticate_user!
    before_action :check_ban
    skip_before_action :verify_authenticity_token

    def open
        result = ChestService.open_chest(current_user,params[:uid])
        render json: result
    end

    def add_win
        result = ChestService.add_win_count(current_user)
        render json: result
    end
  end
end