module Api
    class LoginBonusesController  < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        # TODOメニュー画面でも表示したい場合はリクエストパラムにメニュー化追加すれば対応可能
        def index
            result = LoginBonusService.get_login_bonus(current_user,user_lang)
            render json: result
        end

        def event_receive
            if params[:uid].nil?
                return render json: {error_type: "invalid_params", error: "取得したいイベントが指定されていません"}
            end
            result = LoginBonusService.receive_event_login_bonus(current_user,params[:uid],user_lang)
            render json: result
        end

        def normal_receive
            result = LoginBonusService.receive_normal_login_bonus(current_user,user_lang)
            render json: result
        end
    end
end