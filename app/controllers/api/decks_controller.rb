module Api
    class DecksController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        # デッキの一覧表示
        def index
            result = DeckService.get_deck(current_user)
            render json: result
        end

        #  TODO デッキの保存(直保存するだけ)
        def save
            if params[:deck].present?
                result = DeckService.save_deck(current_user, params[:deck])
                render json: result
            else
                render json: {error_type: "deck_not_specified", error: "デッキが指定されていません"}
            end
        end

    end
end