module Api
    class SkinsController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index
            skins = SkinService.get_skins_json(current_user)
            sands = ItemService.skin_sands_to_array(current_user.has_items["skin_sands"] || [])
            render json: {skins: skins,sands: sands}
        end

        def level_up
            if params[:skin_id].present?
                result = SkinService.level_up(current_user, params[:skin_id])
                render json: result
            else
                render json: {error_type: "invalid_params",error:"パラメータが不足しています"}
            end
        end
    end
end