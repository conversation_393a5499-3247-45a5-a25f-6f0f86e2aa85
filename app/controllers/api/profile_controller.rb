module Api
    class ProfileController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index
            begin
                result = ProfileService.index(current_user)
                render json: result
            end
        end

        def other_user_index
            begin
                other_user = User.find_by(open_id: params[:open_id])
                if other_user.nil?
                    raise StandardError.new("failed_to_find_other_user")
                end
                    render json: {other_user: UserService.get_other_user_res(other_user)}
            rescue StandardError => e
                render json: ErrorService.handle_exception(e, Api::ProfileController, "other_user_index")
            end
        end

        def update
            begin
                profile_settings = {}
                if params[:icon].present?
                    profile_settings[:icon] = params[:icon]
                end
                if params[:icon_frame].present?
                    profile_settings[:icon_frame] = params[:icon_frame]
                end
                if params[:card_collections].present?
                    profile_settings[:card_collections] = params[:card_collections] # array
                end
                if params[:badges].present?
                    profile_settings[:badges] = params[:badges] # array
                end
                if params[:title].present?
                    profile_settings[:title] = params[:title]
                end
                if params[:chara_id].present?
                    profile_settings[:chara_id] = params[:chara_id]
                end
                result = ProfileService.update_profile(current_user, profile_settings)
                render json: result
            rescue StandardError => e
                render json: ErrorService.handle_exception(e, Api::ProfileController, "update")
            end
        end

        def name_update
            begin
                result = ProfileService.name_update(current_user, params[:name], os)
                render json: result
            rescue StandardError => e
                render json: ErrorService.handle_exception(e, Api::ProfileController, "name_update")
            end
        end
    end
end
