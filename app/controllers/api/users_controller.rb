module Api
    class UsersController < ApplicationController
        before_action :authenticate_user!, except: [:register, :login, :use_transfer_code, :maintenance]
        before_action :check_ban, except: [:register, :use_transfer_code, :maintenance, :enable_account, :disable_account]
        skip_before_action :verify_authenticity_token
        skip_before_action :check_api_maintenance, only: [:maintenance]

        def show
            render json: {
                user: UserService.get_res(current_user)
            }
        end

        # uidで指定したユーザーを取得
        def register
          
            if(validate_user() == false)
              return
            end

            # ユーザー名
            name = params[:name]
            user_params = { name: name }
            
            # UserServiceを使用してユーザー作成
            result = UserService.create_user_with_log(user_params, request)

            respond_to do |format|
              if result[:success]
                user = result[:user]
                row_password = result[:password]
                # JSON形式でのレスポンス
                format.json {
                  render json: {
                    code: 200,
                    open_id: user.open_id,
                    password: row_password,
                    access_token: generate_jwt_token(user.open_id)
                  }
                }
                # HTML形式での既存の処理
                format.html {
                  redirect_to user_login_path
                }
              else
                # エラーハンドリング
                error_message = "ユーザー登録に失敗しました。"
                error_type = "registration_failed"
                
                format.json {
                  render json: {
                    code: 400,
                    error: result[:errors],
                    error_type: error_type,
                  }
                }
                format.html {
                  flash[:error] = error_message
                  flash[:errors] = result[:errors]
                  redirect_to user_register_path
                }
              end
            end
        end

        def login
            open_id = params[:open_id]
            password = params[:password]
            master_data = MasterDatum.get_all()
            master_data_hash_key = MasterDatum.get_hash_key()

            if(validate_user() == false)
              return
            end

            user = User.find_by(open_id: open_id)

            respond_to do |format|
              if user && user.authenticate(password)
                # ログイン成功時のログ記録
                begin
                  # ギフトマスターをチェック
                  GiftService.check_and_create_gifts(user)

                  # LoginServiceを使用してログイン処理を実行
                  login_result = LoginService.process_login(user, request)
                  
                  # ログイン処理に失敗した場合
                  unless login_result[:success]
                    Rails.logger.error("ログイン処理エラー: #{login_result[:error]}")
                  end
                  
                rescue => e
                  Rails.logger.error("ログイン記録エラー: #{e.message}")
                  # ログ作成に失敗してもログイン処理は続行
                end

                aa_version = ConstantService.get("operation.aa_version_#{os}")

                format.json {
                  render json: {
                    code: 200,
                    access_token: generate_jwt_token(user.open_id),
                    user: UserService.get_res(user),
                    master_data: master_data,
                    master_data_hash_key: master_data_hash_key,
                    server_settings: { aa_version: aa_version }
                  }
                }
                format.html { redirect_to some_path }
              else
                # ログイン失敗時のログ記録（オプション）
                if user
                  begin
                    user.create_log("login_failed", {ip_address: request.remote_ip, device: request.user_agent})
                  rescue => e
                    Rails.logger.error("ログイン失敗記録エラー: #{e.message}")
                  end
                end

                # エラーメッセージとエラータイプの設定
                error_message = "ログインに失敗しました。"
                error_type = "login_failed"

                if user.nil?
                  error_message = "指定されたユーザーIDが見つかりません。"
                  error_type = "user_not_found"
                elsif !user.authenticate(password)
                  error_message = "パスワードが正しくありません。"
                  error_type = "invalid_password"
                end

                format.json {
                  render json: {
                    code: 401,
                    error: error_message,
                    error_type: error_type
                  }
                }
                format.html { render :login, layout: false }
              end
            end
        end

        def update_settings
            settings = params[:settings]
            settings = JSON.parse(settings) if settings.is_a?(String)
            if settings.nil?
                return {error_type: "invalid_settings", error: "設定が不正です"}
            end
            # 設定のバリデーション
            if settings.to_s.size > ConstantService::MAX_SETTINGS_SIZE * 1024
                return {error_type: "invalid_settings", error: "設定が#{ConstantService::MAX_SETTINGS_SIZE}kBを超えています"}
            end
            current_user.update(user_settings: settings)
            render json: {
                code: 200,
                user: UserService.get_res(current_user)
            }
        end

            # 引き継ぎコード取得
        def get_transfer_code
          result = UserService.get_transfer_code(current_user)
          render json: result
        end

        def use_transfer_code
          result = UserService.use_transfer_code(current_user, params[:code],params[:open_id])
          render json: result
        end

        def delete_transfer_code
          result = UserService.delete_transfer_code(current_user)
          render json: result
        end

        def all_update
          result = UserService.all_update(current_user,params[:main_data],params[:exp],params[:battle_pass_pts],params[:rewards_key],params[:reason])
          render json: result
        end

        def update_main_data
          result = UserService.update_main_data(current_user,params[:main_data])
          Rails.logger.info(category: "users_controller.update_main_data", reason: params[:reason])
          render json: result
        end

        def enable_account
          if current_user.update(enable_account: true)
            render json: {
              success: true,
              user: UserService.get_res(current_user)
            }
          else
            render json: {
              success: false,
              error: "アカウント有効化に失敗しました。"
            }
          end
        end

        def disable_account
          if current_user.update(enable_account: false)
            render json: {
              success: true,
              user: UserService.get_res(current_user)
            }
          else
            render json: {
              success: false,
              error: "アカウント無効化に失敗しました。"
            }
          end
        end

        def maintenance
          render json: {
            code: 503,
            status: 'maintenance',
            message: 'システムメンテナンス中です。しばらく経ってから再度お試しください。',
            estimated_end_time: Time.now + 2.hours
          }
        end

        private

        # JWTトークンを生成する
        def generate_jwt_token(user_id)
          payload = {
            user_id: user_id,
            exp: 12.hours.from_now.to_i
          }
          JWT.encode(payload, Rails.application.credentials.secret_key_base, 'HS256')
        end

    end
end