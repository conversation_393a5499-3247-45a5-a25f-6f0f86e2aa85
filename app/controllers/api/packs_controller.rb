module Api
    class PacksController < ApplicationController
        before_action :authenticate_user!
        before_action :check_ban
        skip_before_action :verify_authenticity_token

        def index_packs
            result = PackService.index_packs(current_user,user_lang)
            render json: result
        end

        def index_cards
            result = PackService.index_cards(params[:pack_id],current_user,os)
            render json: result
        end

        def buy_pack
            category = params[:category] || "dia"
            result = PackService.buy_pack(current_user,os,params[:pack_id],params[:count],category)
            render json: result
        end

        def buy_card
            case params[:edition]
            when "normal"
                edition_type = 1
            when "shine"
                edition_type = 2
            when "premium"
                edition_type = 3
            else
                raise "Invalid edition type"
            end
            result = PackService.buy_card(current_user,os,params[:pack_id],params[:card_id],params[:count],edition_type,params[:category])
            render json: result
        end

    end
end