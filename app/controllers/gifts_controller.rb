class GiftsController < ApplicationController
  before_action :authenticate_user!
  layout "user"

  # def send_specific
  #   item_kind_str = params[:item_kind]
  #   item_kind_i   = item_kind_str.to_i

  #   item_count_str = params[:item_count]
  #   item_count_i   = item_count_str.to_i

  #   desc       = params[:desc]
  #   limit      = params[:limit]
  #   state      = 0

  #   gift = Gift.new(
  #     user_id:     current_user.id,
  #     item_kind:   item_kind_i,
  #     desc:        desc,
  #     state:       state,
  #     limited_at:  limit,
  #     master_id:   -1,
  #     master_limit: limit
  #   )

  #   if [4,5,6].include?(item_kind_i)
  #     gift.value = item_count_i
  #     gift.item_count = 0
  #   else
  #     gift.item_count = item_count_i
  #     gift.value = 0
  #   end

  #   gift.save
  #   redirect_to user_gifts_index_path(current_user.open_id)
  # end


  def index
    @gifts = Gift.where(user_id: current_user.open_id).order(:uid)
  end

  def new
    @gift = Gift.new
  end

  def create
    @gift = Gift.new(gift_params)

    @gift_master = GiftMaster.find_by(uid: params[:gift_master_uid]) if params[:gift_master_uid].present?

    if @gift_master
      @gift.user_id      = current_user.open_id
      @gift.rewards    = @gift_master.rewards
      @gift.desc         = @gift_master.desc
      @gift.end_at       = @gift_master.end_at
      @gift.state        = 0
      @gift.master_id    = @gift_master.uid
    else
      @gift.master_id    = -1
      @gift.master_limit = @gift.end_at
    end

    if @gift.save
      redirect_to gifts_path, notice: "ギフトが作成されました。"
    else
      flash.now[:alert] = "ギフトの作成に失敗しました。"
      render :new, status: :unprocessable_entity
    end
  end

  def open
    result = GiftService.open_gift(current_user, params[:gift_uid])

    if result[:success]
      redirect_to user_gifts_index_path(current_user.open_id), notice: result[:message]
    else
      redirect_to user_gifts_index_path(current_user.open_id), alert: result[:message]
    end
  end

  def open_all
    result = GiftService.open_all_gifts(current_user)

    if result[:success]
      redirect_to user_gifts_index_path(current_user.open_id), notice: result[:message]
    else
      redirect_to user_gifts_index_path(current_user.open_id), alert: result[:message]
    end
  end

  private

  def gift_params
    params.require(:gift).permit(:item_kind, :item_count, :desc, :end_at)
  end
end
