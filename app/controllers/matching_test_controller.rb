class MatchingTestController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @test_users = User.where("open_id LIKE ?", "test_user_%")
                     .order(:rate)
                     .limit(30)
    @current_stats = {
      rank: {
        total: UserStatusService.count_by_channel_type('rank_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched').size
      },
      free: {
        total: UserStatusService.count_by_channel_type('free_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('free_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('free_matching', 'matched').size
      },
      room: {
        total: UserStatusService.count_by_channel_type('room_matching'),
        rooms: UserStatusService.get_room_count
      }
    }
    
    @rooms = UserStatusService.get_rooms_data
  end

  def simulate_rank_matching
    user_ids = params[:user_ids] || []
    
    if user_ids.empty?
      redirect_to matching_test_index_path, alert: "Please select at least one user"
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      # Simulate rank matching
      UserStatusService.update_status(user_id, "matching", {
        rank: user.rate,
        timecount: 0,
        queue_size: user_ids.size,
        started_at: Time.now,
        channel_type: "rank_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching"
      }
    end

    render json: { 
      success: true, 
      message: "#{results.size} users started rank matching",
      results: results
    }
  end

  def simulate_free_matching
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      # Tạo deck mặc định cho user
      default_deck = generate_default_deck

      # Simulate free matching với deck
      UserStatusService.update_status(user_id, "matching", {
        timecount: 0,
        started_at: Time.now,
        channel_type: "free_matching",
        deck: default_deck,
        deck_ready: true
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching",
        deck_ready: true
      }
    end

    render json: {
      success: true,
      message: "#{results.size} users started free matching with decks",
      results: results
    }
  end

  def simulate_room_matching
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    # Logic: Chia đôi users - nửa đầu tạo phòng, nửa sau join vào phòng
    total_users = user_ids.size
    room_creators_count = (total_users / 2.0).ceil  # Làm tròn lên
    joiners_count = total_users - room_creators_count

    room_creators = user_ids[0...room_creators_count]
    room_joiners = user_ids[room_creators_count..-1]

    results = []
    created_rooms = []

    # Bước 1: Tạo phòng cho nửa đầu users
    room_creators.each_with_index do |user_id, index|
      user = User.find_by(open_id: user_id)
      next unless user

      room_id = "ROOM_#{Time.now.to_i}_#{index}"
      created_rooms << room_id

      # User tạo phòng → auto ready (vì là người đầu tiên)
      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        joined_at: Time.now,
        ready: true,  # Người tạo phòng auto ready
        role: "creator",
        channel_type: "room_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        room_id: room_id,
        ready: true,
        role: "creator",
        status: "in_room"
      }
    end

    # Bước 2: Join nửa sau users vào các phòng đã tạo
    room_joiners.each_with_index do |user_id, index|
      user = User.find_by(open_id: user_id)
      next unless user

      # Random chọn phòng từ danh sách phòng đã tạo
      room_id = created_rooms.sample

      # User join vào phòng → chưa ready (bắt đầu tính ready)
      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        joined_at: Time.now,
        ready: false,  # User join vào chưa ready
        role: "joiner",
        channel_type: "room_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        room_id: room_id,
        ready: false,
        role: "joiner",
        status: "in_room"
      }
    end

    render json: {
      success: true,
      message: "Room matching completed: #{room_creators_count} rooms created, #{joiners_count} users joined",
      results: results,
      rooms_created: created_rooms.size,
      logic: "#{room_creators_count} creators (auto ready) + #{joiners_count} joiners (need to ready)"
    }
  end

  def simulate_match_success
    # Simulate 2 users getting matched
    matching_users = UserStatusService.get_users_by_status('matching')
    
    if matching_users.size < 2
      render json: { success: false, message: "Need at least 2 users in matching status" }
      return
    end

    # Take first 2 users
    user_pairs = matching_users.first(2)
    user1_id = user_pairs[0][0]
    user2_id = user_pairs[1][0]
    
    battle_room_id = "BATTLE_#{Time.now.to_i}"

    # Update both users to matched status
    UserStatusService.update_status(user1_id, "matched", {
      opponent_id: user2_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 0,
      channel_type: user_pairs[0][1][:metadata][:channel_type] || "rank_matching",
    })

    UserStatusService.update_status(user2_id, "matched", {
      opponent_id: user1_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 1,
      channel_type: user_pairs[1][1][:metadata][:channel_type] || "rank_matching",
    })

    render json: { 
      success: true, 
      message: "Match created between #{user_pairs[0][1][:name]} and #{user_pairs[1][1][:name]}",
      battle_room_id: battle_room_id,
      players: [
        { id: user1_id, name: user_pairs[0][1][:name] },
        { id: user2_id, name: user_pairs[1][1][:name] }
      ]
    }
  end

  def clear_all_status
    # Clear all user statuses for testing
    UserStatusService.class_variable_get(:@@user_statuses).clear
    
    render json: { 
      success: true, 
      message: "All user statuses cleared"
    }
  end

  def toggle_ready
    user_id = params[:user_id]

    current_status = UserStatusService.get_status(user_id)
    if current_status && current_status[:status] == "in_room"
      current_ready = current_status[:metadata][:ready] || false
      room_id = current_status[:metadata][:room_id]

      # Toggle ready status
      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        ready: !current_ready,
        role: current_status[:metadata][:role],
        channel_type: "room_matching",
      })

      # Kiểm tra xem có cần tạo match không
      match_result = check_and_create_room_match(room_id)

      if match_result[:match_created]
        render json: {
          success: true,
          message: "Ready status toggled and match created!",
          ready: !current_ready,
          match_created: true,
          battle_room_id: match_result[:battle_room_id],
          players: match_result[:players]
        }
      else
        render json: {
          success: true,
          message: "Ready status toggled for user #{user_id}",
          ready: !current_ready,
          match_created: false
        }
      end
    else
      render json: { success: false, message: "User not in room" }
    end
  end

  def auto_ready_rooms
    # Tự động ready tất cả users trong room để test
    rooms = UserStatusService.get_rooms_data
    matches_created = []

    rooms.each do |room_id, room_data|
      # Chỉ xử lý room có đúng 2 players
      if room_data[:players].size == 2
        # Set tất cả players trong room thành ready
        room_data[:players].each do |player|
          UserStatusService.update_status(player[:id], "in_room", {
            room_id: room_id,
            ready: true,
            role: player[:role] || "player",
            channel_type: "room_matching",
          })
        end

        # Tạo match cho room này
        match_result = check_and_create_room_match(room_id)
        if match_result[:match_created]
          matches_created << match_result
        end
      end
    end

    render json: {
      success: true,
      message: "Auto-readied all rooms and created #{matches_created.size} matches",
      matches_created: matches_created
    }
  end

  def disconnect_user
    user_id = params[:user_id]

    current_status = UserStatusService.get_status(user_id)
    if current_status
      UserStatusService.remove_status(user_id)

      render json: {
        success: true,
        message: "User #{user_id} disconnected and removed from all channels"
      }
    else
      render json: { success: false, message: "User not found in any channel" }
    end
  end

  def disconnect_all_users
    user_ids = params[:user_ids] || []

    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    disconnected_count = 0
    user_ids.each do |user_id|
      current_status = UserStatusService.get_status(user_id)
      if current_status
        UserStatusService.remove_status(user_id)
        disconnected_count += 1
      end
    end

    render json: {
      success: true,
      message: "#{disconnected_count} users disconnected from all channels"
    }
  end

  private

  def check_and_create_room_match(room_id)
    # Lấy tất cả users trong room này
    room_users = UserStatusService.get_users_by_status('in_room').select do |user_id, user_data|
      user_data[:metadata][:room_id] == room_id
    end

    # Kiểm tra xem có đúng 2 users và cả 2 đều ready không
    if room_users.size == 2
      all_ready = room_users.all? { |user_id, user_data| user_data[:metadata][:ready] }

      if all_ready
        # Tạo battle room
        battle_room_id = "BATTLE_#{Time.now.to_i}_#{room_id}"

        # Chuyển cả 2 users sang trạng thái matched
        user_pairs = room_users.to_a
        user1_id = user_pairs[0][0]
        user2_id = user_pairs[1][0]

        UserStatusService.update_status(user1_id, "matched", {
          opponent_id: user2_id,
          room_id: battle_room_id,
          matched_at: Time.now,
          index_in_room: 0,
          channel_type: "room_matching",
          from_room: room_id
        })

        UserStatusService.update_status(user2_id, "matched", {
          opponent_id: user1_id,
          room_id: battle_room_id,
          matched_at: Time.now,
          index_in_room: 1,
          channel_type: "room_matching",
          from_room: room_id
        })

        return {
          match_created: true,
          battle_room_id: battle_room_id,
          players: [
            { id: user1_id, name: user_pairs[0][1][:name] },
            { id: user2_id, name: user_pairs[1][1][:name] }
          ]
        }
      end
    end

    { match_created: false }
  end

  def generate_default_deck(user = nil)
    # Tạo deck mặc định cho test
    {
      cards: [
        { id: 1, count: 3 },
        { id: 2, count: 3 },
        { id: 3, count: 3 },
        { id: 4, count: 3 },
        { id: 5, count: 3 },
        { id: 6, count: 3 },
        { id: 7, count: 3 },
        { id: 8, count: 3 },
        { id: 9, count: 3 },
        { id: 10, count: 3 }
      ],
      deck_name: "Test Deck",
      created_at: Time.now
    }
  end
end
