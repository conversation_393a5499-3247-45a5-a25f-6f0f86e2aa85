class MatchingTestController < ApplicationController
  before_action :authenticate_master_user!

  def index
    # L<PERSON>y số lượng user theo params, mặc định 50, tối đa 1000
    limit = params[:limit].to_i
    limit = 50 if limit <= 0
    limit = 1000 if limit > 1000

    @test_users = User.where("open_id LIKE ?", "test_user_%")
                     .order(:rate)
                     .limit(limit)

    @total_test_users = User.where("open_id LIKE ?", "test_user_%").count
    @current_limit = limit

    @current_stats = {
      rank: {
        total: UserStatusService.count_by_channel_type('rank_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('rank_matching', 'matched').size
      },
      free: {
        total: UserStatusService.count_by_channel_type('free_matching'),
        matching: UserStatusService.get_users_by_channel_and_status('free_matching', 'matching').size,
        matched: UserStatusService.get_users_by_channel_and_status('free_matching', 'matched').size
      },
      room: {
        total: UserStatusService.count_by_channel_type('room_matching'),
        rooms: UserStatusService.get_room_count
      }
    }

    @rooms = UserStatusService.get_rooms_data
  end

  def simulate_rank_matching
    user_ids = params[:user_ids] || []
    
    if user_ids.empty?
      redirect_to matching_test_index_path, alert: "Please select at least one user"
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      # Simulate rank matching
      UserStatusService.update_status(user_id, "matching", {
        rank: user.rate,
        timecount: 0,
        queue_size: user_ids.size,
        started_at: Time.now,
        channel_type: "rank_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching"
      }
    end

    render json: { 
      success: true, 
      message: "#{results.size} users started rank matching",
      results: results
    }
  end

  def simulate_free_matching
    user_ids = params[:user_ids] || []
    
    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    results = []
    user_ids.each do |user_id|
      user = User.find_by(open_id: user_id)
      next unless user

      # Simulate free matching
      UserStatusService.update_status(user_id, "matching", {
        timecount: 0,
        started_at: Time.now,
        channel_type: "free_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        status: "matching"
      }
    end

    render json: { 
      success: true, 
      message: "#{results.size} users started free matching",
      results: results
    }
  end

  def simulate_room_matching
    user_ids = params[:user_ids] || []
    room_id = params[:room_id] || "TEST_ROOM_#{Time.now.to_i}"
    
    if user_ids.empty?
      render json: { success: false, message: "Please select at least one user" }
      return
    end

    results = []
    user_ids.each_with_index do |user_id, index|
      user = User.find_by(open_id: user_id)
      next unless user

      # Simulate room matching
      UserStatusService.update_status(user_id, "in_room", {
        room_id: room_id,
        joined_at: Time.now,
        ready: index.even?, # Alternate ready status for testing
        channel_type: "room_matching",
      })

      results << {
        user_id: user_id,
        name: user.name,
        rate: user.rate,
        room_id: room_id,
        ready: index.even?,
        status: "in_room"
      }
    end

    render json: { 
      success: true, 
      message: "#{results.size} users joined room #{room_id}",
      results: results,
      room_id: room_id
    }
  end

  def simulate_match_success
    # Simulate 2 users getting matched
    matching_users = UserStatusService.get_users_by_status('matching')
    
    if matching_users.size < 2
      render json: { success: false, message: "Need at least 2 users in matching status" }
      return
    end

    # Take first 2 users
    user_pairs = matching_users.first(2)
    user1_id = user_pairs[0][0]
    user2_id = user_pairs[1][0]
    
    battle_room_id = "BATTLE_#{Time.now.to_i}"

    # Update both users to matched status
    UserStatusService.update_status(user1_id, "matched", {
      opponent_id: user2_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 0,
      channel_type: user_pairs[0][1][:metadata][:channel_type] || "rank_matching",
    })

    UserStatusService.update_status(user2_id, "matched", {
      opponent_id: user1_id,
      room_id: battle_room_id,
      matched_at: Time.now,
      index_in_room: 1,
      channel_type: user_pairs[1][1][:metadata][:channel_type] || "rank_matching",
    })

    render json: { 
      success: true, 
      message: "Match created between #{user_pairs[0][1][:name]} and #{user_pairs[1][1][:name]}",
      battle_room_id: battle_room_id,
      players: [
        { id: user1_id, name: user_pairs[0][1][:name] },
        { id: user2_id, name: user_pairs[1][1][:name] }
      ]
    }
  end

  def clear_all_status
    # Clear all user statuses for testing
    UserStatusService.class_variable_get(:@@user_statuses).clear
    
    render json: { 
      success: true, 
      message: "All user statuses cleared"
    }
  end

  def toggle_ready
    user_id = params[:user_id]
    
    current_status = UserStatusService.get_status(user_id)
    if current_status && current_status[:status] == "in_room"
      current_ready = current_status[:metadata][:ready] || false
      
      UserStatusService.update_status(user_id, "in_room", {
        room_id: current_status[:metadata][:room_id],
        ready: !current_ready,
        channel_type: "room_matching",
      })
      
      render json: { 
        success: true, 
        message: "Ready status toggled for user #{user_id}",
        ready: !current_ready
      }
    else
      render json: { success: false, message: "User not in room" }
    end
  end
end
