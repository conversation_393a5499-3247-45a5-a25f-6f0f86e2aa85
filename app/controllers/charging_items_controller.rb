class ChargingItemsController < ApplicationController
  before_action :authenticate_master_user!
  before_action :set_charging_item, only: [ :edit, :update, :destroy ]

  def index
    @charging_items = ChargingItem.all.order(:uid)
  end

  def new
    @charging_item = ChargingItem.new
  end

  def create
    @charging_item = ChargingItem.new(charging_item_params)
    if @charging_item.save
      redirect_to charging_items_path, notice: "課金アイテムを作成しました"
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @charging_item.update(charging_item_params)
      redirect_to charging_items_path, notice: "課金アイテムを更新しました"
    else
      render :edit
    end
  end

  def destroy
    @charging_item.destroy
    redirect_to charging_items_path, notice: "課金アイテムを削除しました"
  end

  private

  def set_charging_item
    @charging_item = ChargingItem.find_by!(uid: params[:uid])
  end

  def charging_item_params
    charging_item = params.require(:charging_item).permit(
      :uid,
      :start_at,
      :end_at,
      :period_id,
      :max_count,
      :ios_key,
      :android_key,
      :steam_key,
      :category,
      :rewards,
      name: {},
      desc: {}
    )
    charging_item[:rewards] = JSON.parse(charging_item[:rewards])
    charging_item
  end
end
