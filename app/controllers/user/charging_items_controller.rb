class User::ChargingItemsController < ApplicationController
  before_action :authenticate_user!
  layout "user"
  PREDEFINED_CATEGORIES = ['dia', 'bundle']

  def index
    @user = User.find_by(open_id: params[:open_id])
    if @user.nil? || @user != current_user
      return redirect_to user_login_path
    end
    
    # Filter active items based on start_at and end_at dates
    @charging_items = ChargingItem.within_valid_period
    
    # Filter by category if provided
    if params[:category].present?
      @charging_items = @charging_items.where(category: params[:category])
    end
    
    # Get available categories
    @categories = (ChargingItem.distinct.pluck(:category).compact + PREDEFINED_CATEGORIES).uniq
  end

  def purchase
    @user = User.find_by(open_id: params[:open_id])
    if @user.nil? || @user != current_user
      return redirect_to user_login_path
    end

    @item = ChargingItem.find_by(uid: params[:charging_item_uid])
    if @item
      # Check if the item is available (within date range)
      if (@item.start_at.present? && @item.start_at > Time.current) || 
         (@item.end_at.present? && @item.end_at < Time.current)
        return redirect_to user_charging_items_path(@user.open_id), 
               alert: "このアイテムは現在購入できません"
      end
      
      # Check if max_count is reached
      if @item.max_count.present? && @user.purchases.where(charging_item_id: @item.id).count >= @item.max_count
        return redirect_to user_charging_items_path(@user.open_id), 
               alert: "購入回数の上限に達しました"
      end
      
      # 課金アイテムの購入処理
      dia_amount = @item.rewards.dig("dia") || 0
      
      # ユーザーのダイヤを増やす
      @user.free_dia += dia_amount
      if @user.save
        @user.create_log("normal_purchase", { item_id: @item.uid, amount: dia_amount, category: @item.category })
        redirect_to user_charging_items_path(@user.open_id), notice: "\u30C0\u30A4\u30E4\u3092\u8CFC\u5165\u3057\u307E\u3057\u305F"
      else
        redirect_to user_charging_items_path(@user.open_id), alert: "\u8CFC\u5165\u306B\u5931\u6557\u3057\u307E\u3057\u305F"
      end
    else
      redirect_to user_charging_items_path(@user.open_id), alert: "\u8AB2\u91D1\u30A2\u30A4\u30C6\u30E0\u304C\u898B\u3064\u304B\u308A\u307E\u305B\u3093\u3067\u3057\u305F"
    end
  end
  
  def categories
    @predefined_categories = PREDEFINED_CATEGORIES
    @categories = (ChargingItem.distinct.pluck(:category).compact + @predefined_categories).uniq
    @user = User.find_by(open_id: params[:open_id])
    
    if @user.nil? || @user != current_user
      return redirect_to user_login_path
    end
  end
end
