class User::SessionsController < ApplicationController
  layout "user"
  layout false, only: [ :new_signup, :new ]
  include <PERSON><PERSON><PERSON>el<PERSON>
  require "securerandom"
  skip_before_action :verify_authenticity_token

  def new_signup
    @user = User.new
  end

  def new
    # ログイン済みの場合はトップページにリダイレクト
    redirect_to user_top_path(current_user.open_id) if current_user
  end

  def create_signup
    respond_to do |format|
    result = UserService.create_user_with_log(user_params, request)
  
    if result[:success]
      user = result[:user]
        row_password = result[:password]

        # JSON形式でのレスポンス
        format.json {
      render json: {
            code: 200,
          open_id: user.open_id,
            password: row_password,
          access_token: generate_jwt_token(user.open_id)
        }
      }
        # HTML形式での既存の処理
        format.html {
          redirect_to user_login_path
        }
    else
        format.json { render json: { code: 422, errors: result[:errors] }, status: :unprocessable_entity }
        format.html { render :new_signup, layout: false }
      end
    end
  end

  def create
    user = User.find_by(name: params[:name])
    if user
      session[:user_id] = user.open_id
      GiftService.check_and_create_gifts(user)
      redirect_to user_top_path(user.open_id), notice: "ログインに成功しました。"
    else
      flash.now[:danger] = "ユーザーが見つかりません。"
      redirect_to user_login_path
    end
  end

  private

  # JWTトークンを生成する
  def generate_jwt_token(user_id)
    payload = {
      user_id: user_id,
      exp: 24.hours.from_now.to_i
    }
    JWT.encode(payload, Rails.application.credentials.secret_key_base)
  end

  def user_params
    params.require(:user).permit(:name, :password, :password_confirmation)
  end

  # 初期データ設定
  def set_defaults(user)
    user.free_dia ||= 0
    user.login_days ||= 0
    user.rate ||= 0
  end

end
