class User::GachaController < ApplicationController
  before_action :authenticate_user!
  layout 'user'

  def detail
    @user = User.find_by(open_id: params[:open_id])
    if @user == current_user
      @pack = Pack.find_by(uid: params[:pack_uid])
    else
      return redirect_to user_login_path
    end
  end

  def pull
    @user = User.find_by(open_id: params[:open_id])
    if @user.nil? || @user != current_user
      return render json: { error: "ログインしていません" }, status: :unauthorized
    end

    @pack = Pack.find_by(uid: params[:pack_uid])
    if @pack.nil?
      return render json: { error: "パックが見つかりません" }, status: :not_found
    end

    @pull_count = params[:pull_count].to_i
    if @pull_count < 1 || @pull_count > 10
      return render json: { error: "ガチャ回数は1〜10の範囲で指定" }, status: :bad_request
    end

    @user.free_dia -= @pull_count * @pack.dia_cost
    if @user.free_dia < 0
      return render json: { error: "ダイヤが不足しています" }, status: :bad_request
    end

    @cards_per_pack = @pack.cards_per_pack
    selected_cards = @pack.card_data

    @results = []
    for i in 1..@pull_count do
      one_pack = []
      for j in 1..@cards_per_pack do
        rarity = draw_rarity(@pack)
        selected_rarity = selected_cards[rarity]
        picked_card_id = selected_rarity.sample
        edition = draw_edition(@pack)
        one_pack.push({ uid: picked_card_id, edition: edition })
      end
      @results.push(one_pack)
    end

    @user.gacha_result = @user.gacha_result + @results

    update_box(@user)
    @user.save
    @user.create_log("open_pack", {pack_id: @pack.uid, pull_count: @pull_count, results: @results})
    render :pull_result
  end

  private

  def draw_rarity(pack)
    r1 = pack.pack_rarity_probabilities.find_by(rarity: 1)&.probability
    r2 = pack.pack_rarity_probabilities.find_by(rarity: 2)&.probability
    r3 = pack.pack_rarity_probabilities.find_by(rarity: 3)&.probability
    r4 = pack.pack_rarity_probabilities.find_by(rarity: 4)&.probability
    total_prob = r1 + r2 + r3 + r4

    rand_val = rand(total_prob)

    if rand_val < r1
      return 1
    end

    rand_val -= r1
    if rand_val < r2
      return 2
    end

    rand_val -= r2
    if rand_val < r3
      return 3
    end

    return 4
  end

  def draw_edition(pack)
    e1 = pack.editions.find_by(edition_type: 1)&.probability
    e2 = pack.editions.find_by(edition_type: 2)&.probability
    e3 = pack.editions.find_by(edition_type: 3)&.probability
    e4 = pack.editions.find_by(edition_type: 4)&.probability
    total_prob = e1 + e2 + e3 + e4

    rand_val = rand(total_prob)

    if rand_val < e1
      return 1
    end

    rand_val -= e1
    if rand_val < e2
      return 2
    end

    rand_val -= e2
    if rand_val < e3
      return 3
    end

    return 4
  end

  def update_box(user)
    box = {}
    for i in 0...user.gacha_result.size
      for j in 0...user.gacha_result[i].size
        card_id = user.gacha_result[i][j]["uid"]
        edition_type = user.gacha_result[i][j]["edition"]
  
        if card_id.nil?
          next
        end
  
        entry = find_entry(box, card_id)
  
        if entry.nil?
          entry = {
            1 => 0,
            2 => 0,
            3 => 0,
            4 => 0
          }
          box[card_id] = entry
        end
        entry[edition_type] += 1
      end
    end
    user.box = box
  end
  
  # 該当IDのレコードを探す
  def find_entry(box, card_id)
    if box.has_key?(card_id)
      return box[card_id]
    else
      return nil
    end
  end
end