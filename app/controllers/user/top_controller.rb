# app/controllers/user/top_controller.rb
class User::TopController < ApplicationController
  before_action :authenticate_user!
  layout 'user'

  def show
    @user = User.find_by(open_id: params[:open_id])
  
    if @user.nil?
      flash[:alert] = "ユーザーが見つかりません"
      return redirect_to user_login_path
    end
  end

  def gacha
    @user = User.find_by(open_id: params[:open_id])
    if @user.nil? || @user != current_user
      return redirect_to user_login_path
    end
    @valid_packs = Pack.within_valid_period
  end

  def delete
    current_user.update(is_login: false)
    redirect_to user_login_path
  end
end
