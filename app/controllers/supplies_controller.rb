class SuppliesController < ApplicationController
  before_action :set_supply, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  def index
    @supplies = Supply.all.order(:uid)
    @supplies = @supplies.where(category: params[:category]) if params[:category].present?
  end

  def show
  end

  def new
    @supply = Supply.new
    
    # 前のフォームから引き継いだパラメータがある場合は設定
    if params[:supply].present?
      @supply.assign_attributes(params[:supply].permit(:category, :dia_cost, :start_at, :end_at, :period_id, :uid))
      @supply.rewards = params[:supply][:rewards] if params[:supply][:rewards].present?
    end
  end

  def edit
  end

  def create
    @supply = Supply.new(supply_params)
    
    if @supply.save
      if params[:continuous_create].present?
        # 新しいサプライオブジェクトを作成し、前回の値を引き継ぐ
        new_supply = Supply.new(
          category: @supply.category,
          dia_cost: @supply.dia_cost,
          rewards: @supply.rewards,
          start_at: @supply.start_at,
          end_at: @supply.end_at,
          period_id: @supply.period_id
        )
        
        # UIDを提案値として設定（存在チェックはAutoUidGeneratorで行われる）
        suggested_uid = @supply.uid + 1
        # 既にそのUIDが使われていないか確認
        while Supply.exists?(uid: suggested_uid)
          suggested_uid += 1
        end
        new_supply.uid = suggested_uid
        
        redirect_to new_supply_path(supply: new_supply.attributes.except('id', 'created_at', 'updated_at')), 
                    notice: 'Supply was successfully created. Creating another with similar attributes.'
      else
        redirect_to supplies_path, notice: 'Supply was successfully created.'
      end
    else
      render :new
    end
  end

  def update
    if @supply.update(supply_params)
      redirect_to supplies_path, notice: 'Supply was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @supply.destroy
    redirect_to supplies_url, notice: 'Supply was successfully destroyed.'
  end

  private

  def set_supply
    @supply = Supply.find_by!(uid: params[:uid])
  end

  def supply_params
    supply_param = params.require(:supply).permit(
      :uid, 
      :dia_cost, 
      :category, 
      :rewards, 
      :start_at, 
      :end_at, 
      :period_id, 
      rewards: [:item_type, :count, :item_id, :ext]
    )
    
    supply_param[:rewards] = JSON.parse(supply_param[:rewards])
    return supply_param
  end
end 
