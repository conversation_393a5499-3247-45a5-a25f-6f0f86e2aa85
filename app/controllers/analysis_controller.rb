class AnalysisController < ApplicationController
  before_action :authenticate_master_user!

  def index
    # 分析画面のメインページ
  end

  def billing
    # パラメータの取得とデフォルト値設定
    @view_type = params[:view_type] || 'daily' # daily, monthly, または hourly
    @platform = params[:platform] || 'all' # ios, android, または all
    
    # 時間別表示の場合は特別な期間設定ロジック
    if @view_type == 'hourly'
      start_date_param = params[:start_date]
      end_date_param = params[:end_date]
      
      if start_date_param && end_date_param
        # 両方指定された場合：指定された期間だが、7日を超える場合は制限
        @start_date = Date.parse(start_date_param)
        @end_date = Date.parse(end_date_param)
        
        # 期間が7日を超える場合は終了日から7日前に制限
        if (@end_date - @start_date).to_i > 6
          @start_date = @end_date - 6.days
        end
      elsif start_date_param && !end_date_param
        # 開始日のみ指定された場合：開始日から7日間
        @start_date = Date.parse(start_date_param)
        @end_date = @start_date + 6.days
      elsif !start_date_param && end_date_param
        # 終了日のみ指定された場合：終了日から7日前まで
        @end_date = Date.parse(end_date_param)
        @start_date = @end_date - 6.days
      else
        # どちらも指定されない場合：直近7日間
        @end_date = Date.current
        @start_date = @end_date - 6.days
      end
    else
      # 日別・月別の場合は従来通り
      @start_date = params[:start_date] ? Date.parse(params[:start_date]) : 30.days.ago.to_date
      @end_date = params[:end_date] ? Date.parse(params[:end_date]) : Date.current
    end

    # 課金分析データの取得
    @billing_data = get_billing_analysis_data(@platform, @view_type, @start_date, @end_date)
    @total_summary = calculate_total_summary(@billing_data)
  end

  private

  def get_billing_analysis_data(platform, view_type, start_date, end_date)
    # 価格設定を取得
    price_settings = IapService::PRICE_SETTINGS

    if platform == 'all'
      # 全プラットフォームの場合、iOSとAndroidのデータを統合
      ios_data = get_platform_data(IapIos, view_type, start_date, end_date, price_settings)
      android_data = get_platform_data(IapAnd, view_type, start_date, end_date, price_settings)
      
      # データを統合
      merge_platform_data(ios_data, android_data, view_type)
    else
      # 特定プラットフォームのデータを取得
      model = platform == 'ios' ? IapIos : IapAnd
      get_platform_data(model, view_type, start_date, end_date, price_settings)
    end
  end

  def get_platform_data(model, view_type, start_date, end_date, price_settings)
    # 基本的なクエリを構築
    base_query = model.where(buy_date: start_date.beginning_of_day..end_date.end_of_day)
                      .where(is_refunded: false)

    # 日別、月別、時間別でグループ化
    if view_type == 'monthly'
      # 月別の集計
      grouped_data = base_query.group_by { |record| record.buy_date.beginning_of_month }
      
      result = grouped_data.map do |period, records|
        period_data = calculate_period_data(records, price_settings)
        {
          period: period.strftime('%Y年%m月'),
          period_sort: period,
          products: period_data[:products],
          total_amount: period_data[:total_amount],
          total_count: period_data[:total_count]
        }
      end
    elsif view_type == 'hourly'
      # 時間別の集計
      grouped_data = base_query.group_by { |record| record.buy_date.beginning_of_hour }
      
      result = grouped_data.map do |period, records|
        period_data = calculate_period_data(records, price_settings)
        {
          period: period.strftime('%Y年%m月%d日 %H時'),
          period_sort: period,
          products: period_data[:products],
          total_amount: period_data[:total_amount],
          total_count: period_data[:total_count]
        }
      end
    else
      # 日別の集計
      grouped_data = base_query.group_by { |record| record.buy_date.to_date }
      
      result = grouped_data.map do |period, records|
        period_data = calculate_period_data(records, price_settings)
        {
          period: period.strftime('%Y年%m月%d日'),
          period_sort: period,
          products: period_data[:products],
          total_amount: period_data[:total_amount],
          total_count: period_data[:total_count]
        }
      end
    end

    # 期間でソート
    result.sort_by { |data| data[:period_sort] }
  end

  def merge_platform_data(ios_data, android_data, view_type)
    # 全ての期間を取得
    all_periods = (ios_data.map { |d| d[:period_sort] } + android_data.map { |d| d[:period_sort] }).uniq.sort
    
    result = all_periods.map do |period_sort|
      ios_period_data = ios_data.find { |d| d[:period_sort] == period_sort }
      android_period_data = android_data.find { |d| d[:period_sort] == period_sort }
      
      # 期間表示フォーマットを決定
      period_display = if ios_period_data
                         ios_period_data[:period]
                       elsif android_period_data
                         android_period_data[:period]
                       else
                         case view_type
                         when 'monthly'
                           period_sort.strftime('%Y年%m月')
                         when 'hourly'
                           period_sort.strftime('%Y年%m月%d日 %H時')
                         else
                           period_sort.strftime('%Y年%m月%d日')
                         end
                       end
      
      # プロダクトデータを統合
      merged_products = {}
      total_amount = 0
      total_count = 0
      
      # iOSデータを追加
      if ios_period_data
        ios_period_data[:products].each do |product_key, product_data|
          merged_products[product_key] = {
            count: product_data[:count],
            amount: product_data[:amount],
            unit_price: product_data[:unit_price]
          }
        end
        total_amount += ios_period_data[:total_amount]
        total_count += ios_period_data[:total_count]
      end
      
      # Androidデータを追加（既存のプロダクトがあれば合計）
      if android_period_data
        android_period_data[:products].each do |product_key, product_data|
          if merged_products[product_key]
            merged_products[product_key][:count] += product_data[:count]
            merged_products[product_key][:amount] += product_data[:amount]
          else
            merged_products[product_key] = {
              count: product_data[:count],
              amount: product_data[:amount],
              unit_price: product_data[:unit_price]
            }
          end
        end
        total_amount += android_period_data[:total_amount]
        total_count += android_period_data[:total_count]
      end
      
      {
        period: period_display,
        period_sort: period_sort,
        products: merged_products,
        total_amount: total_amount,
        total_count: total_count
      }
    end
    
    result
  end

  def calculate_period_data(records, price_settings)
    products = {}
    total_amount = 0
    total_count = 0

    records.each do |record|
      product_key = record.product
      # IapAndにはquantityフィールドがないため、安全に取得
      quantity = record.respond_to?(:quantity) && record.quantity ? record.quantity : 1
      price = price_settings[product_key] || 0

      # プロダクトごとの集計
      if products[product_key]
        products[product_key][:count] += quantity
        products[product_key][:amount] += price * quantity
      else
        products[product_key] = {
          count: quantity,
          amount: price * quantity,
          unit_price: price
        }
      end

      total_amount += price * quantity
      total_count += quantity
    end

    {
      products: products,
      total_amount: total_amount,
      total_count: total_count
    }
  end

  def calculate_total_summary(billing_data)
    total_amount = billing_data.sum { |data| data[:total_amount] }
    total_count = billing_data.sum { |data| data[:total_count] }
    
    # プロダクト別の総計
    product_totals = {}
    billing_data.each do |data|
      data[:products].each do |product_key, product_data|
        if product_totals[product_key]
          product_totals[product_key][:count] += product_data[:count]
          product_totals[product_key][:amount] += product_data[:amount]
        else
          product_totals[product_key] = {
            count: product_data[:count],
            amount: product_data[:amount],
            unit_price: product_data[:unit_price]
          }
        end
      end
    end

    {
      total_amount: total_amount,
      total_count: total_count,
      product_totals: product_totals
    }
  end
end 