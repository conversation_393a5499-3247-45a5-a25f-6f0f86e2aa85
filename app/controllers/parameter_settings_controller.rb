class ParameterSettingsController < ApplicationController
  before_action :authenticate_master_user!
  before_action :set_parameter_setting, only: [:edit, :update, :destroy]

  def index
    @parameter_settings = ParameterSetting.order(updated_at: :desc).page(params[:page]).per(10)
    @parameter_setting = ParameterSetting.order(updated_at: :desc).first
  end

  def new
    @parameter_setting = ParameterSetting.new
  end

  def create
    @parameter_setting = ParameterSetting.new(parameter_setting_params)
    if @parameter_setting.save
      redirect_to parameter_settings_path, notice: 'パラメータ設定が正常に作成されました。'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @parameter_setting.update(parameter_setting_params)
      redirect_to parameter_settings_path, notice: 'パラメータ設定が正常に更新されました。'
    else
      render :edit
    end
  end

  def destroy
    @parameter_setting.destroy
    redirect_to parameter_settings_path, notice: 'パラメータ設定が正常に削除されました。'
  end

  private
  
  def set_parameter_setting
    @parameter_setting = ParameterSetting.find(params[:id])
  end

  def parameter_setting_params
    params.require(:parameter_setting).permit(:settings)
  end
end 