# Admin controller for Redis-based player session management
# 管理画面表示は それをgetするだけで いいかと思います
class Admin::RedisPlayerSessionsController < ApplicationController
  before_action :authenticate_admin!
  
  # 全プレイヤーセッション表示
  def index
    @sessions = RedisPlayerSessionService.get_all_active_sessions
    @stats = RedisPlayerSessionService.get_stats
    
    # Cleanup expired sessions
    expired_count = RedisPlayerSessionService.cleanup_expired_sessions
    flash[:info] = "Cleaned up #{expired_count} expired sessions" if expired_count > 0
    
    respond_to do |format|
      format.html
      format.json { render json: { sessions: @sessions, stats: @stats } }
    end
  end
  
  # 特定プレイヤーセッション詳細
  def show
    player_id = params[:id]
    @session = RedisPlayerSessionService.get_session(player_id)
    
    if @session
      respond_to do |format|
        format.html
        format.json { render json: @session }
      end
    else
      respond_to do |format|
        format.html { redirect_to admin_redis_player_sessions_path, alert: "Player session not found" }
        format.json { render json: { error: "Player session not found" }, status: :not_found }
      end
    end
  end
  
  # ステータス別プレイヤー一覧
  def by_status
    status = params[:status]
    @players = RedisPlayerSessionService.get_players_by_status(status)
    @status = status
    
    respond_to do |format|
      format.html
      format.json { render json: @players }
    end
  end
  
  # バトルモード別プレイヤー一覧
  def by_battle_mode
    battle_mode = params[:battle_mode]
    @players = RedisPlayerSessionService.get_players_by_battle_mode(battle_mode)
    @battle_mode = battle_mode
    
    respond_to do |format|
      format.html
      format.json { render json: @players }
    end
  end
  
  # 統計情報のみ取得 (API用)
  def stats
    @stats = RedisPlayerSessionService.get_stats
    
    respond_to do |format|
      format.json { render json: @stats }
    end
  end
  
  # リアルタイム更新用 (WebSocket経由でも使用可能)
  def realtime_stats
    stats = RedisPlayerSessionService.get_stats
    
    # Broadcast to admin channel
    ActionCable.server.broadcast("admin_redis_sessions", {
      type: "stats_update",
      data: stats,
      timestamp: Time.now.to_i
    })
    
    render json: { status: "broadcasted", stats: stats }
  end
  
  # セッションクリーンアップ (手動実行)
  def cleanup
    expired_count = RedisPlayerSessionService.cleanup_expired_sessions
    
    respond_to do |format|
      format.html { 
        redirect_to admin_redis_player_sessions_path, 
        notice: "Cleaned up #{expired_count} expired sessions" 
      }
      format.json { 
        render json: { 
          status: "completed", 
          expired_count: expired_count 
        } 
      }
    end
  end
  
  private
  
  def authenticate_admin!
    # Add your admin authentication logic here
    # For example:
    # redirect_to root_path unless current_user&.admin?
  end
end
