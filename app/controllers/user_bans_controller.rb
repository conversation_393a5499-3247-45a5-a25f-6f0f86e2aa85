class UserBansController < ApplicationController
    before_action :authenticate_master_user!

    def new
    end

    def create
        puts params
        user_id = params[:user_id]
        ban_end_at = params[:ban_end_at]
        reason = params[:reason]

        user = User.find_by(open_id: user_id)

        if user.nil?
            flash[:error] = "ユーザーIDが見つかりません。"
            redirect_to new_user_ban_path
            return
            end
        user.ban_end_at = ban_end_at

        ActiveRecord::Base.transaction do
            if user.save
                # バン理由をログに記録するなどの処理
                flash[:notice] = "ユーザー #{user.name} をバンしました。"
                redirect_to new_user_ban_path
            else
                flash[:error] = "バン処理に失敗しました。"
                redirect_to new_user_ban_path
            end
            UserBan.create(open_id: user_id, ban_end_at: ban_end_at, reason: reason)
        end
    end

end