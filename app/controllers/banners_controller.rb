class BannersController < ApplicationController
  before_action :set_banner, only: [:show, :edit, :update, :destroy]
  before_action :set_image_url_options, only: [:index, :show, :edit, :new]
  before_action :authenticate_master_user!
  def index
    @banners = Banner.all.order(uid: :asc)
  end

  def show
  end

  def new
    @banner = Banner.new
  end

  def edit
  end

  def create
    @banner = Banner.new(banner_params)

    if @banner.save
      redirect_to banners_path, notice: 'バナーが正常に作成されました。'
    else
      set_image_url_options
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if params[:banner][:remove_image] == '1'
      @banner.banner_image.purge
    end

    if @banner.update(banner_params.except(:remove_image))
      redirect_to banners_path, notice: 'バナーが正常に更新されました。'
    else
      set_image_url_options
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @banner.banner_image.attached?
      @banner.banner_image.purge
    end
    @banner.destroy
    redirect_to banners_path, notice: 'バナーが正常に削除されました。'
  end

  private
    def set_banner
      @banner = Banner.find_by(uid: params[:uid])
      unless @banner
        redirect_to banners_path, alert: '指定されたバナーが見つかりません。'
      end
    end

    def set_image_url_options
      @image_url_options = {
        host: request.host,
        port: request.port
      }
    end

    def banner_params
      params.require(:banner).permit(:uid, :title, :view_type, :view_uid, :start_at, :end_at, :period_id, :banner_image, :remove_image)
    end
end 