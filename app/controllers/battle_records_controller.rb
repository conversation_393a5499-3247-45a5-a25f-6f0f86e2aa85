class BattleRecordsController < ApplicationController
  before_action :set_battle_record, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  
  def index
    @battle_records = BattleRecord.all
  end

  def show
  end

  def new
    @battle_record = BattleRecord.new
  end

  def edit
  end

  def create
    @battle_record = BattleRecord.new(battle_record_params)

    if @battle_record.save
      redirect_to battle_record_path(@battle_record.uid), notice: 'Battle record was successfully created.'
    else
      render :new
    end
  end

  def update
    if @battle_record.update(battle_record_params)
      redirect_to battle_record_path(@battle_record.uid), notice: 'Battle record was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @battle_record.destroy
    redirect_to battle_records_url, notice: 'Battle record was successfully destroyed.'
  end

  private

  def set_battle_record
    @battle_record = BattleRecord.find_by!(uid: params[:uid])
  end

  def battle_record_params
    params.require(:battle_record).permit(:uid, :category, :user_id_1, :user_id_2, :date, :winner_id, :reason)
  end
end 