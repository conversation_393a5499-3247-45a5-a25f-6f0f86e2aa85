class CardStatsController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @cards = MasterDatum.get_cards
    @title = "カードスタッツ"
    @card_stats_dailies = CardStatsDaily.order(date: :desc, card_id: :asc)
  end

  def create_daily_status
    @result = CardStatsService.create_daily_status
    # 結果の処理や表示などをここで行う
    
    # 必要に応じてリダイレクトやレスポンスの設定を行う
    respond_to do |format|
      format.html { redirect_to card_stats_index_path, notice: 'カードスタッツが更新されました' }
      format.json { render json: @result }
    end
  end

end
