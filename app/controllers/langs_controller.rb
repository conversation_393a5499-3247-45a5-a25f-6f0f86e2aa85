class LangsController < ApplicationController
  before_action :set_lang, only: %i[ show edit update destroy ]
  before_action :authenticate_master_user!
  
  def index
    @langs = Lang.order(:position)
  end

  def show
  end

  def new
    @lang = Lang.new
  end

  def edit
  end

  def create
    @lang = Lang.new(lang_params)
    @lang.name = Lang.find_name_by_locale(lang_params[:locale])

    if @lang.save
      redirect_to langs_url, notice: "言語が正常に追加されました。"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    @lang.name = Lang.find_name_by_locale(lang_params[:locale]) 
    if @lang.update(lang_params)
      redirect_to langs_url, notice: "言語が正常に更新されました。"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    p "destroy ----------------------------------------------------"
    @lang.destroy
    redirect_to langs_url, notice: "言語が正常に削除されました。"
  end

  private
    def set_lang
      @lang = Lang.find_by!(uid: params[:uid])
    end

    def lang_params
      params.require(:lang).permit(:uid, :locale, :position)
    end
end 