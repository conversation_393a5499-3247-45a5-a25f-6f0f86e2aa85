class PlayGroundsController < ApplicationController
  before_action :authenticate_master_user!
  
  def index
    # ParameterSettingのデータをビューに渡す例
    @parameter_data = {
      cached: ParameterSetting.get('daily_shop_card_sales_count'),
      direct_db: ParameterSetting.order(updated_at: :desc).first&.settings&.dig('daily_shop_card_sales_count'),
      all_settings: ParameterSetting.settings
    }
  end
end 