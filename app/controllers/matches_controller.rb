class MatchesController < ApplicationController
  before_action :set_match, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  
  def index
    @matches = Match.all
  end

  def show
  end

  def new
    @match = Match.new
  end

  def create
    @match = Match.new(match_params)

    if @match.save
      redirect_to @match, notice: '対戦記録が正常に作成されました。'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @match.update(match_params)
      redirect_to @match, notice: '対戦記録が正常に更新されました。'
    else
      render :edit
    end
  end

  def destroy
    @match.destroy
    redirect_to matches_url, notice: '対戦記録が正常に削除されました。'
  end

  private
    def set_match
      @match = Match.find(params[:id])
    end

    def match_params
      params.require(:match).permit(
        :player_0, :player_1, :result, :played_at, :game_mode, 
        :event_id, :matching_time_0, :matching_time_1, 
        :deck_0, :deck_1, :group_0, :group_1, :rank, 
        :before_rate_0, :before_rate_1, :after_rate_0, :after_rate_1,
        :replay_data, :other_data
      )
    end
end
