class MatchesController < ApplicationController
  before_action :set_match, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  
  def index
    @matches = Match.all
    
    # プレイヤーIDで検索（player_0またはplayer_1に含まれる）
    if params[:player_id].present?
      @matches = @matches.where("player_0 = ? OR player_1 = ?", params[:player_id], params[:player_id])
    end
    
    # ゲームモードでフィルタリング
    if params[:game_mode].present?
      @matches = @matches.where(game_mode: params[:game_mode])
    end
    
    # 検索期間でフィルタリング
    if params[:start_date].present?
      @matches = @matches.where("played_at >= ?", params[:start_date])
    end
    
    if params[:end_date].present?
      @matches = @matches.where("played_at <= ?", params[:end_date])
    end
    
    # ソートとページング
    @matches = @matches.order(created_at: :desc)
                       .page(params[:page])
                       .per(params[:per_page] || 100)
    
    # プレイヤー情報を事前に取得してパフォーマンスを向上
    player_ids = @matches.pluck(:player_0, :player_1).flatten.uniq.compact
    @users_cache = User.where(open_id: player_ids).index_by(&:open_id)
  end

  def show
    # プレイヤー情報を事前に取得
    player_ids = [@match.player_0, @match.player_1].compact
    @users_cache = User.where(open_id: player_ids).index_by(&:open_id)
  end

  def new
    @match = Match.new
  end

  def create
    @match = Match.new(match_params)

    if @match.save
      redirect_to @match, notice: '対戦記録が正常に作成されました。'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @match.update(match_params)
      redirect_to @match, notice: '対戦記録が正常に更新されました。'
    else
      render :edit
    end
  end

  def destroy
    @match.destroy
    redirect_to matches_url, notice: '対戦記録が正常に削除されました。'
  end

  private
    def set_match
      @match = Match.find(params[:id])
    end

    def match_params
      params.require(:match).permit(
        :player_0, :player_1, :result, :played_at, :game_mode, 
        :event_id, :matching_time_0, :matching_time_1, 
        :deck_0, :deck_1, :group_0, :group_1, :rank, 
        :before_rate_0, :before_rate_1, :after_rate_0, :after_rate_1,
        :replay_data, :other_data
      )
    end
    
    # プレイヤー名を取得するヘルパーメソッド
    def get_player_name(player_id)
      return "不明" if player_id.blank?
      
      begin
        user = @users_cache&.[](player_id)
        if user
          "#{user.name} (#{player_id})"
        else
          player_id
        end
      rescue => e
        Rails.logger.error("Error getting player name for #{player_id}: #{e.message}")
        player_id
      end
    end
    helper_method :get_player_name
    
    # プレイヤー情報を取得するヘルパーメソッド
    def get_player_info(player_id)
      return nil if player_id.blank?
      
      begin
        @users_cache&.[](player_id)
      rescue => e
        Rails.logger.error("Error getting player info for #{player_id}: #{e.message}")
        nil
      end
    end
    helper_method :get_player_info
end
