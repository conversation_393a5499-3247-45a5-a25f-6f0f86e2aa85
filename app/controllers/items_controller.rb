require 'csv'

class ItemsController < ApplicationController
  before_action :authenticate_master_user!
  def index
    @items = Item.all
    @items = @items.where(item_type: params[:item_type]) if params[:item_type].present?
    @item = Item.new
  end

  def new
    @item = Item.new
  end

  def create
    @item = Item.new(item_params)
    if @item.data.blank?
      @item.data = {}
    end
    if @item.save
      redirect_to items_path, notice: 'アイテムを作成しました'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @item = Item.find_by!(uid: params[:uid])
  end

  def update
    @item = Item.find_by!(uid: params[:uid])
    if @item.update(item_params)
      redirect_to items_path, notice: 'アイテムを更新しました'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def export
    @items = Item.all
    @items = @items.where(item_type: params[:item_type]) if params[:item_type].present?

    respond_to do |format|
      format.csv do
        send_data generate_csv(@items, @items.first.attributes.keys),
          filename: "items-#{Time.current.strftime('%Y%m%d%H%M%S')}.csv",
          type: 'text/csv; charset=utf-8'
        return
      end
      format.json do
        send_data generate_json(@items),
          filename: "items-#{Time.current.strftime('%Y%m%d%H%M%S')}.json",
          type: 'application/json; charset=utf-8'
        return
      end
    end
  end

  private

  def item_params
    params.require(:item).permit(
      "uid",
      "item_type",
      name: {}, 
      desc: {}, 
      data: {}
       )
  end

  def generate_csv(model, columns)
    # モデルをJSONに変換
    json_data = model.as_json(only: columns)

    # ヘッダーを作成
    headers = columns.map(&:to_s)

    # CSVを生成
    CSV.generate(headers: headers, write_headers: true, encoding: Encoding::UTF_8) do |csv|
      json_data.each do |item|
        csv << columns.map { |column| item[column.to_s].is_a?(Hash) ? item[column.to_s].to_json : item[column.to_s] }
      end
    end
  end
end 