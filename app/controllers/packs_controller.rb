class PacksController < ApplicationController
  before_action :authenticate_master_user!
  before_action :set_pack, only: [:edit, :update, :destroy]

  include PacksHelper

  def index
    @packs = Pack.all.order(uid: :desc)
  end

  def new
    @pack = Pack.new
    set_data
  end

  def create
    @pack = Pack.new(pack_params)
    set_data
    add_card_and_skin_data
    if @has_main_card
      if @pack.save
        redirect_to packs_path, notice: "パックが作成されました。"
      else
        render :new, status: :unprocessable_entity
      end
    else
      flash[:alert] = "メインカードが選択されていないか、パックにメインカードが含まれていません。"
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    set_data
  end

  def update
    set_data
    add_card_and_skin_data
    if @has_main_card
      if @pack.update(pack_params) # このタイミングでcard_data,skin_dataが更新される
        redirect_to packs_path, notice: "パックが更新されました。"
      else
        # エラー時は編集画面を再表示
        render :edit, status: :unprocessable_entity
      end
    else
      flash[:alert] = "メインカードが選択されていないか、パックにメインカードが含まれていません。"
      render :edit, status: :unprocessable_entity
    end
  end

  def add_card_and_skin_data
    # フォームからカードデータを取得
    if params[:card_data].present?
      card_ids = params[:card_data].map(&:to_i)
      existing_cards = @pack.card_data || []
      new_cards = []
      @has_main_card = false
      card_ids.each do |card_id|
        matching_card = @all_cards.find { |c| c["ID"] == card_id }
        next unless matching_card
        rarity_int = matching_card["Rarity"]
        rarity = rarity_to_key(rarity_int)
        need_pts = pack_params[:change_pts][rarity]
        need_dia = ConstantService.get("shop.card_change_dia_costs")[rarity]
        prob = pack_params[:rarity_prob][rarity]
        @has_main_card = true if pack_params[:main_card_id].include?(card_id)
        # packのスキーマに入れるときはrarityは数値型にする
        new_cards << {
          "id" => card_id,
          "rarity" => rarity_int,
          "need_pts" => need_pts,
          "need_dia" => need_dia,
          "probability" => prob
        }
      end
      @pack.card_data = new_cards
    end

    # スキンデータの処理
    if params[:skin_data].present?
      skin_ids = params[:skin_data].map(&:to_i)
      new_skins = []

      skin_ids.each do |skin_id|
        # skin_probからそのスキンの確率を取得
        each_skin_prob = params[:skin_prob].present? ? params[:skin_prob][skin_id.to_s].to_f : 0.0
        new_skins << {
          "id" => skin_id,
          "probability" => each_skin_prob
        }
      end

      @pack.skin_data = new_skins
    else
      # スキンが選択されていない場合は空の配列を設定
      @pack.skin_data = []
    end
  end

  def destroy
    @pack = Pack.find_by!(uid: params[:uid])
    @pack.destroy
    redirect_to packs_path, notice: "パックが削除されました。"
  end

  private

  def set_pack
    @pack = Pack.find_by!(uid: params[:uid])
  end

  def set_data
    # マスターデータを取得。なければアラートを表示する
    @all_cards = []
    @all_skins = []
    if MasterDatum.exists?
      master_data = MasterDatum.order(created_at: :desc).first
      @all_cards = master_data.content["_cards"].select { |card| card["ID"].to_i >= 10000 }
      @all_skins = master_data.content["_leader_skins"]
    # マスターデータを取得。なければアラートを表示する
    else
      flash[:alert] = "最新のマスターデータを取得してください"
      redirect_to packs_path and return
    end

    # role_priority = { "C" => 0, "A" => 1, "D" => 2 }
    # sorted_cards = @all_cards.sort_by do |card|
    #   role = card["Role"].to_s.upcase
    #   cost = card["Cost"].to_i
    #   power = card["Power"].to_i
    #   [ role_priority[role] || 999, cost, power ]
    # end
    # @all_cards = sorted_cards
    
    # 選択したカードに表示なければ[]を返す
    if @pack.card_data.present?
      @checked_cards = @pack.card_data.map { |card| card["id"].to_s }
    else
      @checked_cards = []
    end
    
    # スキンデータの設定
    @checked_skins = @pack.skin_data.present? ? @pack.skin_data : []
  end

  def pack_params
    permitted = params.require(:pack).permit(
      :uid,
      :start_at,
      :end_at,
      :period_id,
      :cards_per_pack,
      :dia_cost,
      :per_skin_prob,
      :is_special,
      :enable_pts,
      :is_visible,
      :enable_dia,
      :enable_ticket,
      :is_main,
      :image,
      :main_card_id,
      :sort_order,
      # jsonb
      name: {},
      desc: {},
      rarity_prob: {},
      edition_prob: {},
      change_pts: {},
    )

    # 文字列で入ってきた値をFloat/Integerに変換
    permitted[:main_card_id] = [permitted[:main_card_id]&.to_i] # TODO 無理やり配列にしてる
    permitted[:rarity_prob]&.transform_values!(&:to_f)
    permitted[:edition_prob]&.transform_values!(&:to_f)
    permitted[:change_pts]&.transform_values!(&:to_i)

    permitted
  end

end
