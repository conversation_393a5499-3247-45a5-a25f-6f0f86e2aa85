require 'open-uri'
require 'hashdiff'

class MasterDataController < ApplicationController
  before_action :authenticate_master_user!
  
  def index
    @data = MasterDatum.all
  end

  def show
    @datum = MasterDatum.find_by!(uid: params[:uid])
  end
  
  def show_json
    @datum = MasterDatum.find_by!(uid: params[:uid])
    respond_to do |format|
      format.html { render partial: 'shared/model_modal', locals: { title: "マスターデータ JSON (UID: #{@datum.uid})", content: @datum.content } }
      format.json { render json: @datum.content }
    end
  end
  
  # マスターデータを取得し、過去の差分をｈ
  def create
    url = "https://drive.google.com/uc?id=1GzBR_jWkRtvH7L6gcpB9nTIimCNWH8UK"

    if url.present?
      begin
        # URLからJSONデータを取得
        json_data = URI.open(url).read
        parsed_data = JSON.parse(json_data)
  
        # # 最新のMasterDatumを取得
        latest_data = MasterDatum.order(created_at: :desc).first
  
        # # 比較結果の生成
        if latest_data.present?
          uid = latest_data.uid+1
          # diff = Hashdiff.diff(latest_data.content, parsed_data)
          # diff_summary = diff.map do |change|
          #   case change[0]
          #   when "~"
          #     "変更: #{change[1]} -> #{change[2]} から #{change[3]}"
          #   when "+"
          #     "追加: #{change[1]} -> #{change[2]}"
          #   when "-"
          #     "削除: #{change[1]} -> #{change[2]}"
          #   end
          # end.join("\n")
        else
          uid = 1
          diff_summary = "初回データ登録: 差分なし"
        end

        # if diff_summary.present?
        #   chat_gpt = ChatGptService.new
        #   title_str = chat_gpt.chat("以下の変更内容を40字程度で簡潔に要約してください。(メタデータの時間に関する情報は記載しないでください。)#{diff_summary}",200)
        # else
        #   title_str = "変更なし"
        # end

        diff_summary = "データ取得"

        title_str = "文字数: #{json_data.length}"

        # 新しいデータを保存
        MasterDatum.create!(
          uid: uid,
          content: parsed_data,
          title: title_str,
          desc: diff_summary
        )
  
        redirect_to master_data_index_path, notice: "データを取得し、バージョンを保存しました。差分: #{diff_summary}"
      rescue => e
        redirect_to master_data_index_path, alert: "データの取得に失敗しました: #{e.message}"
      end
    else
      redirect_to master_data_index_path, alert: "URLを入力してください。"
    end
  end

  def destroy
    @datum = MasterDatum.find_by!(uid: params[:uid])
    @datum.destroy
    redirect_to master_data_index_path, notice: "データを削除しました。"
  end
  
  # まとめて削除
  def multi_delete
    if params[:uids].present?
      MasterDatum.where(uid: params[:uids]).destroy_all
      redirect_to master_data_index_path, notice: "選択したデータを削除しました。"
    else
      redirect_to master_data_index_path, alert: "削除するデータを選択してください。"
    end
  end

end
