class LoginBonusesController < ApplicationController
  before_action :set_login_bonus, only: [:show, :edit, :update, :destroy]
  before_action :authenticate_master_user!
  
  def index
    @login_bonuses = LoginBonus.all.order(uid: :desc)
  end

  def show
  end

  def new
    @login_bonus = LoginBonus.new
  end

  def edit
  end

  def create
    @login_bonus = LoginBonus.new(login_bonus_params)

    if @login_bonus.save
      redirect_to login_bonus_path(@login_bonus.uid), notice: 'Login bonus was successfully created.'
    else
      render :new
    end
  end

  def update
    if @login_bonus.update(login_bonus_params)
      redirect_to login_bonus_path(@login_bonus.uid), notice: 'Login bonus was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @login_bonus.destroy
    redirect_to login_bonuses_url, notice: 'Login bonus was successfully destroyed.'
  end

  private

  def set_login_bonus
    @login_bonus = LoginBonus.find_by!(uid: params[:uid])
  end

  def login_bonus_params
    login_bonus = params.require(:login_bonus).permit(:uid, :start_at, :end_at, :period_id, :is_event, :rewards, title: {}, desc: {})
    login_bonus[:rewards] = JSON.parse(login_bonus[:rewards])
    login_bonus
  end
end