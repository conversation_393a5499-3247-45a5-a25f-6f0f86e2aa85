class ApplicationController < ActionController::Base
  helper_method :current_user
  before_action :check_api_maintenance

  def current_user
    if(validate_user() == false)
      return
    end

    # JWTトークンを使用してユーザーを認証
    if session[:user_id]
      @current_user ||= User.find_by(open_id: session[:user_id])
    else
      # JWTトークンを使用してユーザーを認証
      token = request.headers['Authorization']&.split(' ')&.last
      if token
        @current_user ||= ApplicationController.authenticate_with_jwt(token)
      end
    end
  end

  def authenticate_user!
    if current_user.nil?
      redirect_to user_login_path
    end
  end

  def generate_json_data(items, except_keys = [])
    json_data = items.map do |item|
      item.as_json.except(*except_keys)
    end
  end

  def get_store_url
    if os == "ios"
      return "https://apps.apple.com/app/mythologia-the-oracle/id6739644168"
    elsif os == "android"
      return "https://play.google.com/store/apps/details?id=com.neconome.mythologiatheoracle"
    elsif os == "steam"
      return "https://store.steampowered.com/app/1234567890"
    end
  end

  def validate_user
    # バージョンチェック
    if os == "ios"
      if !ConstantService::ACCEPT_IOS_VERSIONS.include?(user_game_version)
        render json: {error_type: "failed_to_get_user_data", error: "バージョンが古いためログインできません (E10001)", decide: "ストアを開く", url: get_store_url} , status: :unauthorized
        return false
      end
    elsif os == "android"
      if !ConstantService::ACCEPT_ANDROID_VERSIONS.include?(user_game_version)
        render json: {error_type: "failed_to_get_user_data", error: "バージョンが古いためログインできません (E10002)", decide: "ストアを開く", url: get_store_url} , status: :unauthorized
        return false
      end
    elsif os == "steam"
      if !ConstantService::ACCEPT_STEAM_VERSIONS.include?(user_game_version)
        render json: {error_type: "failed_to_get_user_data", error: "バージョンが古いためログインできません (E10003)", decide: "ストアを開く", url: get_store_url} , status: :unauthorized
        return false
      end
    else
      render json: {error_type: "failed_to_get_user_data", error: "バージョンが古いためログインできません (E10004)", decide: "ストアを開く", url: get_store_url} , status: :unauthorized
      return false
    end

    return true
  end

  def user_lang
    ul = request.headers['Accept-Language']&.split(',')&.first || "ja"
    if !Lang.locales.include?(ul)
      ul = Lang.main_lang.locale
    end
    ul
  end
  
  def user_app_version
    request.headers['App-Version']&.split(',')&.first || "unknown"
  end

  def user_game_version
    request.headers['Game-Version']&.split(',')&.first || "unknown"
  end

  def user_master_data_version
    request.headers['Master-Data-Version']&.split(',')&.first || "unknown"
  end

  # ios か android か steam
  def os
    request.headers['Use-OS'] || "ios"
  end

  # JWTトークンからユーザーを認証するメソッド
  def self.authenticate_with_jwt(token)
    begin
      decoded_token = JWT.decode(token, Rails.application.credentials.secret_key_base, true, { algorithm: 'HS256' })
      User.find_by(open_id: decoded_token[0]['user_id'])
    rescue JWT::DecodeError
      nil
    end
  end

  def check_ban
    if current_user.nil?
      return
    end
    if current_user.enable_account == false
      render json: {
        error_type: "ban",
        error: "お使いのアカウントは永続的に停止されています。"
      }, status: :unauthorized
      return
    end
    if current_user.ban_end_at && current_user.ban_end_at > Time.current
      render json: {
        error_type: "ban",
        error: "お使いのアカウントは停止されています。\n停止終了日時: __TIME__",
        time_value: current_user.ban_end_at
      }, status: :unauthorized
      return
    end
  end

  private

  # APIリクエスト用のメンテナンスチェック
  def check_api_maintenance
    # /api/から始まるリクエストかつapi/users/maintenanceではない場合にチェック
    if request.path.start_with?('/api/') && request.path != '/api/users/maintenance'
      begin
        # メンテナンスモードが有効な場合
        if ConstantService.get("operation.maintenance_immediate")
          maintenance_end_time = ConstantService.get("operation.maintenance_end");
          render json: {
            error_type: "maintenance",
            time_value: Time.current.iso8601,
            error: "システムメンテナンス中です。\n終了予定時刻: 未定" 
          }, status: :service_unavailable
          return
        end

        if ConstantService.get("operation.maintenance_mode")
          maintenance_start_time = ConstantService.get("operation.maintenance_start");
          maintenance_end_time = ConstantService.get("operation.maintenance_end");
          if Time.current >= maintenance_start_time && Time.current <= maintenance_end_time
              render json: {
                error_type: "maintenance",
                time_value: maintenance_end_time ? Time.parse(maintenance_end_time).iso8601 : Time.current.iso8601,
              error: "システムメンテナンス中です。\n終了予定時刻: __TIME__" 
            }, status: :service_unavailable
            return
          end
        end

      rescue => e
        # エラーが発生した場合はログに記録
        Rails.logger.error("メンテナンスチェックエラー: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
      end
    end
  end
end