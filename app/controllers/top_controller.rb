class TopController < ApplicationController
  before_action :authenticate_master_user!, only: %i[index]
  def index
    redis = Redis.new(url: ENV["REDIS_URL"] || "redis://localhost:6379/0")

    @rank_wait_user_count = redis.keys("matching_data:*").count
    @all_room_count = redis.keys("battle_room:*").count
    @rank_room_count = redis.keys("battle_room:rank*").count
    @free_room_count = redis.keys("battle_room:free*").count

    # すべてのランクルームのbefore_rate_0とbefore_rate_1を取得
    @rank_rates = []
    @rank_room_rates = []
    redis.keys("battle_room:rank*").each do |key|
      json = JSON.parse(redis.get(key))
      @rank_rates << json["before_rate_0"]
      @rank_rates << json["before_rate_1"]
      
      rate_0 = json["before_rate_0"]
      rate_1 = json["before_rate_1"]
      rate_avg = (rate_0 + rate_1) / 2.0
      
      @rank_room_rates << {
        rate_0: rate_0,
        rate_1: rate_1,
        rate_avg: rate_avg,
        user_0_open_id: json["player_0_id"] || "",
        user_1_open_id: json["player_1_id"] || "",
        user_0_name: json["player_0_name"] || "",
        user_1_name: json["player_1_name"] || ""
      }
    end
    
    @rank_room_rates.sort_by! { |room| room[:rate_avg] }.reverse!
  end
end
