class GiftMastersController < ApplicationController
  before_action :authenticate_master_user!

  def index
    @gift_masters = GiftMaster.all.order(uid: :desc)
  end

  def new
    @gift_master = GiftMaster.new
    @gift_master.user_id = params[:user_id] if params[:user_id].present?
  end

  def create
    @gift_master = GiftMaster.new(gift_master_params)

    term = params[:term].to_i

    if term.positive?
      @gift_master.end_at = @gift_master.period_start_at.since(term.days)
    end

    if @gift_master.save
      redirect_to gift_masters_path, notice: "ギフトが作成されました。"
    else
      flash.now[:alert] = "ギフトの作成に失敗しました。"
      render :new
    end
  end
  def edit
    @gift_master = GiftMaster.find_by(uid: params[:uid])
  end
  
  def update
    @gift_master = GiftMaster.find_by(uid: params[:uid])
    @gift_master.update(gift_master_params)
    redirect_to gift_masters_path, notice: "ギフトが更新されました。"
  end

  def destroy
    @gift_master = GiftMaster.find_by(uid: params[:uid])
    @gift_master.destroy
    redirect_to gift_masters_path, notice: "ギフトが削除されました。"
  end

  private

  def gift_master_params
    gift_master = params.require(:gift_master).permit(
      :uid,
      :rewards,
      :start_at,
      :end_at,
      :period_id,
      :user_id,
      name: {},
      desc: {}  
    )
    gift_master[:rewards] = JSON.parse(gift_master[:rewards])
    gift_master
  end
end
