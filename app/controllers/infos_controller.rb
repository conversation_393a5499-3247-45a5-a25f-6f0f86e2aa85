class InfosController < ApplicationController
  before_action :set_info, only: %i[ show edit update destroy ]
  before_action :authenticate_master_user!

  # GET /infos or /infos.json
  def index
    respond_to do |format|
      @infos = Info.all.order(uid: :desc)
      format.html { }
      format.json { render json: { infos: @infos } }
    end
  end

  # GET /infos/1 or /infos/1.json
  def show
  end

  # GET /infos/new
  def new
    @info = Info.new
  end

  # GET /infos/1/edit
  def edit
  end

  # POST /infos or /infos.json
  def create
    @info = Info.new(info_params)

    respond_to do |format|
      if @info.save
        format.html { redirect_to info_path(@info.uid), notice: "Info was successfully created." }
        format.json { render :show, status: :created, location: info_path(@info.uid) }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @info.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /infos/1 or /infos/1.json
  def update
    respond_to do |format|
      if @info.update(info_params)
        format.html { redirect_to info_path(@info.uid), notice: "Info was successfully updated." }
        format.json { render :show, status: :ok, location: info_path(@info.uid) }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @info.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /infos/1 or /infos/1.json
  def destroy
    @info.destroy!

    respond_to do |format|
      format.html { redirect_to infos_path, status: :see_other, notice: "Info was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_info
      @info = Info.find_by!(uid: params[:uid])
    end

    # Only allow a list of trusted parameters through.
    def info_params
      params.require(:info).permit(
        :uid,
        :start_at,
        :end_at,
        :period_id,
        :category,
        :title,
        # :content,
        :image,
        title: {},
        title_img: {},
        desc: {}
      )
    end
end
