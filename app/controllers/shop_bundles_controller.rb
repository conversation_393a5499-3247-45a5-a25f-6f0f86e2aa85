class ShopBundlesController < ApplicationController
  before_action :set_shop_bundle, only: %i[ show edit update destroy ]
  before_action :authenticate_master_user!
  # GET /shop_bundles or /shop_bundles.json
  def index
    @shop_bundles = ShopBundle.includes(:period).all.order(:uid)
  end

  # GET /shop_bundles/1 or /shop_bundles/1.json
  def show
  end

  # GET /shop_bundles/new
  def new
    @shop_bundle = ShopBundle.new
  end

  # GET /shop_bundles/1/edit
  def edit
  end

  # POST /shop_bundles or /shop_bundles.json
  def create
    @shop_bundle = ShopBundle.new(shop_bundle_params)

    respond_to do |format|
      if @shop_bundle.save
        format.html { redirect_to shop_bundle_path(@shop_bundle.uid), notice: "Shop bundle was successfully created." }
        format.json { render :show, status: :created, location: shop_bundle_path(@shop_bundle.uid) }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @shop_bundle.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /shop_bundles/1 or /shop_bundles/1.json
  def update
    # "gpt"=>{"name"=>{"ja"=>"0", "en"=>"1"}, "desc"=>{"ja"=>"0", "en"=>"1"}

    respond_to do |format|
      if @shop_bundle.update(shop_bundle_params)
        format.html { redirect_to shop_bundle_path(@shop_bundle.uid), notice: "Shop bundle was successfully updated." }
        format.json { render :show, status: :ok, location: shop_bundle_path(@shop_bundle.uid) }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @shop_bundle.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /shop_bundles/1 or /shop_bundles/1.json
  def destroy
    @shop_bundle.destroy!

    respond_to do |format|
      format.html { redirect_to shop_bundles_path, status: :see_other, notice: "Shop bundle was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_shop_bundle
      @shop_bundle = ShopBundle.find_by!(uid: params[:uid])
    end

    # Only allow a list of trusted parameters through.
    def shop_bundle_params
      shop_param = params.require(:shop_bundle).permit(
        :uid,
        :rewards,
        :start_at,
        :end_at,
        :period_id,
        :cost,
        :max_count,
        name: {},
        desc: {},
        gpt: {},
        rewards: [:item_type, :count, :item_id, :ext]
      )

      shop_param[:rewards] = JSON.parse(shop_param[:rewards])

      # gptで自動翻訳チェック
      if shop_param[:gpt]
        main_lang = Lang.main_lang
        translations_needed = []

        # 必要な翻訳情報を収集
        shop_param[:gpt].each do |column, value|
          default_data = shop_param[column][main_lang.locale]
          value.each do |lang, useGpt|
            if useGpt == "1"
              translations_needed << {
                column: column,
                lang: lang,
                text: default_data
              }
            end
          end
        end

        # 翻訳が必要な場合は一括処理
        if translations_needed.any?
          chat_gpt = ChatGptService.new

          # 翻訳リクエストの説明
          prompt = "以下の各行を指定された言語に翻訳してください。\n"
          prompt += "各行は「ID,言語コード,翻訳するテキスト」の形式になっています。\n"
          prompt += "回答は「ID,翻訳結果」の形式でCSV形式で返してください。\n"
          prompt += "翻訳結果にはカンマ(,)を含めないでください。\n\n"

          # 翻訳リクエストをCSV形式で追加
          translations_needed.each_with_index do |item, index|
            prompt += "#{index+1},#{item[:lang]},#{item[:text]}\n"
          end

          # 一度だけAPIを呼び出す
          results = chat_gpt.chat(prompt, 1000)

          # 結果を解析して適用
          results.strip.split("\n").each do |line|
            parts = line.split(",", 2) # 最初のカンマだけで分割
            if parts.length == 2
              id = parts[0].to_i - 1
              translation = parts[1].strip

              if id >= 0 && id < translations_needed.length
                item = translations_needed[id]
                shop_param[item[:column]][item[:lang]] = translation
              end
            end
          end
        end

        shop_param.delete(:gpt)
      end
      return shop_param
    end
end
