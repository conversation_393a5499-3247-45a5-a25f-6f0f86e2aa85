class AccountController < ApplicationController
  before_action :authenticate_master_user! # ログイン済みマスターユーザーのみアクセス許可
  def show
    @user = current_master_user
  end

  def edit
    @user = current_master_user
  end

  def update
    @user = current_master_user
    if @user.update(account_params)
      redirect_to account_path, notice: "アカウント情報を更新しました。"
    else
      flash.now[:alert] = "更新に失敗しました。"
      render :edit
    end
  end


  private

  # 許可するパラメータを指定
  def account_params
    params.require(:master_user).permit(:email, :name, :password, :password_confirmation)
  end
end
