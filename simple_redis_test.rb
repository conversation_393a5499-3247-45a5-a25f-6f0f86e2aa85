#!/usr/bin/env ruby

puts "🧪 Simple Redis Test..."

require 'redis'
require 'connection_pool'

# Test direct Redis connection
begin
  redis = Redis.new(url: "redis://localhost:6379/1")
  result = redis.ping
  puts "✅ Direct Redis PING: #{result}"
  redis.close
rescue => e
  puts "❌ Direct Redis failed: #{e.message}"
  exit 1
end

# Test connection pool
begin
  pool = ConnectionPool.new(size: 5, timeout: 5) do
    Redis.new(url: "redis://localhost:6379/1")
  end
  
  pool.with do |redis|
    result = redis.ping
    puts "✅ Pool Redis PING: #{result}"
  end
  
  puts "✅ Redis connection pool working!"
rescue => e
  puts "❌ Redis pool failed: #{e.message}"
  exit 1
end

puts "🎉 Redis is working! The issue might be in Rails loading."
