<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>プレイヤーステータス管理</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding: 20px;
      background-color: #f8f9fa;
    }
    
    .status-container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 30px;
    }
    
    h3 {
      color: #0d6efd;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 10px;
    }
    
    h5 {
      color: #495057;
      margin-top: 20px;
      margin-bottom: 10px;
    }
    
    .stats-box {
      background-color: #e9ecef;
      border-radius: 6px;
      padding: 10px;
    }
    
    .table {
      margin-bottom: 25px;
    }
    
    .table th {
      background-color: #f8f9fa;
      font-weight: 600;
    }
    
    .ready-check {
      color: #198754;
      font-weight: bold;
    }
    
    .not-ready {
      color: #dc3545;
    }
    
    .nav-tabs {
      margin-bottom: 20px;
    }
    
    .nav-tabs .nav-link.active {
      font-weight: bold;
      border-bottom: 3px solid #0d6efd;
    }
    
    .player-info {
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <ul class="nav nav-tabs">
      <li class="nav-item">
        <a class="nav-link active" href="#">ランクマッチ</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#">フリーマッチ</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="#">ルームマッチ</a>
      </li>
    </ul>

    <div class="status-container">
      <h3 class="mb-3">ランクマッチ状況（接続済み）</h3>

      <div class="stats-box mb-3">
        <span class="me-3">総プレイヤー数: 12</span>
        <span class="me-3">マッチング中: 4</span>
        <span>対戦中: 3</span>
      </div>

      <h5>＜マッチング中のプレイヤー＞</h5>
      <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
          <tr>
            <th>ID</th><th>名前</th><th>レート</th><th>更新時間</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>101</td><td>Yuki</td><td>1520</td><td>2025-05-23 15:22</td></tr>
          <tr><td>102</td><td>Hana</td><td>1480</td><td>2025-05-23 15:18</td></tr>
        </tbody>
      </table>

      <h5>＜マッチング済みのプレイヤー＞</h5>
      <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
          <tr>
            <th>ID</th><th>名前</th><th>レート</th><th>ルームID</th><th>更新時間</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>103</td><td>Ken</td><td>1600</td><td>RM123</td><td>2025-05-23 15:20</td></tr>
          <tr><td>104</td><td>Miki</td><td>1450</td><td>RM123</td><td>2025-05-23 15:20</td></tr>
        </tbody>
      </table>
    </div>

    <div class="status-container">
      <h3 class="mb-3">フリーマッチ状況（接続済み）</h3>

      <div class="stats-box mb-3">
        <span class="me-3">総プレイヤー数: 10</span>
        <span class="me-3">マッチング中: 3</span>
        <span>対戦中: 2</span>
      </div>

      <h5>＜マッチング中のプレイヤー＞</h5>
      <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
          <tr>
            <th>ID</th><th>名前</th><th>更新時間</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>201</td><td>Rina</td><td>2025-05-23 15:10</td></tr>
          <tr><td>202</td><td>Sota</td><td>2025-05-23 15:09</td></tr>
        </tbody>
      </table>

      <h5>＜マッチング済みのプレイヤー＞</h5>
      <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
          <tr>
            <th>ID</th><th>名前</th><th>ルームID</th><th>更新時間</th>
          </tr>
        </thead>
        <tbody>
          <tr><td>203</td><td>Kai</td><td>FM888</td><td>2025-05-23 15:11</td></tr>
          <tr><td>204</td><td>Nana</td><td>FM888</td><td>2025-05-23 15:11</td></tr>
        </tbody>
      </table>
    </div>

    <div class="status-container">
      <h3 class="mb-3">ルームマッチ状況（接続済み）</h3>

      <div class="stats-box mb-3">
        <span class="me-3">総プレイヤー数: 8</span>
        <span>ルーム数: 2</span>
      </div>

      <h5>＜ルーム一覧＞</h5>
      <table class="table table-bordered table-sm table-hover">
        <thead class="table-light">
          <tr>
            <th>No.</th>
            <th>ルームID</th>
            <th>プレイヤー情報</th>
            <th>更新時間</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>RM456</td>
            <td>
              <div class="player-info">
                <strong>P1:</strong> ID: 301, 名前: Taro, 準備状態: <span class="ready-check">✅</span>
              </div>
              <div class="player-info">
                <strong>P2:</strong> ID: 302, 名前: Jiro, 準備状態: <span class="not-ready">❌</span>
              </div>
            </td>
            <td>2025-05-23 15:15</td>
          </tr>
          <tr>
            <td>2</td>
            <td>RM789</td>
            <td>
              <div class="player-info">
                <strong>P1:</strong> ID: 303, 名前: Hanako, 準備状態: <span class="ready-check">✅</span>
              </div>
              <div class="player-info">
                <strong>P2:</strong> ID: 304, 名前: Akiko, 準備状態: <span class="ready-check">✅</span>
              </div>
            </td>
            <td>2025-05-23 15:05</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
