previewsEnabled: true
previewsExpireAfterDays: 3
previewsSourceBranchFilter:
  - pattern: 'feature/*'
  - pattern: 'develop'

databases:
  - name: rails-render-demo
    databaseName: rails_render_demo
    user: rails_render_demo
    plan: basic-256mb
    region: singapore
    postgresMajorVersion: 14

  - name: rails-render-demo-secondary
    databaseName: rails_render_demo_secondary
    user: rails_render_demo_secondary
    plan: basic-256mb
    region: singapore
    postgresMajorVersion: 14

services:
  - type: web
    name: rails-render-demo
    env: ruby
    plan: starter
    region: singapore
    buildCommand: "./bin/render-build.sh"
    startCommand: "bundle exec puma -C config/puma.rb"
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: rails-render-demo
          property: connectionString

      - key: SECONDARY_DATABASE_URL
        fromDatabase:
          name: rails-render-demo-secondary
          property: connectionString
      - fromGroup: rails-render-demo_dotenv