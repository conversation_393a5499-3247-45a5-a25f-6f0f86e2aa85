#!/usr/bin/env ruby

puts "🧪 Testing Redis Connection and UserStatusService..."

# Load Rails environment
require_relative 'config/environment'

puts "\n✅ REDIS CONNECTION TEST"
puts "=" * 50

begin
  # Test Redis connection pool
  REDIS.with do |redis|
    result = redis.ping
    puts "✅ Redis PING: #{result}"
  end
  
  # Test Redis operations
  REDIS.with do |redis|
    redis.set("test_key", "test_value")
    value = redis.get("test_key")
    puts "✅ Redis SET/GET: #{value}"
    redis.del("test_key")
  end
  
  puts "✅ Redis connection pool working!"
rescue => e
  puts "❌ Redis connection failed: #{e.message}"
  exit 1
end

puts "\n✅ USERSTATUSSERVICE TEST"
puts "=" * 50

begin
  # Test UserStatusService
  result = UserStatusService.update_status("test_user_999", "matching", {
    rank: 1500,
    channel_type: "rank_matching"
  })
  
  puts "✅ Update status: #{result}"
  
  # Test get status
  status = UserStatusService.get_status("test_user_999")
  puts "✅ Get status: #{status ? 'Found' : 'Not found'}"
  
  # Test get stats
  stats = UserStatusService.get_stats
  puts "✅ Get stats: #{stats[:total]} total users"
  
  # Test get users by status
  matching_users = UserStatusService.get_users_by_status('matching')
  puts "✅ Get matching users: #{matching_users.size} users"
  
  # Cleanup
  UserStatusService.remove_status("test_user_999")
  puts "✅ Cleanup: Done"
  
  puts "✅ UserStatusService working!"
rescue => e
  puts "❌ UserStatusService failed: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(5)
  exit 1
end

puts "\n✅ CONTROLLER SIMULATION TEST"
puts "=" * 50

begin
  # Simulate controller actions
  user_ids = ["test_user_1", "test_user_2"]
  
  # Test simulate_rank_matching logic
  user_ids.each do |user_id|
    UserStatusService.update_status(user_id, "matching", {
      rank: 1500,
      timecount: 0,
      queue_size: user_ids.size,
      started_at: Time.now,
      channel_type: "rank_matching",
    })
  end
  
  puts "✅ Simulated rank matching for #{user_ids.size} users"
  
  # Test get users by channel
  rank_users = UserStatusService.get_users_by_channel_type('rank_matching')
  puts "✅ Rank users: #{rank_users.size}"
  
  # Test clear all
  active_players = REDIS.with { |redis| redis.smembers("active_players") }
  puts "✅ Active players before clear: #{active_players.size}"
  
  active_players.each do |player_id|
    UserStatusService.remove_status(player_id)
  end
  REDIS.with { |redis| redis.del("active_players") }
  
  final_stats = UserStatusService.get_stats
  puts "✅ Active players after clear: #{final_stats[:total]}"
  
  puts "✅ Controller simulation working!"
rescue => e
  puts "❌ Controller simulation failed: #{e.message}"
  puts "Backtrace:"
  puts e.backtrace.first(5)
  exit 1
end

puts "\n🎉 ALL TESTS PASSED!"
puts "Redis connection and UserStatusService are working correctly!"
puts "Buttons should now work in the browser!"
