Rails.application.routes.draw do
  # get "matches/index"
  # get "matches/show"
  # get "matches/new"
  # get "matches/create"
  # get "matches/edit"
  # get "matches/update"
  # get "matches/destroy"
  resources :matches

  resources :card_modifiers, param: :uid do
    member do
      get 'set_use'
      get 'set_unuse'
    end
  end

  # パラメータ設定
  resources :parameter_settings

  # カード表示用ルート
  get 'cards/default', to: 'cards#default', as: :cards_default
  get 'cards/modified', to: 'cards#modified', as: :cards_modified

    # 通信対戦用
  mount ActionCable.server => "/cable"

  get "cable", to: "cable#index"  # 新しいルートを追加

  resources :decks, param: :uid
  resources :shop_bundles, param: :uid
  get "user_page/new"
  get "user_page/find"
  get "user_page/findmulti"
  get "user_page/analytics"
  get "user_page/ranking"
  get "user_page/ban/:open_id" , to: 'user_page#ban', as: 'user_page_ban'
  get "user_page/score_edit/:open_id" , to: 'user_page#score_edit', as: 'user_page_score_edit'
  post "user_page/score_update/:open_id" , to: 'user_page#score_update', as: 'user_page_score_update'
  post "user_page/ban_update/:open_id" , to: 'user_page#ban_update', as: 'user_page_ban_update'
  patch "user_page/box_update/:open_id" , to: 'user_page#box_update', as: 'user_page_box_update'
  get "user_page/box/:open_id", to: 'user_page#box', as: 'user_page_box'
  get "user_page/gifts/:open_id", to: 'user_page#gifts', as: 'user_page_gifts'
  post "user_page/create"
  get "user_page/show/:open_id" , to: 'user_page#show', as: 'user_page_show'
  get "user_page/edit/:open_id" , to: 'user_page#edit', as: 'user_page_edit'
  patch "user_page/update/:open_id" , to: 'user_page#update', as: 'user_page_update'
  get "user_page/generate_transfer_code/:open_id", to: 'user_page#generate_transfer_code', as: 'user_page_generate_transfer_code'
  post "user_page/enable_account_update/:open_id", to: 'user_page#enable_account_update', as: 'user_page_enable_account_update'
  post "user_page/disable_account_update/:open_id", to: 'user_page#disable_account_update', as: 'user_page_disable_account_update'
  
  get "users/show"
  get "users/new"
  resources :infos, param: :uid
  resources :announcements, only: [:index, :new, :create]
  get "master_data/index"
  resources :master_data, only: [:show, :destroy], param: :uid do
    member do
      get :show_json
    end
    collection do
      delete 'multi_delete', to: 'master_data#multi_delete'
    end
  end
  post "master_data/create"
  get "account/show"
  get "account/edit"
  get "account/update"
  resource :account, only: [:show, :edit, :update] # 単一リソースとして扱う
  get "top/index"
  devise_for :master_users, controllers: {
    omniauth_callbacks: 'master_users/omniauth_callbacks'
  }
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/*
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  # Defines the root path route ("/")
  # root "posts#index"

  get "server_page/index"
  get "data_page/event_battle_index"
  get "data_page/gacha_index"
  get "data_page/login_bonus_index"
  get "data_page/master_data_index"
  get "data_page/shop_index"

  get "card_stats/index"
  post "card_stats/create_daily_status"

  resources :user_statuses, only: [:index] do
    collection do
      get :matching
      get :matched
      get :in_room
      get :all
      delete :remove
      get :rank_match
      get :free_match
      get :room_match
    end
  end

  namespace :user do
    get 'login', to: 'sessions#new', as: :user_login
    post 'login', to: 'sessions#create'
    get 'signup', to: 'sessions#new_signup'
    post 'signup', to: 'sessions#create_signup'
    get ':open_id', to: 'top#show', as: 'top'
    delete 'logout', to: 'user/top#destroy', as: :user_logout
    get 'logout', to: redirect('/user/login')

    get ':open_id/gacha', to: 'top#gacha', as: :gacha_show
    get ':open_id/gacha/detail/:pack_uid', to: 'gacha#detail', as: :gacha_detail
    get ':open_id/gacha_show', to: 'users#gacha_show'
    post ':open_id/gacha/pull', to: 'gacha#pull', as: :gacha_pull

    get ':open_id/charging_items', to: 'charging_items#index', as: :charging_items
    get ':open_id/charging_items/categories', to: 'charging_items#categories', as: :charging_item_categories
    post ':open_id/purchase_charging_item', to: 'charging_items#purchase', as: :purchase_charging_item
  end

  get 'user/:open_id/gifts', to: 'gifts#index', as: :user_gifts_index
  post 'user/:open_id/gifts/open', to: 'gifts#open', as: :gifts_open

  resources :charging_items, only: [:index, :new, :create, :destroy, :edit, :update], param: :uid
  resources :gifts, only: [:show, :new, :create], param: :uid
  resources :gift_masters, only: [:index, :new, :create, :destroy, :edit, :update], param: :uid
  resources :packs, only: [:index, :new, :create, :edit, :update, :destroy, :show], param: :uid do
    member do
      post :add_card_data
    end
  end
  resources :users, only: [:show], param: :open_id

  resources :langs, param: :uid
  resources :periods, param: :uid
  resources :items, param: :uid do
    collection do
      get :export
    end
  end

  root to: "top#index"

  resources :server_versions, param: :uid do
    collection do
      post 'save_data'
      post 'import_data'
      post 'back_up'
    end
    member do
      post 'select'
      get 'export_data'
    end
  end

  namespace :api do
    resources :charging_items, only: [:index], param: :uid do
      member do
        post :purchase
      end
    end

    # ユーザー関連
    post 'users/register'
    post 'users/login'
    post 'users/update_settings'
    get 'users/show'
    get 'users/maintenance'
    post 'users/get_transfer_code'
    post 'users/use_transfer_code'
    post 'users/delete_transfer_code'
    post 'users/all_update'
    post 'users/update_main_data'
    post 'users/enable_account'
    post 'users/disable_account'

    get 'other_users/get_other_user'
    get 'rankings/index'

    # お知らせ
    get 'infos/index'
    get 'infos/show'

    # ギフト
    get 'gifts/index'
    post 'gifts/gift_open'
    post 'gifts/open_all'

    # パック
    get 'packs/index_packs'
    get 'packs/index_cards'
    post 'packs/buy_pack'
    post 'packs/buy_card'

    # ログインボーナス
    get 'login_bonuses/index'
    post 'login_bonuses/event_receive'
    post 'login_bonuses/normal_receive'

    # バトルパス
    get 'battle_passes/index'
    post 'battle_passes/receive'
    post 'battle_passes/receive_all'

    # ショップ
    get 'shop/index'
    post 'shop/buy_daily_shop'
    post 'shop/update_daily_shop'
    post 'shop/buy_dia_bundle'
    post 'shop/buy_cash_bundle'
    post 'shop/buy_supply'
    post 'shop/buy_card'
    post 'shop/receive_charged_rewards'
    post 'shop/receive_consumed_rewards'
    post 'shop/buy_pass'
    post 'shop/buy_dia'

    # ショップ(課金)
    post 'shop/iap_ios'
    post 'shop/iap_android'

    # プロフィールの表示
    get 'profile/index'
    get 'profile/other_user_index'
    post 'profile/update'
    post 'profile/name_update'

    # ホーム画面
    get 'home/index'

    # 課金
    post 'iaps/check_ios'
    post 'iaps/check_android'
    post 'iaps/test_buy'
    post 'ios_connect/handle_s2s_notification' # appleのS2S通知を受け取る用でクライアントからは呼ばれない
    post 'and_connect/handle_rtdn' # googleのS2S通知を受け取る用でクライアントからは呼ばれない
    # 宝箱
    post 'chests/open'
    post 'chests/add_win'

    # デッキ
    get 'decks/index'
    post 'decks/save'

    # バトル関連
    post 'battle/add_exp'

    # スキン
    get 'skins/index'
    post 'skins/level_up'

    # お問い合わせ
    post 'contacts/create'

    # バトルレコード
    get 'battle_records/index'

  end

  resources :battle_passes, param: :uid
  resources :solo_modes, param: :uid
  resources :supplies, param: :uid
  resources :missions, param: :uid
  resources :battle_records, param: :uid
  resources :login_bonuses, param: :uid
  resource :user_ban, only: [:new, :create]

  resources :chests, param: :uid

  resources :banners, param: :uid

  resources :play_grounds, only: [:index] do
    collection do
      get 'constant'
    end
  end

end
