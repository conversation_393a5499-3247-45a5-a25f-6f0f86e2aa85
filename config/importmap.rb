# Pin npm packages by running ./bin/importmap

pin "application"
pin "@hotwired/turbo-rails", to: "turbo.min.js"
pin "@hotwired/stimulus", to: "stimulus.min.js"
pin "@hotwired/stimulus-loading", to: "stimulus-loading.js"

# Stimulusコントローラーの設定
pin_all_from "app/javascript/controllers", under: "controllers"

# Alpine.jsの設定
pin "alpinejs", to: "https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/module.esm.js"

# Alpine.jsコンポーネントを読み込む
# pin_all_from "app/javascript/alpine", under: "alpine"

pin "trix"
pin "@rails/actiontext", to: "actiontext.esm.js"

# User Status WebSocket Manager
pin "user_status_websocket", to: "user_status_websocket.js"
