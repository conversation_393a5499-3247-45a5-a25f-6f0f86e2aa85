# config/initializers/redis_cleanup.rb
# サーバー起動時にbattle_room関連のRedisデータを削除する

Rails.application.config.after_initialize do
  # begin
  #   matching_range = ENV.fetch("MATCHING_RANGE", 10).to_i
  #   Rails.logger.info(level: "info", worker: "MatchProcessWorker", message: "Using MATCHING_RANGE: #{matching_range}")
  # rescue => e
  #   Rails.logger.error "RankMatchingChannel.matching_rangeの取得中にエラーが発生しました: #{e.message}"
  # end

  # begin
  #   redis = Redis.new
  #   battle_room_keys = redis.keys("battle_room:*") + redis.keys("room_matching_channel:*") + redis.keys("matching_data:*") + redis.keys("free_matching_data:*")
  #   if battle_room_keys.any?
  #     count = battle_room_keys.size
  #     battle_room_keys.each do |key|
  #       redis.del(key)
  #     end
  #     Rails.logger.info "#{count}件のbattle_roomキーを削除しました"
  #   else
  #     Rails.logger.info "削除対象のbattle_roomキーはありませんでした"
  #   end
  # rescue => e
  #   Rails.logger.error "battle_roomキーの削除中にエラーが発生しました: #{e.message}"
  # end
end
