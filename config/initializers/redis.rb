require "redis"
require "connection_pool"

redis_config = {
  url:                ENV.fetch("REDIS_URL") { "redis://localhost:6379/1" },
  # コネクションタイムアウト設定を追加
  connect_timeout:    ENV.fetch("REDIS_CONNECT_TIMEOUT") { 3.0 }.to_f,
  read_timeout:       ENV.fetch("REDIS_READ_TIMEOUT") { 3.0 }.to_f,
  write_timeout:      ENV.fetch("REDIS_WRITE_TIMEOUT") { 3.0 }.to_f,
  # 再接続の試行回数
  reconnect_attempts: ENV.fetch("REDIS_RECONNECT_ATTEMPTS") { 10 }.to_i,
  # reconnect_delay:     ENV.fetch("REDIS_RECONNECT_DELAY") { 0.5 }.to_f,
  # reconnect_delay_max: ENV.fetch("REDIS_RECONNECT_DELAY_MAX") { 2.0 }.to_f,
}

# SSL設定を本番環境かつSSL必要な場合のみ追加
if Rails.env.production? && ENV["REDIS_USE_SSL"] == "true"
  redis_config[:ssl_params] = { verify_mode: OpenSSL::SSL::VERIFY_NONE }
end

# コネクションプールのサイズ設定
# Puma worker数 x スレッド数 + ActionCable接続数の見積もりを考慮
pool_size = ENV.fetch("REDIS_POOL_SIZE") { 60 }.to_i # 50から60に増加
pool_timeout = ENV.fetch("REDIS_POOL_TIMEOUT") { 5 }.to_i # 3から5に増加

# プール作成ヘルパー - エラーハンドリングを強化
def create_redis_pool(config, size, timeout)
  ConnectionPool.new(size: size, timeout: timeout) do
    begin
      redis = Redis.new(config)
      redis.ping # 初期接続テスト
      redis
    rescue Redis::BaseError => e
      Rails.logger.error "Error creating Redis connection: #{e.message}"
      # エラーを再度発生させて上位でハンドリング
      raise
    end
  end
end

# 環境ごとの設定
case Rails.env
when "production"
  # 本番環境用のプール
  begin
    REDIS = create_redis_pool(redis_config, pool_size, pool_timeout)
    # 直接アクセス用のクライアント（必要な場合）
    REDIS_CLIENT = Redis.new(redis_config)
  rescue Redis::BaseError => e
    Rails.logger.error "Failed to initialize Redis connection pool: #{e.message}"
    # フェイルセーフの最小プール
    REDIS = create_redis_pool(redis_config.merge(reconnect_attempts: 1), 3, 1)
    REDIS_CLIENT = Redis.new(redis_config)
  end
when "test"
  # テスト環境用のプール
  test_config = redis_config.merge(db: 2)
  REDIS = create_redis_pool(test_config, pool_size, pool_timeout)
  REDIS_CLIENT = Redis.new(test_config)
when "development"
  # 開発環境用のプール
  REDIS = create_redis_pool(redis_config, pool_size, pool_timeout)
  REDIS_CLIENT = Redis.new(redis_config)
else
  # その他の環境
  REDIS = create_redis_pool(redis_config, pool_size, pool_timeout)
  REDIS_CLIENT = Redis.new(redis_config)
end

# Redis接続確認とプール健全性チェック
begin
  # 複数の接続を同時にテストして接続プールの健全性を確認
  3.times.map do
    Thread.new do
      REDIS.with do |conn|
        conn.ping
      end
    end
  end.each(&:join)

  Rails.logger.info "Redis connection pool established with size: #{pool_size}, timeout: #{pool_timeout}s"

  # 設定値のログ出力
  Rails.logger.info "Redis configuration: " +
    "connect_timeout=#{redis_config[:connect_timeout]}s, " +
    "read_timeout=#{redis_config[:read_timeout]}s, " +
    "reconnect_attempts=#{redis_config[:reconnect_attempts]}"
rescue Redis::CannotConnectError => e
  Rails.logger.error "Failed to connect to Redis: #{e.message}"
end
