require "redlock"
require "connection_pool"

# コネクションプールのサイズと設定
pool_size = ENV.fetch("REDLOCK_POOL_SIZE") { 20 }.to_i
pool_timeout = ENV.fetch("REDLOCK_POOL_TIMEOUT") { 8 }.to_i

# Redlockの設定オプション
redlock_options = {
  retry_count:   5,
  retry_delay:   400, # ミリ秒
  retry_jitter:  100, # ミリ秒
  redis_timeout: 1.0, # 秒
}

# SSL設定を本番環境かつSSL必要な場合のみ追加
if Rails.env.production? && ENV["REDIS_USE_SSL"] == "true"
  redlock_options[:ssl] = { verify_mode: OpenSSL::SSL::VERIFY_NONE }
end

# Redlockクライアントを初期化
# 同じRedisインスタンスを使用するか、クラスター構成の場合は複数のRedisサーバーを指定します
case Rails.env
when "production"
  # 本番環境では環境変数から複数のRedisサーバーを取得する例
  redis_servers = ENV.fetch("REDLOCK_SERVERS") { ENV.fetch("REDIS_URL") { "redis://localhost:6379" } }.split(",")

  # Redlockクライアントの作成
  redlock_client = Redlock::Client.new(
    redis_servers,
    redlock_options
  )

  # Connection Pool でラップ
  REDLOCK = ConnectionPool.new(size: pool_size, timeout: pool_timeout) { redlock_client }
  # 直接アクセス用
  REDLOCK_CLIENT = redlock_client
when "test"
  # テスト環境用の設定
  redlock_client = Redlock::Client.new([ "redis://localhost:6379" ], redlock_options)
  REDLOCK = ConnectionPool.new(size: pool_size, timeout: pool_timeout) { redlock_client }
  REDLOCK_CLIENT = redlock_client
else
  # 開発環境用のデフォルト設定
  redlock_client = Redlock::Client.new([ "redis://localhost:6379" ], redlock_options)
  REDLOCK = ConnectionPool.new(size: pool_size, timeout: pool_timeout) { redlock_client }
  REDLOCK_CLIENT = redlock_client
end

Rails.logger.info "Redlock initialized with connection pool size: #{pool_size}"

# 使用例:
# REDLOCK.with do |redlock|
#   redlock.lock('resource_key', 10000) do |lock_info|
#     # クリティカルセクション
#   end
# end
