# ワーカーを1つずつ起動（Sidekiq起動時）
Rails.application.configure do
  config.after_initialize do
    if defined?(Sidekiq) && Sidekiq.server?
      Rails.logger.info "Starting match workers..."

      # フリーマッチワーカーを1つ起動
      Thread.new do
        FreeMatchWorker.new.perform
      rescue => e
        Rails.logger.error "FreeMatchWorker crashed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end

      # ランクマッチワーカーを1つ起動
      Thread.new do
        MatchProcessWorker.new.perform
      rescue => e
        Rails.logger.error "MatchProcessWorker crashed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end

      # バトルルームチェックワーカーを1つ起動
      Thread.new do
        BattleRoomCheckWorker.new.perform
      rescue => e
        Rails.logger.error "BattleRoomCheckWorker crashed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end

      Rails.logger.info "Match workers started successfully"
    end
  end
end
