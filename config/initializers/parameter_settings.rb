# アプリケーション起動時にParameterSettingが存在しなければデフォルト値で作成する
Rails.application.config.after_initialize do
  # データベースが存在しない場合（マイグレーション前など）はスキップ
  begin
    # パラメータ設定が1つも存在しない場合にデフォルト値で作成
    if ParameterSetting.count == 0
      # デフォルト値を使って新しいパラメータ設定を作成
      parameter_setting = ParameterSetting.new(settings: ConstantService.default_data)
      
      if parameter_setting.save
        Rails.logger.info "デフォルトのParameterSettingを作成しました"
      else
        Rails.logger.error "デフォルトのParameterSettingの作成に失敗しました: #{parameter_setting.errors.full_messages.join(', ')}"
      end
    end
  rescue ActiveRecord::NoDatabaseError, ActiveRecord::StatementInvalid, PG::UndefinedTable => e
    Rails.logger.warn "データベースまたはテーブルが存在しないため、ParameterSettingの初期化をスキップします: #{e.message}"
  end
end 