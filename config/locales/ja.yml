# Files in the config/locales directory are used for internationalization and
# are automatically loaded by Rails. If you want to use locales other than
# English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# To learn more about the API, please read the Rails Internationalization guide
# at https://guides.rubyonrails.org/i18n.html.
#
# Be aware that YAML interprets the following case-insensitive strings as
# booleans: `true`, `false`, `on`, `off`, `yes`, `no`. Therefore, these strings
# must be quoted to be interpreted as strings. For example:
#
#     en:
#       "yes": yup
#       enabled: "ON"

ja:
  datetime:
    distance_in_words:
      half_a_minute: 半分
      less_than_x_seconds:
        one:   1秒未満
        other: "%{count}秒未満"
      x_seconds:
        one:   1秒
        other: "%{count}秒"
      less_than_x_minutes:
        one:   1分未満
        other: "%{count}分未満"
      x_minutes:
        one:   1分
        other: "%{count}分"
      about_x_hours:
        one:   約1時間
        other: 約%{count}時間
      x_days:
        one:   1日
        other: "%{count}日"
      about_x_months:
        one:   約1ヶ月
        other: 約%{count}ヶ月
      x_months:
        one:   1ヶ月
        other: "%{count}ヶ月"
      about_x_years:
        one:   約1年
        other: 約%{count}年
      over_x_years:
        one:   1年以上
        other: "%{count}年以上"

  activerecord:
    attributes:
      gift_master:
        desc: "説明"
        item_count: "個数"  # カラム名を日本語化

    errors:
      models:
        user:
          attributes:
            name:
              too_short: "名前は%{count}文字以上で入力してください。"
              too_long: "名前は%{count}文字以下で入力してください。"
              blank: "名前を入力してください。"
              invalid: "名前は日本語、英数字、アンダースコア(_)のみ使用できます。"
              taken: "この名前は既に使用されています。"
            open_id:
              blank: "ユーザーIDの生成に失敗しました。再度お試しください。"
            password:
              blank: "パスワードを入力してください。"
              invalid: "パスワードが正しくありません。"

        gift_master:
          attributes:
            desc:
              blank: "説明を入力してください"  # presence: true に引っかかった時の個別メッセージ
            item_count:
              blank: "個数を入力してください"  # presence: true のエラーメッセージ
              greater_than: "個数は0より大きい値を入力してください"  # numericality: { greater_than: 0 }

      messages:
        blank: "を入力してください"  # 全モデル共通の“blank”メッセージ(個別設定が無い場合)
        too_short: "は%{count}文字以上で入力してください。"
        greater_than: "%{count} より大きい値を入力してください"
