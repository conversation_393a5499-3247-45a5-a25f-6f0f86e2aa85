#!/usr/bin/env ruby

puts "🧪 Testing Improved BatchUserStatusService..."

# Load the improved service
require_relative 'app/services/batch_user_status_service'

# Mock UserStatusService to track calls
class MockUserStatusService
  @@calls = []
  @@mutex = Mutex.new
  
  def self.update_status(user_id, status, metadata = {})
    @@mutex.synchronize do
      timestamp = Time.now.strftime("%H:%M:%S.%3N")
      @@calls << {
        timestamp: timestamp,
        user_id: user_id,
        status: status,
        metadata: metadata,
        thread: Thread.current.object_id
      }
      puts "  📞 UserStatusService.update_status(#{user_id}, #{status}) at #{timestamp} [Thread: #{Thread.current.object_id}]"
    end
    
    # Simulate processing time
    sleep(0.01)
  end
  
  def self.calls
    @@calls.dup
  end
  
  def self.reset_calls
    @@mutex.synchronize { @@calls = [] }
  end
end

# Mock Redis for testing
class MockRedis
  @@data = {}
  @@mutex = Mutex.new
  
  def self.current
    self
  end
  
  def self.set(key, value, **options)
    @@mutex.synchronize do
      if options[:nx] && @@data[key]
        false # Key exists, nx failed
      else
        @@data[key] = value
        true
      end
    end
  end
  
  def self.eval(script, keys:, argv:)
    @@mutex.synchronize do
      key = keys.first
      value = argv.first
      if @@data[key] == value
        @@data.delete(key)
        1
      else
        0
      end
    end
  end
  
  def self.reset
    @@mutex.synchronize { @@data = {} }
  end
end

# Replace constants for testing
Object.const_set(:UserStatusService, MockUserStatusService)
Object.const_set(:Redis, MockRedis)

puts "\n🧪 Test 1: Basic Batch Functionality"
puts "=" * 50

MockUserStatusService.reset_calls
MockRedis.reset

BatchUserStatusService.batch_update do
  BatchUserStatusService.update_status("player1", "matched", { opponent_id: "player2" })
  BatchUserStatusService.update_status("player2", "matched", { opponent_id: "player1" })
  puts "  📝 Collected 2 updates in batch"
end

calls = MockUserStatusService.calls
puts "\n📊 Results:"
puts "  Total UserStatusService calls: #{calls.size}"
puts "  Expected: 2 (one for each player)"
puts "  ✅ Test 1: #{calls.size == 2 ? 'PASSED' : 'FAILED'}"

puts "\n🧪 Test 2: Deduplication (Multiple Updates for Same User)"
puts "=" * 50

MockUserStatusService.reset_calls
MockRedis.reset

BatchUserStatusService.batch_update do
  BatchUserStatusService.update_status("player1", "matching", { queue_size: 5 })
  sleep(0.001) # Small delay to ensure different timestamps
  BatchUserStatusService.update_status("player1", "matched", { opponent_id: "player2" })
  BatchUserStatusService.update_status("player2", "matched", { opponent_id: "player1" })
  puts "  📝 Collected 3 updates (2 for player1, 1 for player2)"
end

calls = MockUserStatusService.calls
player1_calls = calls.select { |c| c[:user_id] == "player1" }
player2_calls = calls.select { |c| c[:user_id] == "player2" }

puts "\n📊 Results:"
puts "  Total UserStatusService calls: #{calls.size}"
puts "  Player1 calls: #{player1_calls.size} (should be 1 - latest status)"
puts "  Player2 calls: #{player2_calls.size} (should be 1)"
puts "  Player1 final status: #{player1_calls.first&.dig(:status)}"
puts "  ✅ Test 2: #{calls.size == 2 && player1_calls.size == 1 && player1_calls.first[:status] == 'matched' ? 'PASSED' : 'FAILED'}"

puts "\n🧪 Test 3: Concurrent Batches (Multi-threading)"
puts "=" * 50

MockUserStatusService.reset_calls
MockRedis.reset

threads = []
3.times do |i|
  threads << Thread.new do
    BatchUserStatusService.batch_update do
      BatchUserStatusService.update_status("user_#{i}_1", "matched", { thread: i })
      BatchUserStatusService.update_status("user_#{i}_2", "matched", { thread: i })
      puts "  📝 Thread #{i}: Collected 2 updates"
    end
  end
end

threads.each(&:join)

calls = MockUserStatusService.calls
puts "\n📊 Results:"
puts "  Total UserStatusService calls: #{calls.size}"
puts "  Expected: 6 (2 users × 3 threads)"
puts "  Unique users: #{calls.map { |c| c[:user_id] }.uniq.size}"
puts "  ✅ Test 3: #{calls.size == 6 ? 'PASSED' : 'FAILED'}"

puts "\n🧪 Test 4: Direct Mode (Outside Batch)"
puts "=" * 50

MockUserStatusService.reset_calls
MockRedis.reset

BatchUserStatusService.update_status("direct_user", "online", { direct: true })

calls = MockUserStatusService.calls
puts "\n📊 Results:"
puts "  Total UserStatusService calls: #{calls.size}"
puts "  Expected: 1 (direct call)"
puts "  ✅ Test 4: #{calls.size == 1 ? 'PASSED' : 'FAILED'}"

puts "\n🧪 Test 5: Nested Batch Protection"
puts "=" * 50

MockUserStatusService.reset_calls
MockRedis.reset

BatchUserStatusService.batch_update do
  BatchUserStatusService.update_status("outer_user", "matched", {})
  
  # This should not create a nested batch
  BatchUserStatusService.batch_update do
    BatchUserStatusService.update_status("inner_user", "matched", {})
  end
end

calls = MockUserStatusService.calls
puts "\n📊 Results:"
puts "  Total UserStatusService calls: #{calls.size}"
puts "  Expected: 2 (both users processed in outer batch)"
puts "  ✅ Test 5: #{calls.size == 2 ? 'PASSED' : 'FAILED'}"

puts "\n🎯 Summary of Improvements:"
puts "=" * 50
puts "✅ Thread-local storage for concurrent batch support"
puts "✅ Timestamp-based deduplication for latest updates"
puts "✅ Redis lock for cross-process synchronization"
puts "✅ Comprehensive error handling with fallbacks"
puts "✅ Multiple log levels for better debugging"
puts "✅ Nested batch protection"
puts "✅ Graceful degradation when Redis unavailable"

puts "\n🚀 Performance Benefits:"
puts "- Reduced lock contention through batching"
puts "- Smart deduplication reduces unnecessary updates"
puts "- Thread-safe concurrent batch processing"
puts "- Cross-process safety with Redis locks"
puts "- Fallback mechanisms ensure reliability"
