#!/usr/bin/env ruby

# Script to fix Redis.current to REDIS.with in UserStatusService

file_path = 'app/services/user_status_service.rb'
content = File.read(file_path)

puts "🔧 Fixing Redis.current calls in #{file_path}..."

# Count original occurrences
original_count = content.scan(/Redis\.current/).size
puts "Found #{original_count} Redis.current calls"

# Replace patterns
replacements = [
  # Simple Redis.current calls
  [/Redis\.current\.smembers\(([^)]+)\)/, 'REDIS.with { |redis| redis.smembers(\1) }'],
  [/Redis\.current\.hgetall\(([^)]+)\)/, 'REDIS.with { |redis| redis.hgetall(\1) }'],
  [/Redis\.current\.hget\(([^)]+)\)/, 'REDIS.with { |redis| redis.hget(\1) }'],
  [/Redis\.current\.del\(([^)]+)\)/, 'REDIS.with { |redis| redis.del(\1) }'],
  
  # Multi blocks
  [/Redis\.current\.multi do \|multi\|/, 'REDIS.with do |redis|\n        redis.multi do |multi|'],
  
  # Pipelined blocks
  [/Redis\.current\.pipelined do \|pipeline\|/, 'REDIS.with do |redis|\n        redis.pipelined do |pipeline|']
]

# Apply replacements
replacements.each do |pattern, replacement|
  before_count = content.scan(pattern).size
  content.gsub!(pattern, replacement)
  after_count = content.scan(pattern).size
  puts "  #{pattern} -> #{before_count - after_count} replacements"
end

# Fix multi block endings (add extra end)
content.gsub!(/^(\s+)end\n(\s+)log_debug/, "\\1  end\n\\1end\n\n\\2log_debug")
content.gsub!(/^(\s+)end\n(\s+)log_info/, "\\1  end\n\\1end\n\n\\2log_info")

# Count remaining occurrences
remaining_count = content.scan(/Redis\.current/).size
puts "Remaining Redis.current calls: #{remaining_count}"

if remaining_count == 0
  # Write back to file
  File.write(file_path, content)
  puts "✅ Successfully fixed all Redis.current calls!"
else
  puts "❌ Some Redis.current calls remain. Manual fix needed."
  
  # Show remaining lines
  content.lines.each_with_index do |line, index|
    if line.include?('Redis.current')
      puts "  Line #{index + 1}: #{line.strip}"
    end
  end
end
