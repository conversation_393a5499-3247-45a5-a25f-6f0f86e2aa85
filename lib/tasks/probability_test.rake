# lib/tasks/prob_test.rake
namespace :prob_test do
  desc "Check edition probabilities for a given pack with mass draws"
  task check_edition: :environment do
    pack_id = ENV["PACK_ID"]&.to_i || 1
    count   = ENV["COUNT"]&.to_i   || 10_000

    puts "=== Start Edition Probability Test ==="
    puts "Pack ID: #{pack_id}, Draw Count: #{count}"

    # パックをDBから取得
    pack = Pack.includes(:editions).find_by(uid: pack_id)
    unless pack
      puts "Error: Pack(uid=#{pack_id}) not found."
      exit 1
    end

    # Editions を取得
    editions = pack.editions

    if editions.blank?
      puts "Error: Pack(uid=#{pack_id}) has no editions."
      exit 1
    end

    # editions から probability の合計を算出
    total_probability = editions.sum(&:probability)
    if total_probability <= 0
      puts "Error: total_probability is 0 or negative."
      exit 1
    end

    # 結果集計用ハッシュ
    counts = Hash.new(0)

    # "count"回ループして Edition を抽選
    count.times do
      r = rand(total_probability)
      cumulative = 0.0

      editions.each do |ed|
        cumulative += ed.probability
        if r < cumulative
          counts[ed.edition_type] += 1
          break
        end
      end
    end

    # 結果の出力
    puts "=== Result ==="
    # edition_type 順に並べて表示（ソートキーが文字列なら to_i、整数ならそのまま）
    counts.sort_by { |edition_type, _| edition_type.to_i }.each do |edition_type, c|
      percentage = (c.to_f / count * 100).round(2)
      puts "EditionType=#{edition_type}: #{c}回 (#{percentage}%)"
    end
  end
end
