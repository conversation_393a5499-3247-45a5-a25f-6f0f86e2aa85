namespace :user do
  desc "既存ユーザーのレートと勝利数を初期化します"
  task initialize_rate_and_wins: :environment do
    puts "既存ユーザーのレートと勝利数を初期化しています..."
    
    count = 0
    User.find_each do |user|
      # UserScoreテーブルからレートと勝利数を取得
      rate_score = user.user_scores.find_by(category: "rate")
      win_score = user.user_scores.find_by(category: "win")
      
      # レートの設定
      if rate_score.present?
        user.update_column(:rate, rate_score.score)
      else
        user.update_column(:rate, 0)
      end
      
      # 勝利数の設定
      if win_score.present?
        user.update_column(:wins, win_score.score)
      else
        user.update_column(:wins, 0)
      end
      
      count += 1
      if count % 100 == 0
        puts "#{count}人のユーザーを処理しました"
      end
    end
    
    puts "完了しました。合計#{count}人のユーザーを処理しました。"
  end

end
