# lib/tasks/edition_test.rake

namespace :edition do
  desc "Check edition distribution for a specified pack"
  task :test_distribution, [:pack_id, :times] => :environment do |_t, args|
    # 1. 引数の準備
    pack_id = args[:pack_id] || 1
    times   = (args[:times] || 10_000).to_i  # デフォルト1万回試行

    # 2. 対象パックを取得
    pack = Pack.find_by(uid: pack_id)
    if pack.nil?
      puts "Pack with ID=#{pack_id} not found."
      exit(1)
    end

    # 3. 試行を繰り返してカウント
    results = Hash.new(0)
    times.times do
      ed_type = draw_edition(pack)
      results[ed_type] += 1
    end

    # 4. 集計・表示
    puts "----- Edition Probability Test (Pack ID: #{pack_id}) -----"
    puts "Total trials: #{times}"
    sum_count = results.values.sum

    # 各エディションごとの出現率
    results.sort_by {|k,_v| k }.each do |edition_type, count|
      percentage = (count.to_f / sum_count * 100).round(2)
      puts "Edition Type #{edition_type}: Count=#{count}, Ratio=#{percentage}%"
    end

    puts "---------------------------------------------------------"
  end

  # 実際の抽選メソッド (同じファイル内に定義)
  def draw_edition(pack)
    e1 = pack.editions.find_by(edition_type: 1)&.probability.to_f
    e2 = pack.editions.find_by(edition_type: 2)&.probability.to_f
    e3 = pack.editions.find_by(edition_type: 3)&.probability.to_f
    e4 = pack.editions.find_by(edition_type: 4)&.probability.to_f

    total_prob = e1 + e2 + e3 + e4
    rand_val = rand(total_prob)

    if rand_val < e1
      return 1
    end
    rand_val -= e1
    if rand_val < e2
      return 2
    end
    rand_val -= e2
    if rand_val < e3
      return 3
    end
    return 4
  end
end
