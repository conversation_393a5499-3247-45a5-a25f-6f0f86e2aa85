#!/usr/bin/env ruby

# Script to replace REDIS.with with redis_connection.with

file_path = 'app/services/user_status_service.rb'
content = File.read(file_path)

puts "🔧 Fixing REDIS.with calls in #{file_path}..."

# Count original occurrences
original_count = content.scan(/REDIS\.with/).size
puts "Found #{original_count} REDIS.with calls"

# Replace REDIS.with with redis_connection.with
content.gsub!(/REDIS\.with/, 'redis_connection.with')

# Count remaining occurrences
remaining_count = content.scan(/REDIS\.with/).size
puts "Remaining REDIS.with calls: #{remaining_count}"

# Write back to file
File.write(file_path, content)
puts "✅ Successfully replaced all REDIS.with calls!"

# Also fix controller
controller_path = 'app/controllers/matching_test_controller.rb'
if File.exist?(controller_path)
  controller_content = File.read(controller_path)
  controller_original = controller_content.scan(/REDIS\.with/).size
  
  if controller_original > 0
    puts "Found #{controller_original} REDIS.with calls in controller"
    controller_content.gsub!(/REDIS\.with/, 'UserStatusService.redis_connection.with')
    File.write(controller_path, controller_content)
    puts "✅ Fixed controller REDIS.with calls!"
  end
end
