require "test_helper"

class CardModifiersControllerTest < ActionDispatch::IntegrationTest
  test "should get index" do
    get card_modifiers_index_url
    assert_response :success
  end

  test "should get set_use" do
    get card_modifiers_set_use_url
    assert_response :success
  end

  test "should get set_unuse" do
    get card_modifiers_set_unuse_url
    assert_response :success
  end

  test "should get show" do
    get card_modifiers_show_url
    assert_response :success
  end

  test "should get new" do
    get card_modifiers_new_url
    assert_response :success
  end

  test "should get edit" do
    get card_modifiers_edit_url
    assert_response :success
  end

  test "should get create" do
    get card_modifiers_create_url
    assert_response :success
  end

  test "should get update" do
    get card_modifiers_update_url
    assert_response :success
  end

  test "should get destroy" do
    get card_modifiers_destroy_url
    assert_response :success
  end
end
