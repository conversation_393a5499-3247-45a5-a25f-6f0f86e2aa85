require "test_helper"

class ShopBundlesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @shop_bundle = shop_bundles(:one)
  end

  test "should get index" do
    get shop_bundles_url
    assert_response :success
  end

  test "should get new" do
    get new_shop_bundle_url
    assert_response :success
  end

  test "should create shop_bundle" do
    assert_difference("ShopBundle.count") do
      post shop_bundles_url, params: { shop_bundle: { desc: @shop_bundle.desc, end_at: @shop_bundle.end_at, name: @shop_bundle.name, period_id: @shop_bundle.period_id, rewards: @shop_bundle.rewards, start_at: @shop_bundle.start_at } }
    end

    assert_redirected_to shop_bundle_url(ShopBundle.last)
  end

  test "should show shop_bundle" do
    get shop_bundle_url(@shop_bundle)
    assert_response :success
  end

  test "should get edit" do
    get edit_shop_bundle_url(@shop_bundle)
    assert_response :success
  end

  test "should update shop_bundle" do
    patch shop_bundle_url(@shop_bundle), params: { shop_bundle: { desc: @shop_bundle.desc, end_at: @shop_bundle.end_at, name: @shop_bundle.name, period_id: @shop_bundle.period_id, rewards: @shop_bundle.rewards, start_at: @shop_bundle.start_at } }
    assert_redirected_to shop_bundle_url(@shop_bundle)
  end

  test "should destroy shop_bundle" do
    assert_difference("ShopBundle.count", -1) do
      delete shop_bundle_url(@shop_bundle)
    end

    assert_redirected_to shop_bundles_url
  end
end
