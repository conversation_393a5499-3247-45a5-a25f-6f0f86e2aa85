require "test_helper"

class UserPageControllerTest < ActionDispatch::IntegrationTest
  test "should get new" do
    get user_page_new_url
    assert_response :success
  end

  test "should get find" do
    get user_page_find_url
    assert_response :success
  end

  test "should get findmulti" do
    get user_page_findmulti_url
    assert_response :success
  end

  test "should get analytics" do
    get user_page_analytics_url
    assert_response :success
  end
end
