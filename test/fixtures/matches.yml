# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  player_1: MyString
  player_2: MyString
  result: MyString
  played_at: 2025-04-11 17:10:45
  game_mode: MyString
  event_id: 1
  matching_time_1: 1
  matching_time_2: 1
  deck_1: 
  deck_2: 
  group_1: MyString
  group_2: MyString
  rank: MyString
  before_rate_1: 1
  before_rate_2: 1
  after_rate_1: 1
  after_rate_2: 1

two:
  player_1: MyString
  player_2: MyString
  result: MyString
  played_at: 2025-04-11 17:10:45
  game_mode: MyString
  event_id: 1
  matching_time_1: 1
  matching_time_2: 1
  deck_1: 
  deck_2: 
  group_1: MyString
  group_2: MyString
  rank: MyString
  before_rate_1: 1
  before_rate_2: 1
  after_rate_1: 1
  after_rate_2: 1
