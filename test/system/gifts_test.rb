require "application_system_test_case"

class GiftsTest < ApplicationSystemTestCase
  setup do
    @gift = gifts(:one)
  end

  test "visiting the index" do
    visit gifts_url
    assert_selector "h1", text: "Gifts"
  end

  test "should create gift" do
    visit gifts_url
    click_on "New gift"

    fill_in "Created at", with: @gift.created_at
    fill_in "Desc", with: @gift.desc
    fill_in "Item count", with: @gift.item_count
    fill_in "Item kind", with: @gift.item_kind
    fill_in "Limited at", with: @gift.limited_at
    fill_in "Master", with: @gift.master_id
    fill_in "Master limit", with: @gift.master_limit
    fill_in "State", with: @gift.state
    fill_in "Updated at", with: @gift.updated_at
    fill_in "User", with: @gift.user_id
    click_on "Create Gift"

    assert_text "Gift was successfully created"
    click_on "Back"
  end

  test "should update Gift" do
    visit gift_url(@gift)
    click_on "Edit this gift", match: :first

    fill_in "Created at", with: @gift.created_at.to_s
    fill_in "Desc", with: @gift.desc
    fill_in "Item count", with: @gift.item_count
    fill_in "Item kind", with: @gift.item_kind
    fill_in "Limited at", with: @gift.limited_at.to_s
    fill_in "Master", with: @gift.master_id
    fill_in "Master limit", with: @gift.master_limit
    fill_in "State", with: @gift.state
    fill_in "Updated at", with: @gift.updated_at.to_s
    fill_in "User", with: @gift.user_id
    click_on "Update Gift"

    assert_text "Gift was successfully updated"
    click_on "Back"
  end

  test "should destroy Gift" do
    visit gift_url(@gift)
    click_on "Destroy this gift", match: :first

    assert_text "Gift was successfully destroyed"
  end
end
