require "application_system_test_case"

class ShopBundlesTest < ApplicationSystemTestCase
  setup do
    @shop_bundle = shop_bundles(:one)
  end

  test "visiting the index" do
    visit shop_bundles_url
    assert_selector "h1", text: "Shop bundles"
  end

  test "should create shop bundle" do
    visit shop_bundles_url
    click_on "New shop bundle"

    fill_in "Desc", with: @shop_bundle.desc
    fill_in "End at", with: @shop_bundle.end_at
    fill_in "Name", with: @shop_bundle.name
    fill_in "Period", with: @shop_bundle.period_id
    fill_in "Rewards", with: @shop_bundle.rewards
    fill_in "Start at", with: @shop_bundle.start_at
    click_on "Create Shop bundle"

    assert_text "Shop bundle was successfully created"
    click_on "Back"
  end

  test "should update Shop bundle" do
    visit shop_bundle_url(@shop_bundle)
    click_on "Edit this shop bundle", match: :first

    fill_in "Desc", with: @shop_bundle.desc
    fill_in "End at", with: @shop_bundle.end_at.to_s
    fill_in "Name", with: @shop_bundle.name
    fill_in "Period", with: @shop_bundle.period_id
    fill_in "Rewards", with: @shop_bundle.rewards
    fill_in "Start at", with: @shop_bundle.start_at.to_s
    click_on "Update Shop bundle"

    assert_text "Shop bundle was successfully updated"
    click_on "Back"
  end

  test "should destroy Shop bundle" do
    visit shop_bundle_url(@shop_bundle)
    click_on "Destroy this shop bundle", match: :first

    assert_text "Shop bundle was successfully destroyed"
  end
end
