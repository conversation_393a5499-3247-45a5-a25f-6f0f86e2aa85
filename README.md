
# README

このドキュメントは、このアプリケーションをセットアップし、動作させるための手順を記載しています。

---

## 必要環境

### Rubyのバージョン
- このアプリケーションでは以下のRubyバージョンを使用しています:
  - **Ruby 3.3.6**（例: `rbenv` を使用してインストールしてください）

### システム依存関係
- 以下の依存パッケージが必要です:
  - **PostgreSQL**（データベース用）

---

## 設定方法

### 1. リポジトリのクローン
```bash
git clone <リポジトリのURL>
cd <プロジェクトディレクトリ>
```

### 2. 必要なライブラリのインストール
- Bundlerを使用してGemをインストール:
  ```bash
  bundle install
  ```

### 3. データベースの設定
- データベースの作成とマイグレーションを実行:
  ```bash
  rails db:create
  rails db:migrate
  ```

---

## アプリケーションの実行

### 開発環境でサーバーを起動
以下のコマンドでアプリケーションを起動します:
```bash
rails s -b 0.0.0.0 -p 3001
```
sudo ufw status などでポートが開いているかを確認してください。
他のアプリでポートが使用されていないかも確認してください。

ブラウザで [http://160.251.14.208:3001](http://160.251.14.208:3001) にアクセスしてください。

---

## テスト

### テストスイートの実行
以下のコマンドでテストを実行できます:
```bash
rails test
```

Rspecなどを使用している場合:
```bash
bundle exec rspec
```

---

## サービス

- ジョブキュー、キャッシュサーバー、検索エンジン（必要に応じて以下を設定してください）
  - 例: Sidekiq、Redis、Elasticsearch など

---

## その他

- 必要に応じて詳細なドキュメントや補足情報を追加してください。
