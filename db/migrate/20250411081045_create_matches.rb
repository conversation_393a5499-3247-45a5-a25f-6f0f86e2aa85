class CreateMatches < ActiveRecord::Migration[7.2]
  def change
    create_table :matches do |t|
      t.string :player_1
      t.string :player_2
      t.string :reason
      t.string :result
      t.datetime :played_at
      t.string :game_mode
      t.integer :event_id
      t.integer :matching_time_1
      t.integer :matching_time_2
      t.json :deck_1
      t.json :deck_2
      t.string :group_1
      t.string :group_2
      t.string :rank
      t.integer :before_rate_1
      t.integer :before_rate_2
      t.integer :after_rate_1
      t.integer :after_rate_2

      t.timestamps
    end
  end
end
