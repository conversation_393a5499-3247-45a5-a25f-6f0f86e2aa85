class CreateCardModifiers < ActiveRecord::Migration[7.2]
  def change
    create_table :card_modifiers do |t|
      
      t.integer :uid, null: false
      t.integer :card_id
      t.integer :is_nerf
      t.string :name
      t.integer :group
      t.string :category
      t.string :role
      t.integer :cost
      t.integer :power
      t.integer :use_text0
      t.integer :use_text1
      t.integer :use_text2
      t.integer :use_script0
      t.integer :use_script1
      t.integer :use_script2
      t.integer :use_timing0
      t.integer :use_timing1
      t.integer :use_timing2
      t.string :timing0
      t.string :text0
      t.string :script0
      t.string :timing1
      t.string :text1
      t.string :script1
      t.string :timing2
      t.string :text2
      t.string :script2
      t.integer :use
      t.integer :kind
      t.integer :rarity

      t.timestamps
    end

    add_index :card_modifiers, :uid, unique: true
    add_index :card_modifiers, :card_id, unique: true
  end
end
