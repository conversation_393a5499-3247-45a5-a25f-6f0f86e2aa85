class ChangeUser < ActiveRecord::Migration[7.2]
  def change
    # カラム名の変更
    rename_column :users, :login_count, :login_days

    # カラムを追加
    add_column :users, :profile_settings, :jsonb, default: {}
    add_column :users, :user_save_data, :jsonb, default: {}

    # 不要なカラムを削除
    remove_column :users, :server_settings
    remove_column :users, :open_settings
    remove_column :users, :gacha_result
    remove_column :users, :icon
    remove_column :users, :icon_frame
    remove_column :users, :title
    remove_column :users, :login_bonuses
    remove_column :users, :infos
    remove_column :users, :solo_modes
    remove_column :users, :shops
    remove_column :users, :battle_passes


  end
end
