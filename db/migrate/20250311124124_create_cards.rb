class CreateCards < ActiveRecord::Migration[7.2]
  def change
    create_table :cards do |t|
      t.integer :uid, null: false
      t.integer :card_id, null: false
      t.string :open_id, null: false
      t.integer :normal, default: 0
      t.integer :shine, default: 0
      t.integer :premium, default: 0

      t.timestamps
    end

    add_index :cards, :uid, unique: true
    add_index :cards, [:card_id, :open_id], unique: true
    add_index :cards, :open_id
  end
end
