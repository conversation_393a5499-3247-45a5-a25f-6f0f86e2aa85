class CreateSkins < ActiveRecord::Migration[7.2]
  def change
    create_table :skins do |t|
      t.integer :uid, null: false
      t.integer :skin_id, null: false
      t.integer :skin_level, default: 0
      t.string :open_id, null: false
      t.timestamps
    end
    add_index :skins, :uid
    add_index :skins, :skin_id
    add_index :skins, :open_id
    add_index :skins, [:uid, :skin_id], unique: true
  end
end
