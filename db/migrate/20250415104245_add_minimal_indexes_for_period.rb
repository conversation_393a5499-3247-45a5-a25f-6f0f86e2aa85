class AddMinimalIndexesForPeriod < ActiveRecord::Migration[7.2]
  def change
    # 各テーブルのperiod_idカラムにインデックスを追加
    add_index :banners, :period_id unless index_exists?(:banners, :period_id)
    add_index :battle_passes, :period_id unless index_exists?(:battle_passes, :period_id)
    add_index :login_bonuses, :period_id unless index_exists?(:login_bonuses, :period_id)
    add_index :shop_bonuses, :period_id unless index_exists?(:shop_bonuses, :period_id)
    
    # 既に持っている可能性があるので確認
    add_index :cash_shop_bundles, :period_id unless index_exists?(:cash_shop_bundles, :period_id)
    
    # よく使われるテーブルの日付カラムにインデックスを追加
    add_index :supplies, :start_at unless index_exists?(:supplies, :start_at)
    add_index :supplies, :end_at unless index_exists?(:supplies, :end_at)
    add_index :banners, :start_at unless index_exists?(:banners, :start_at)
    add_index :banners, :end_at unless index_exists?(:banners, :end_at)
  end
end
