class CreateBattleRecords < ActiveRecord::Migration[7.2]
  def change
    create_table :battle_records do |t|
      t.integer :uid, null: false
      t.string :category
      t.string :user_id_1
      t.string :user_id_2
      t.datetime :date
      t.string :winner_id
      t.string :reason

      t.timestamps
    end
    add_index :battle_records, :uid, unique: true
    add_index :battle_records, :user_id_1
    add_index :battle_records, :user_id_2
  end
end
