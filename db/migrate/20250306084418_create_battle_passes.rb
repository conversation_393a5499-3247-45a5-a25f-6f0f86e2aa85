class CreateBattlePasses < ActiveRecord::Migration[7.2]
  def change
    create_table :battle_passes do |t|
      t.integer :uid, null: false
      t.jsonb :premium_rewards, default: {}
      t.jsonb :free_rewards, default: {}
      t.datetime :start_at
      t.datetime :end_at
      t.integer :period_id
      t.integer :season

      t.timestamps
    end
    add_index :battle_passes, :uid, unique: true
  end
end
