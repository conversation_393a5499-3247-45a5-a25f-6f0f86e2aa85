class CreateLoginBonuses < ActiveRecord::Migration[7.2]
  def change
    create_table :login_bonuses do |t|
      t.integer :uid, null: false
      t.jsonb :rewards, default: {}
      t.jsonb :title, default: {}
      t.jsonb :desc, default: {}
      t.datetime :start_at
      t.datetime :end_at
      t.integer :period_id

      t.timestamps
    end
    add_index :login_bonuses, :uid, unique: true
  end
end
