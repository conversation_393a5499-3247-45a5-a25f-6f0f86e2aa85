class CreateCashShopBundles < ActiveRecord::Migration[7.2]
  def change
    create_table :cash_shop_bundles do |t|
      t.integer :uid, null: false
      t.jsonb :name, default: {}
      t.jsonb :desc, default: {}
      t.jsonb :rewards, default: {}
      t.datetime :start_at
      t.datetime :end_at
      t.integer :period_id
      t.integer :cost
      t.timestamps
    end
    add_index :cash_shop_bundles, :uid, unique: true
  end
end
