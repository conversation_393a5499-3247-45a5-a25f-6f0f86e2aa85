class UpdateIap < ActiveRecord::Migration[7.2]
  def change
    remove_column :charging_items, :money_cost
    add_column :charging_items, :start_at, :datetime
    add_column :charging_items, :end_at, :datetime
    add_column :charging_items, :period_id, :integer
    add_column :charging_items, :max_count, :integer
    add_column :charging_items, :ios_key, :string
    add_column :charging_items, :android_key, :string
    add_column :charging_items, :steam_key, :string
    add_column :charging_items, :category, :string
    add_index :charging_items, :ios_key
    add_index :charging_items, :android_key
    add_index :charging_items, :steam_key
    add_index :charging_items, :category
  end
end
