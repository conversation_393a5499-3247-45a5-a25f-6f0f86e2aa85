class AddSubscriptionFieldsToIapAnd < ActiveRecord::Migration[7.2]
  def change
    add_column :iap_and, :expires_date, :datetime
    add_column :iap_and, :auto_renew, :boolean, default: false
    add_column :iap_and, :notification_type, :string
    add_column :iap_and, :is_refunded, :boolean, default: false
    add_column :iap_and, :refund_date, :datetime
    add_column :iap_ios, :expires_date, :datetime
    add_column :iap_ios, :auto_renew, :boolean, default: false
    add_column :iap_ios, :notification_type, :string
    add_column :iap_ios, :is_refunded, :boolean, default: false
    add_column :iap_ios, :refund_date, :datetime

    add_index :iap_ios, :expires_date
    add_index :iap_ios, :auto_renew
    add_index :iap_ios, :is_refunded
    add_index :iap_and, :expires_date
    add_index :iap_and, :auto_renew
    add_index :iap_and, :is_refunded
  end
end
