class CreateCardStatsDailies < ActiveRecord::Migration[7.2]
  def change
    create_table :card_stats_dailies do |t|
      t.integer :card_id
      t.integer :total_used
      t.string :game_mode
      t.string :rank
      t.date :date
      t.integer :use_count
      t.integer :use_1_count
      t.integer :use_2_count
      t.integer :use_3_count
      t.integer :use_over_3_count
      t.integer :win_count
      t.integer :lose_count
      t.integer :draw_count
      t.float :win_rate
      t.float :use_rate

      t.timestamps
    end
  end
end
