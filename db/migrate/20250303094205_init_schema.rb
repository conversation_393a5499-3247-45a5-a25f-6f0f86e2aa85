class InitSchema < ActiveRecord::Migration[7.2]
  def up
    # These are extensions that must be enabled in order to support this database
    enable_extension "plpgsql"
    create_table "active_storage_attachments" do |t|
      t.string "name", null: false
      t.string "record_type", null: false
      t.bigint "record_id", null: false
      t.bigint "blob_id", null: false
      t.datetime "created_at", null: false
      t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
      t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
    end
    create_table "active_storage_blobs" do |t|
      t.string "key", null: false
      t.string "filename", null: false
      t.string "content_type"
      t.text "metadata"
      t.string "service_name", null: false
      t.bigint "byte_size", null: false
      t.string "checksum"
      t.datetime "created_at", null: false
      t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
    end
    create_table "active_storage_variant_records" do |t|
      t.bigint "blob_id", null: false
      t.string "variation_digest", null: false
      t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
    end
    create_table "charging_items" do |t|
      t.integer "uid", null: false
      t.jsonb "name", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "rewards", default: {}
      t.integer "money_cost"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_charging_items_on_uid", unique: true
    end
    create_table "decks" do |t|
      t.integer "uid", null: false
      t.jsonb "name", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "data", default: {}
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_decks_on_uid", unique: true
    end
    create_table "editions" do |t|
      t.integer "uid"
      t.integer "pack_id", null: false
      t.integer "edition_type", default: 0
      t.decimal "probability", precision: 6, scale: 3, default: "0.0"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_editions_on_uid", unique: true
      t.index ["pack_id"], name: "index_editions_on_pack_id"
    end
    create_table "gift_masters" do |t|
      t.integer "uid", null: false
      t.jsonb "name", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "rewards", default: {}
      t.datetime "start_at"
      t.datetime "end_at"
      t.integer "period_id"
      t.string "user_id"
      t.integer "duration"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_gift_masters_on_uid", unique: true
    end
    create_table "gifts" do |t|
      t.string "user_id"
      t.integer "uid", null: false
      t.jsonb "desc", default: {}
      t.jsonb "rewards", default: {}
      t.datetime "end_at"
      t.integer "state", default: 0
      t.integer "master_id", default: -1
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_gifts_on_uid", unique: true
    end
    create_table "infos" do |t|
      t.integer "uid", null: false
      t.jsonb "title", default: {}
      t.jsonb "title_img", default: {}
      t.jsonb "desc", default: {}
      t.datetime "start_at"
      t.datetime "end_at"
      t.integer "period_id"
      t.string "category"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_infos_on_uid", unique: true
    end
    create_table "items" do |t|
      t.string "item_type", null: false
      t.integer "uid", null: false
      t.jsonb "data", default: {}, null: false
      t.jsonb "name", default: {}, null: false
      t.jsonb "desc", default: {}, null: false
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_items_on_uid", unique: true
    end
    create_table "langs" do |t|
      t.integer "uid", null: false
      t.string "name"
      t.string "locale"
      t.integer "position"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_langs_on_uid", unique: true
      t.index ["position"], name: "index_langs_on_position"
    end
    create_table "master_data" do |t|
      t.integer "uid", null: false
      t.string "title"
      t.string "desc"
      t.jsonb "content"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_master_data_on_uid", unique: true
    end
    create_table "master_users" do |t|
      t.string "email", default: "", null: false
      t.string "encrypted_password", default: "", null: false
      t.string "reset_password_token"
      t.datetime "reset_password_sent_at"
      t.datetime "remember_created_at"
      t.integer "sign_in_count", default: 0, null: false
      t.datetime "current_sign_in_at"
      t.datetime "last_sign_in_at"
      t.string "current_sign_in_ip"
      t.string "last_sign_in_ip"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.string "provider"
      t.string "open_id"
      t.index ["email"], name: "index_master_users_on_email", unique: true
      t.index ["provider", "open_id"], name: "index_master_users_on_provider_and_open_id", unique: true
      t.index ["reset_password_token"], name: "index_master_users_on_reset_password_token", unique: true
    end
    create_table "pack_rarity_probabilities" do |t|
      t.integer "uid"
      t.integer "pack_id", null: false
      t.integer "rarity"
      t.decimal "probability", precision: 6, scale: 3
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_pack_rarity_probabilities_on_uid", unique: true
      t.index ["pack_id"], name: "index_pack_rarity_probabilities_on_pack_id"
    end
    create_table "packs" do |t|
      t.integer "uid", null: false
      t.jsonb "name", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "card_data", default: {}
      t.datetime "start_at"
      t.datetime "end_at"
      t.integer "period_id"
      t.integer "cards_per_pack", default: 0
      t.integer "dia_cost", default: 0
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_packs_on_uid", unique: true
    end
    create_table "periods" do |t|
      t.integer "uid", null: false
      t.string "name"
      t.text "description"
      t.datetime "start_at"
      t.datetime "end_at"
      t.integer "parent_id"
      t.boolean "is_persistent"
      t.boolean "is_active"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["parent_id"], name: "index_periods_on_parent_id"
      t.index ["uid"], name: "index_periods_on_uid", unique: true
    end
    create_table "server_settings" do |t|
      t.integer "uid", null: false
      t.integer "version_id"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_server_settings_on_uid", unique: true
    end
    create_table "server_versions" do |t|
      t.integer "uid", null: false
      t.jsonb "title", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "data", default: {}
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_server_versions_on_uid", unique: true
    end
    create_table "shop_bundles" do |t|
      t.integer "uid", null: false
      t.jsonb "name", default: {}
      t.jsonb "desc", default: {}
      t.jsonb "rewards", default: {}
      t.datetime "start_at"
      t.datetime "end_at"
      t.integer "period_id"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.integer "cost"
      t.index ["uid"], name: "index_shop_bundles_on_uid", unique: true
      t.index ["period_id"], name: "index_shop_bundles_on_period_id"
    end
    create_table "user_logs" do |t|
      t.bigint "uuid", null: false
      t.string "action"
      t.integer "user_id"
      t.jsonb "data", default: {}
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uuid"], name: "index_user_logs_on_uuid", unique: true
    end
    create_table "user_scores" do |t|
      t.integer "uid", null: false
      t.integer "user_id"
      t.string "category", null: false
      t.integer "season"
      t.integer "score"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_user_scores_on_uid", unique: true
      t.index ["category", "score"], name: "index_user_scores_on_category_and_score"
      t.index ["user_id"], name: "index_user_scores_on_user_id"
    end
    create_table "user_tags" do |t|
      t.integer "uid", null: false
      t.integer "user_id"
      t.string "name"
      t.text "query_params"
      t.string "color"
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.index ["uid"], name: "index_user_tags_on_uid", unique: true
      t.index ["user_id"], name: "index_user_tags_on_user_id"
    end
    create_table "users" do |t|
      t.string "name"
      t.string "password_digest"
      t.integer "free_dia"
      t.integer "login_count"
      t.jsonb "gacha_result", default: []
      t.jsonb "box", default: []
      t.integer "rate"
      t.integer "icon", default: 0
      t.integer "icon_frame", default: 0
      t.integer "title", default: 0
      t.string "open_id"
      t.datetime "last_login_at"
      t.jsonb "user_settings", default: {}
      t.jsonb "server_settings", default: {}
      t.jsonb "open_settings", default: {}
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.jsonb "has_items", default: {}
      t.jsonb "login_bonuses", default: {}
      t.jsonb "infos", default: {}
      t.jsonb "solo_modes", default: {}
      t.jsonb "shops", default: {}
      t.jsonb "battle_passes", default: {}
      t.index ["open_id"], name: "index_users_on_open_id", unique: true
    end
    add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
    add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
    add_foreign_key "editions", "packs"
    add_foreign_key "pack_rarity_probabilities", "packs"
    add_foreign_key "periods", "periods", column: "parent_id"
  end

  def down
    raise ActiveRecord::IrreversibleMigration, "The initial migration is not revertable"
  end
end
