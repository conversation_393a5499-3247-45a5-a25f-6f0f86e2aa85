# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.2].define(version: 2025_05_26_094256) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "banners", force: :cascade do |t|
    t.integer "uid"
    t.string "banner_image_url"
    t.string "title"
    t.string "view_type"
    t.integer "view_uid"
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["end_at"], name: "index_banners_on_end_at"
    t.index ["period_id"], name: "index_banners_on_period_id"
    t.index ["start_at"], name: "index_banners_on_start_at"
    t.index ["uid"], name: "index_banners_on_uid"
  end

  create_table "battle_passes", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "premium_rewards", default: {}
    t.jsonb "free_rewards", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.integer "season"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["period_id"], name: "index_battle_passes_on_period_id"
    t.index ["uid"], name: "index_battle_passes_on_uid", unique: true
  end

  create_table "battle_records", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "category"
    t.string "user_id_1"
    t.string "user_id_2"
    t.datetime "date"
    t.string "winner_id"
    t.string "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_battle_records_on_uid", unique: true
    t.index ["user_id_1"], name: "index_battle_records_on_user_id_1"
    t.index ["user_id_2"], name: "index_battle_records_on_user_id_2"
  end

  create_table "card_modifiers", force: :cascade do |t|
    t.integer "uid", null: false
    t.integer "card_id"
    t.integer "is_nerf"
    t.string "name"
    t.integer "group"
    t.string "category"
    t.string "role"
    t.integer "cost"
    t.integer "power"
    t.integer "use_text0"
    t.integer "use_text1"
    t.integer "use_text2"
    t.integer "use_script0"
    t.integer "use_script1"
    t.integer "use_script2"
    t.integer "use_timing0"
    t.integer "use_timing1"
    t.integer "use_timing2"
    t.string "timing0"
    t.string "text0"
    t.string "script0"
    t.string "timing1"
    t.string "text1"
    t.string "script1"
    t.string "timing2"
    t.string "text2"
    t.string "script2"
    t.integer "use"
    t.integer "kind"
    t.integer "rarity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["card_id"], name: "index_card_modifiers_on_card_id", unique: true
    t.index ["uid"], name: "index_card_modifiers_on_uid", unique: true
  end

  create_table "card_stats_dailies", force: :cascade do |t|
    t.integer "card_id"
    t.integer "total_used"
    t.string "game_mode"
    t.string "rank"
    t.date "date"
    t.integer "use_count"
    t.integer "use_1_count"
    t.integer "use_2_count"
    t.integer "use_3_count"
    t.integer "use_over_3_count"
    t.integer "win_count"
    t.integer "lose_count"
    t.integer "draw_count"
    t.float "win_rate"
    t.float "use_rate"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "cash_shop_bundles", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "rewards", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.integer "cost"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "max_count"
    t.index ["period_id"], name: "index_cash_shop_bundles_on_period_id"
    t.index ["uid"], name: "index_cash_shop_bundles_on_uid", unique: true
  end

  create_table "charging_items", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "rewards", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.integer "max_count"
    t.string "ios_key"
    t.string "android_key"
    t.string "steam_key"
    t.string "category"
    t.index ["android_key"], name: "index_charging_items_on_android_key"
    t.index ["category"], name: "index_charging_items_on_category"
    t.index ["ios_key"], name: "index_charging_items_on_ios_key"
    t.index ["steam_key"], name: "index_charging_items_on_steam_key"
    t.index ["uid"], name: "index_charging_items_on_uid", unique: true
  end

  create_table "chests", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "category"
    t.jsonb "rewards"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_chests_on_uid", unique: true
  end

  create_table "contacts", force: :cascade do |t|
    t.string "player_id"
    t.string "app_version"
    t.string "device_model"
    t.string "os_version"
    t.string "content"
    t.string "date"
    t.string "content_detail"
    t.string "email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "decks", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_decks_on_uid", unique: true
  end

  create_table "dia_charges", force: :cascade do |t|
    t.integer "uid"
    t.integer "dia"
    t.integer "money"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.index ["uid"], name: "index_dia_charges_on_uid", unique: true
  end

  create_table "gift_masters", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "rewards", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.string "user_id"
    t.integer "duration"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_gift_masters_on_uid", unique: true
  end

  create_table "gifts", force: :cascade do |t|
    t.string "user_id"
    t.integer "uid", null: false
    t.jsonb "desc", default: {}
    t.jsonb "rewards", default: {}
    t.datetime "end_at"
    t.integer "state", default: 0
    t.integer "master_id", default: -1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "name"
    t.datetime "open_at"
    t.index ["uid"], name: "index_gifts_on_uid", unique: true
  end

  create_table "iap_and", force: :cascade do |t|
    t.string "product"
    t.datetime "buy_date"
    t.string "buy_token"
    t.string "order_id"
    t.string "open_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "expires_date"
    t.boolean "auto_renew", default: false
    t.string "notification_type"
    t.boolean "is_refunded", default: false
    t.datetime "refund_date"
    t.index ["auto_renew"], name: "index_iap_and_on_auto_renew"
    t.index ["buy_token"], name: "index_iap_and_on_buy_token"
    t.index ["expires_date"], name: "index_iap_and_on_expires_date"
    t.index ["is_refunded"], name: "index_iap_and_on_is_refunded"
    t.index ["order_id"], name: "index_iap_and_on_order_id"
  end

  create_table "iap_ios", force: :cascade do |t|
    t.integer "platform"
    t.string "product"
    t.string "open_id"
    t.string "transaction_data"
    t.datetime "buy_date"
    t.integer "quantity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "original_transaction_id"
    t.datetime "expires_date"
    t.boolean "auto_renew", default: false
    t.string "notification_type"
    t.boolean "is_refunded", default: false
    t.datetime "refund_date"
    t.index ["auto_renew"], name: "index_iap_ios_on_auto_renew"
    t.index ["expires_date"], name: "index_iap_ios_on_expires_date"
    t.index ["is_refunded"], name: "index_iap_ios_on_is_refunded"
    t.index ["original_transaction_id"], name: "index_iap_ios_on_original_transaction_id"
    t.index ["transaction_data"], name: "index_iap_ios_on_transaction_data"
  end

  create_table "infos", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "title", default: {}
    t.jsonb "title_img", default: {}
    t.jsonb "desc", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_infos_on_uid", unique: true
  end

  create_table "items", force: :cascade do |t|
    t.string "item_type", null: false
    t.integer "uid", null: false
    t.jsonb "data", default: {}, null: false
    t.jsonb "name", default: {}, null: false
    t.jsonb "desc", default: {}, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_items_on_uid", unique: true
  end

  create_table "langs", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "name"
    t.string "locale"
    t.integer "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["position"], name: "index_langs_on_position"
    t.index ["uid"], name: "index_langs_on_uid", unique: true
  end

  create_table "login_bonuses", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "rewards", default: {}
    t.jsonb "title", default: {}
    t.jsonb "desc", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_event"
    t.index ["period_id"], name: "index_login_bonuses_on_period_id"
    t.index ["uid"], name: "index_login_bonuses_on_uid", unique: true
  end

  create_table "master_data", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "title"
    t.string "desc"
    t.jsonb "content"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_master_data_on_uid", unique: true
  end

  create_table "master_users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "provider"
    t.string "open_id"
    t.index ["email"], name: "index_master_users_on_email", unique: true
    t.index ["provider", "open_id"], name: "index_master_users_on_provider_and_open_id", unique: true
    t.index ["reset_password_token"], name: "index_master_users_on_reset_password_token", unique: true
  end

  create_table "matches", force: :cascade do |t|
    t.string "player_1"
    t.string "player_0"
    t.string "reason"
    t.string "result"
    t.datetime "played_at"
    t.string "game_mode"
    t.integer "event_id"
    t.integer "matching_time_1"
    t.integer "matching_time_0"
    t.json "deck_1"
    t.json "deck_0"
    t.string "group_1"
    t.string "group_0"
    t.string "rank"
    t.integer "before_rate_1"
    t.integer "before_rate_0"
    t.integer "after_rate_1"
    t.integer "after_rate_0"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "replay_data"
    t.jsonb "other_data"
    t.index ["created_at"], name: "index_matches_on_created_at"
    t.index ["player_0"], name: "index_matches_on_player_0"
    t.index ["player_1"], name: "index_matches_on_player_1"
  end

  create_table "missions", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "category"
    t.jsonb "rewards", default: {}
    t.jsonb "achieve_cond", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_missions_on_uid", unique: true
  end

  create_table "packs", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "card_data", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.integer "cards_per_pack", default: 0
    t.integer "dia_cost", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "skin_data", default: []
    t.decimal "per_skin_prob", precision: 6, scale: 3, default: "0.0"
    t.jsonb "rarity_prob"
    t.jsonb "edition_prob"
    t.jsonb "change_pts"
    t.boolean "is_special", default: false
    t.boolean "is_visible", default: true
    t.boolean "enable_dia", default: true
    t.boolean "enable_ticket", default: true
    t.boolean "is_main", default: false
    t.jsonb "main_card_id", default: []
    t.boolean "enable_pts", default: true
    t.integer "sort_order"
    t.index ["uid"], name: "index_packs_on_uid", unique: true
  end

  create_table "parameter_settings", force: :cascade do |t|
    t.text "settings", default: "{}"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "periods", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "name"
    t.text "description"
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "parent_id"
    t.boolean "is_persistent"
    t.boolean "is_active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_id"], name: "index_periods_on_parent_id"
    t.index ["uid"], name: "index_periods_on_uid", unique: true
  end

  create_table "server_settings", force: :cascade do |t|
    t.integer "uid", null: false
    t.integer "version_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_server_settings_on_uid", unique: true
  end

  create_table "server_versions", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "title", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_server_versions_on_uid", unique: true
  end

  create_table "shop_bonuses", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "uid"
    t.jsonb "rewards", default: {}
    t.string "category"
    t.integer "need_dia"
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.index ["period_id"], name: "index_shop_bonuses_on_period_id"
    t.index ["uid"], name: "index_shop_bonuses_on_uid", unique: true
  end

  create_table "shop_bundles", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "name", default: {}
    t.jsonb "desc", default: {}
    t.jsonb "rewards", default: {}
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "cost"
    t.integer "max_count"
    t.index ["period_id"], name: "index_shop_bundles_on_period_id"
    t.index ["uid"], name: "index_shop_bundles_on_uid", unique: true
  end

  create_table "shop_passes", force: :cascade do |t|
    t.integer "uid"
    t.jsonb "name"
    t.jsonb "desc"
    t.integer "cost"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "category"
    t.index ["uid"], name: "index_shop_passes_on_uid", unique: true
  end

  create_table "solo_modes", force: :cascade do |t|
    t.integer "uid", null: false
    t.integer "level"
    t.jsonb "missions", default: {}
    t.jsonb "title", default: {}
    t.jsonb "enemy_decks", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_solo_modes_on_uid", unique: true
  end

  create_table "supplies", force: :cascade do |t|
    t.integer "uid", null: false
    t.jsonb "rewards", default: {}
    t.integer "dia_cost"
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "start_at"
    t.datetime "end_at"
    t.integer "period_id"
    t.index ["end_at"], name: "index_supplies_on_end_at"
    t.index ["start_at"], name: "index_supplies_on_start_at"
    t.index ["uid"], name: "index_supplies_on_uid", unique: true
  end

  create_table "user_bans", force: :cascade do |t|
    t.string "open_id"
    t.datetime "ban_end_at"
    t.string "reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "user_logs", force: :cascade do |t|
    t.bigint "uuid", null: false
    t.string "action"
    t.string "user_id"
    t.jsonb "data", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uuid"], name: "index_user_logs_on_uuid", unique: true
  end

  create_table "user_scores", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "user_id"
    t.string "category", null: false
    t.integer "season"
    t.integer "score"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category", "score"], name: "index_user_scores_on_category_and_score"
    t.index ["uid"], name: "index_user_scores_on_uid", unique: true
    t.index ["user_id"], name: "index_user_scores_on_user_id"
  end

  create_table "user_tags", force: :cascade do |t|
    t.integer "uid", null: false
    t.string "user_id"
    t.string "name"
    t.text "query_params"
    t.string "color"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_user_tags_on_uid", unique: true
    t.index ["user_id"], name: "index_user_tags_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "name"
    t.string "password_digest"
    t.integer "free_dia"
    t.integer "login_days"
    t.string "open_id"
    t.datetime "last_login_at"
    t.string "user_settings", default: ""
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "has_items", default: {}
    t.jsonb "profile_settings", default: {}
    t.jsonb "user_save_data", default: {}
    t.integer "ios_dia"
    t.integer "android_dia"
    t.integer "steam_dia"
    t.string "tutorial_status"
    t.integer "guild_id"
    t.integer "exp", default: 0
    t.jsonb "box", default: {}, null: false
    t.jsonb "skins_box", default: {}, null: false
    t.integer "rate", default: 0
    t.integer "wins", default: 0
    t.datetime "ban_end_at"
    t.boolean "enable_account", default: true
    t.index ["open_id"], name: "index_users_on_open_id", unique: true
    t.index ["rate"], name: "index_users_on_rate"
    t.index ["wins"], name: "index_users_on_wins"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "periods", "periods", column: "parent_id"
end
