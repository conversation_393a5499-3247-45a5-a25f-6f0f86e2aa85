GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    brakeman (7.0.0)
      racc
    builder (3.3.0)
    byebug (11.1.3)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.0)
    crass (1.0.6)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    drb (2.2.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    faraday (2.12.2)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.1-aarch64-linux-gnu)
    ffi (1.17.1-aarch64-linux-musl)
    ffi (1.17.1-arm-linux-gnu)
    ffi (1.17.1-arm-linux-musl)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86_64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    ffi (1.17.1-x86_64-linux-musl)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-androidpublisher_v3 (0.79.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (0.17.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 1.9)
      httpclient (>= 2.8.3, < 3.a)
      mini_mime (~> 1.0)
      mutex_m
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
    google-cloud-env (2.3.0)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-logging-utils (0.2.0)
    googleauth (1.14.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hashdiff (1.1.2)
    hashie (5.0.0)
    httpclient (2.9.0)
      mutex_m
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.10.1)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    logger (1.6.6)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.4)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.3.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.2-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.2-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.2-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.2-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    parallel (1.26.3)
    parser (3.3.7.1)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.3)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.13)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-ujs (0.1.0)
      railties (>= 3.1)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rbs (3.9.2)
      logger
    rdoc (6.13.1)
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redlock (2.0.6)
      redis-client (>= 0.14.1, < 1.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.0)
    rubocop (1.71.2)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.38.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.38.0)
      parser (>= 3.3.1.0)
    rubocop-minitest (0.36.0)
      rubocop (>= 1.61, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-performance (1.23.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.29.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails-omakase (1.0.0)
      rubocop
      rubocop-minitest
      rubocop-performance
      rubocop-rails
    ruby-lsp (0.23.14)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 4)
      sorbet-runtime (>= 0.5.10782)
    ruby-openai (7.4.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    rubyzip (2.4.1)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    securerandom (0.4.1)
    select2-rails (4.0.13)
    selenium-webdriver (4.28.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sidekiq (7.2.4)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.19.0)
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    signet (0.20.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simple_calendar (3.1.0)
      rails (>= 6.1)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    sorbet-runtime (0.5.12017)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    squasher (0.8.0)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.2)
    thor (1.3.2)
    tilt (2.6.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (2.0.11)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.2)
    useragent (0.16.11)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.9.1)
    websocket (1.2.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.1)

PLATFORMS
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin
  x86_64-darwin
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bcrypt (~> 3.1.7)
  binding_of_caller
  bootsnap
  brakeman
  byebug
  capybara
  debug
  devise
  google-apis-androidpublisher_v3
  googleauth
  hashdiff
  image_processing (~> 1.2)
  importmap-rails
  jbuilder
  kaminari
  omniauth-google-oauth2
  omniauth-rails_csrf_protection
  pg (~> 1.1)
  puma (>= 5.0)
  rails (~> 7.2.2, >= *******)
  rails-ujs
  rdoc (~> 6.13.1)
  redis
  redlock
  reverse_markdown
  rubocop
  rubocop-performance
  rubocop-rails
  rubocop-rails-omakase
  ruby-lsp
  ruby-openai
  select2-rails
  selenium-webdriver
  sidekiq (~> 7.2.4)
  sidekiq-scheduler
  simple_calendar
  sprockets-rails
  squasher
  stimulus-rails
  turbo-rails
  tzinfo-data
  web-console

BUNDLED WITH
   2.6.2
