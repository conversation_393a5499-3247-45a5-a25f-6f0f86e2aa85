# =============================================
# RuboCop 設定ファイル（Rails向け Omakase スタイル＋拡張ルール）
# =============================================

# Omakase の推奨設定をベースに継承
inherit_gem:
  rubocop-rails-omakase: rubocop.yml

# =============================================
# インデントルール
# =============================================

# スペース or タブでインデントを統一する
Layout/IndentationStyle:
  Enabled: true
  EnforcedStyle: spaces         # 他の選択肢: tabs（ただし Ruby界では spaces が推奨）

# インデント幅を 2スペースに固定（Rubyの一般慣習）
Layout/IndentationWidth:
  Enabled: true
  Width: 2

# タブとスペースの混在を禁止
Layout/IndentationConsistency:
  Enabled: true

# case 式の when 節を case に揃える
Layout/CaseIndentation:
  Enabled: true
  EnforcedStyle: case           # 他の選択肢: end（end に揃える）

# `def` の位置に `end` を揃える
Layout/DefEndAlignment:
  Enabled: true
  EnforcedStyleAlignWith: start_of_line  # 他の選択肢: def

# `do`, `if` などのキーワードに `end` を揃える
Layout/EndAlignment:
  Enabled: true
  EnforcedStyleAlignWith: keyword        # 他の選択肢: start_of_line, variable

# =============================================
# メソッド引数・呼び出し
# =============================================

# 複数行引数のとき、2スペースインデント
Layout/ArgumentAlignment:
  Enabled: true
  EnforcedStyle: with_fixed_indentation  # 他の選択肢: with_first_argument, fixed_indentation

# 最初の引数も必ず改行する
Layout/FirstMethodArgumentLineBreak:
  Enabled: false
  EnforcedStyle: consistent              # 他の選択肢: consistent, never

# メソッド呼び出しの引数の改行スタイル
Layout/MultilineMethodCallIndentation:
  Enabled: true
  EnforcedStyle: indented                # 他の選択肢: aligned, indented_relative_to_receiver

# 演算子（+ や &&）がある場合の折り返しインデント
Layout/MultilineOperationIndentation:
  Enabled: true
  EnforcedStyle: indented                # 他の選択肢: aligned, indented_relative_to_binary_operator

# 1行あたりの最大文字数
Layout/LineLength:
  Enabled: false
  Max: 100                               # 100文字以上で折り返しを推奨

Layout/ExtraSpacing:
  Enabled: true
  AllowForAlignment: false  # ← false にすると無駄なスペースを削除してくれる


# =============================================
# ハッシュリテラルの整形
# =============================================

# ハッシュの key: value を縦に整列
Layout/HashAlignment:
  Enabled: true
  EnforcedColonStyle: table             # 他の選択肢: key, separator
  EnforcedHashRocketStyle: table        # => を使う場合の整形方法（table が縦揃え）

# 複数行のハッシュでは key ごとに改行する
Layout/MultilineHashKeyLineBreaks:
  Enabled: true

# 複数行ハッシュで末尾にカンマを付ける
Style/TrailingCommaInHashLiteral:
  Enabled: true
  EnforcedStyleForMultiline: consistent_comma   # 他の選択肢: no_comma, comma

# =============================================
# 括弧のスタイル
# =============================================

# 閉じカッコ（`)`）の位置をインデントに合わせる
Layout/ClosingParenthesisIndentation:
  Enabled: true
  EnforcedStyle: consistent               # 他の選択肢: align_parentheses, align_with_start_of_line

# =============================================
# Ruby スタイルルール（お好みで）
# =============================================

# 明示的な `return` を不要にする（最後の式が返るため）
Style/RedundantReturn:
  Enabled: true
